class SetBarPlugin{

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { actions, config }) {
        config.add('workbench.setbar.items', {
            label: 'nodeSchema',
            name: 'nodeSchema'
        });

        config.add('workbench.setbar.items', {
            label: 'optionsSchema',
            name: 'optionsSchema'
        });
    }
}

export default SetBarPlugin;