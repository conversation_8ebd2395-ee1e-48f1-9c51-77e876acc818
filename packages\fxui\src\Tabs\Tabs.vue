<script>
import { Vue } from '@beecraft/shared';

const FxTabs = {
    extends: Vue.options.components.FxTabs,
    methods: {
        /**
         * FxTabs通过$slots获取FxTabPane的引用，NodeElement改变了这个结构，所以需要重写获取的逻辑
         */
        calcPaneInstances(isForceUpdate = false) {
            if (this.$slots.default) {
                const paneSlots = this.$slots.default.reduce((memo, vnode) => {
                    let instance = vnode.componentInstance;

                    while (instance) {
                        const vnode = instance.$vnode;

                        if (vnode && vnode.tag && vnode.componentOptions && vnode.componentOptions.Ctor.options.name === 'FxTabPane') {
                            memo.push(vnode);
                            break;
                        }

                        instance = instance.$children[0]; // 此处完全利用了Node渲染的结构，如果结构变更，此处也需要修改
                    }

                    return memo;
                }, []);
                // update indeed
                const panes = paneSlots.map(({ componentInstance }) => componentInstance).filter(item => item.hidden !== true);
                const panesChanged = !(panes.length === this.panes.length && panes.every((pane, index) => pane === this.panes[index]));
                if (isForceUpdate || panesChanged) {
                    this.panes = panes;
                }
            } else if (this.panes.length !== 0) {
                this.panes = [];
            }
        }
    }
}

FxTabs.beecraft = function () {
    const template = {
        name: 'FxTabs',
        displayName: $t('beecraft.fxui.tabContainer', {}, '页签容器'),
        data: {
            value: 'tabItem1'
        },
        children: [{
            name: 'FxTabPane',
            data: {
                "label": $t('beecraft.fxui.tags', { num: 1 }, '标签{{num}}'),
                "name": "tabItem1"
            }
        }, {
            name: 'FxTabPane',
            data: {
                "label": $t('beecraft.fxui.tags', { num: 2 }, '标签{{num}}'),
                "name": "tabItem2"
            }
        }]
    };

    return {
        name: 'FxTabs',
        displayName: $t('beecraft.fxui.tabContainer', {}, '页签容器'),
        data: {
            type: 'normal'
        },
        $$data: {
            template,
            noWrapper: true // 不需要包装器
        },
        related: {
            attributeSettings: [{
                name: 'SetterField',
                data: {
                    label: $t('beecraft.fxui.tab', {}, '选项卡'),
                    display: 'block',
                    setter: {
                        component: 'ArraySetter',
                        data: {
                            ranges: ['data', 'label']
                        }
                    }
                }
            }, {
                name: 'SetterField',
                data: {
                    label: $t('beecraft.common.form', {}, '形态'),
                    display: 'block',
                    setter: {
                        component: 'RadioSetter',
                        ranges: ['data', 'type'],
                        data: {
                            options: [{
                                value: 'normal',
                                label: $t('普通')
                            }, {
                                value: 'border-card',
                                label: $t('beecraft.common.package', {}, '包裹')
                            }, {
                                value: 'card',
                                label: $t('beecraft.common.capsule', {}, '胶囊')
                            }]
                        }
                    }
                }
            }]
        },
        rules: {
            canMoveIn: (nodes) => (!nodes.find(node => node.name !== 'FxTabPane'))
        }
    }
}

export default FxTabs;
</script>
<style lang="less">
.bc-node.el-tabs {
    background-color: #fff;

    .el-tabs__header {
        margin: 0;
    }

    .el-tabs__content {
        padding: 0;
    }
}

// 固定高度的样式处理
.bc-node.el-tabs.component-h-100 {
    display: flex;
    flex-direction: column;

    &>.el-tabs__content {
        flex: 1;

        &>.el-tab-pane {
            height: 100%;
            overflow-y: scroll;
        }
    }
}
</style>