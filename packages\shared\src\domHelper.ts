export function getCssVariable(element, variableName) {
    const computedStyle = window.getComputedStyle(element);
    // 使用getPropertyValue获取CSS变量的值
    return computedStyle.getPropertyValue(`--${variableName}`).trim();
}

export function observeDom(elem, callback) {
    const ro = new ResizeObserver(entries => {
        for (const entry of entries) {
            callback(entry);
        }
    });
    ro.observe(elem);
    return ro;
}

export function observeDomRect(elem, prop, callback) {
    let prev = 0;
    return observeDom(elem, function(entry) {
        if(prev !== entry.contentRect[prop]) {
            callback(entry.contentRect[prop], prev, entry);
            prev = entry.contentRect[prop];
        }
    });
}