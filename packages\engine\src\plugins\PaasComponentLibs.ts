/**
 * @module engine/plugins/PaasComponentLibs
 * @desc PAAS组件库，安装后可直接使用纷享PAAS组件
 */

// 加载PAAS组件，并同步替换beecraft函数
export function loadCtorAndSyncBeecraft(node) {
    return new Promise(resolve => {
        const CtorOrAsyncCtor = (<any>window).Cmpt.get_paas(node.name);

        if (CtorOrAsyncCtor) {
            const { beecraft } = CtorOrAsyncCtor;

            if (beecraft) {
                const bcRet = beecraft();

                if (bcRet instanceof Promise) {
                    bcRet.then(res => {
                        // 替换真正的beecraft函数
                        CtorOrAsyncCtor.beecraft = res.default;
                        resolve(CtorOrAsyncCtor);
                    });
                    return;
                }
            }

            resolve(CtorOrAsyncCtor);
            return;
        }

        resolve(null);
    })
}

export function loadCtors(node) {
    return new Promise(resolve => {
        const collection: any = {};
        const requireHandles: any = [];
        // 加载主组件
        loadCtorAndSyncBeecraft(node).then((Ctor: any) => {
            let ignore: any = {
                [node.name]: true
            };
            collection[node.name] = Ctor;

            // 加载模板中所有的组件
            if (Ctor?.beecraft) {
                const widgetSchema = Ctor.beecraft();

                if (widgetSchema.$$data?.template) {
                    widgetSchema.$$data.template.children.reduce((memo, item) => {
                        if (ignore[item.name]) {
                            return memo;
                        }
                        ignore[item.name] = true;

                        memo.push(loadCtorAndSyncBeecraft(item).then((res: any) => {
                            collection[item.name] = res;
                        }));
                        return memo;
                    }, requireHandles);
                }
            }
            Promise.all(requireHandles).then(() => {
                resolve(collection);
            });
        });
    })
}

export default class PaasComponentLibs {
    apply() {
        return [
            {
                event: 'beecraft.init.before',
                functional: this.initBefore.bind(this),
            },
        ];
    }

    initBefore(service, { config, options }) {
        const Cmpt = (<any>window).Cmpt;

        let requireHandles: any[] = [];

        let startTime = Date.now();

        // 预处理物料列表
        if (options?.workbench?.materials) {
            options?.workbench?.materials.forEach((item) => {
                item.children.forEach((item) => {
                    if (
                        item.source === 'paas' &&
                        !options.resolver[item.name] &&
                        !config.get(`resolver.${item.name}`)
                    ) {
                        const asyncLoad = loadCtors(item).then((Ctors: any) => {
                            Object.keys(Ctors).forEach(key => {
                                config.set(`resolver.${key}`, Ctors[key]);
                            });
                        });

                        requireHandles.push(asyncLoad);
                    }
                });
            });
        }

        // 渲染引擎使用
        config.add('normalizeNodes', function (node, { query, actions }) {
            // resolver可以找到对应的组件
            if(node._done) {
                return;
            }
            if (!node.source || node.source === 'paas') {
                const { resolver } = query.getOptions();

                let require: Promise<void>;
                if (resolver[node.name]) {
                    require = Promise.resolve(resolver[node.name]);
                } else {
                    require = new Promise((resolve, reject) => {
                        loadCtorAndSyncBeecraft(node).then((Ctor: any) => {
                            if (Ctor) {
                                actions.history.ignore().setOptions(({ resolver }) => {
                                    resolver[node.name] = Ctor;
                                });
                                resolve(Ctor);
                            } else {
                                reject();
                            }
                        }, reject);
                    })
                }

                require.then((Ctor) => {
                    actions.history.ignore().setState((state) => {
                        const nNode = query.parseSerializedNode(node, { [node.name]: Ctor }).toNode();
                        const { parent, children } = node;

                        var oNode = state.nodes[node.id]; // 过度状态的节点
                        if (oNode) {
                            Object.assign(oNode, nNode, { parent, children });
                        } else {
                            Object.assign(node, nNode, { parent, children });
                        }
                    });
                });
            }
        });

        return Promise.all(requireHandles).then(res => {
            console.log('paas组件库加载完成', Date.now() - startTime);
            return res;
        });
    }
}
