// 创建节点时用的模板
const template = {
    name: 'Demo'
}

export default function(){
    return {
        type: 'Demo',
        name: $t('测试'),
        data: {
            text: ''
        },
        $$data: {
            isCanvas: true,
            template,
            title: $t('示例组件')
        },
        related: {
            wrapperBars: [{
                name: 'test1',
                data: {
                    icon: 'icon-zujian',
                    description: "组件",
                },
                onClick() {
                    console.log(arguments);
                }
            },{
                name: 'test2',
                data: {
                    icon: 'icon-zujian',
                    description: "组件",
                },
                onClick() {
                    console.log(arguments);
                }
            }]
        },
        hooks: {
            created(node, { query, options }) {
                node.data.aaa = 123;
            }
        }
    }
}