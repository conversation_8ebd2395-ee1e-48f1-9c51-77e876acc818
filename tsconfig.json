{"compilerOptions": {"baseUrl": ".", "target": "ES2015", "lib": ["dom", "dom.iterable", "esnext", "scripthost"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "importHelpers": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "preserve", "noImplicitThis": false, "noImplicitAny": false, "paths": {"@beecraft/*": ["packages/*/src"]}}, "exclude": ["node_modules"], "include": ["packages", "examples", "typings"]}