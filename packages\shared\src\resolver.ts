import Vue from 'vue';

const loadingMap = new Map();

class ResolverLoader {
    resolver;
    compLoader;
    options;
    loadingMap;

    constructor(options) {
        this.options = options;
        this.compLoader = options.compLoader;
        this.resolver = new Map();
    }

    async load(name, source) {
        if(this.compLoader[source]) {
            await this._loadComponentAndBeecraft(name, source);
        }
        return Object.fromEntries(this.resolver);
    }

    /**
     * 加载模板中的组件
     * @param {Object} template - 模板对象
     * @param {string} source - 组件来源
     * @returns {Promise<void[]>}
     */
    async _loadComponentsByTemplate(template, source) {
        const promises = new Map();

        const traverse = (node) => {
            if (!node?.children) {
                return;
            }

            for (const child of node.children) {
                if (promises.has(child.name)) {
                    continue;
                }
                const promise = this._loadComponentAndBeecraft(child.name, child.source || source);
                promises.set(child.name, promise);
                traverse(child);
            }
        };

        traverse(template);
        return Promise.all([...promises.values()]);
    }

    /**
     * 加载组件和对应的 beecraft
     * @param {string} name - 组件名称
     * @param {string} source - 组件来源
     * @returns {Promise<void>}
     */
    async _loadComponentAndBeecraft(name, source) {
        const CtorOrAsync = this.compLoader[source](name);
        let beecraft, CtorExport;
        try {
            [ beecraft, CtorExport ] = await Promise.all([
                this._loadBeecraft(CtorOrAsync, source, name),
                this._loadComponent(CtorOrAsync, name, source)
            ]);

            CtorExport = CtorExport.default ?? CtorExport;
            const Ctor = typeof CtorExport === 'function' ? CtorExport : Vue.extend(CtorExport);
            Ctor.beecraft = beecraft;
            this.resolver.set(name, Ctor);
        } catch (error) {
            const EmptyComponent = Vue.extend({ render: h => h('div', { class: 'd-flex justify-content-center align-items-center', style: { height: '200px' } }, `组件未找到: ${name}`) });
            // @ts-ignore
            EmptyComponent.beecraft = beecraft;
            this.resolver.set(name, EmptyComponent);
        }
    }

    /**
     * 加载组件
     * @param {*} ctorOrAsync - 组件构造函数或异步函数
     * @returns {Promise<*>}
     */
    async _loadComponent(ctorOrAsync, name, source) {
        if (typeof ctorOrAsync !== 'function') {
            // 是VueOptions的情况
            return ctorOrAsync;
        }

        // 是Vue.extend() 返回的构造函数
        if (ctorOrAsync.super === Vue) {
            return ctorOrAsync;
        }

        // 是异步组件
        const key = `${source}.${name}`;
        if(loadingMap.has(key)) {
            return loadingMap.get(key);
        }

        loadingMap.set(key, ctorOrAsync());

        return loadingMap.get(key).then(res => {
            loadingMap.delete(key);
            return res;
        });
    }

    /**
     * 加载 beecraft
     * @param {Object} ctorOrAsync - 包含 beecraft 方法的组件对象
     * @param {string} source - 组件来源
     * @returns {Promise<*>}
     */
    async _loadBeecraft(ctorOrAsync, source, name) {
        let { beecraft } = ctorOrAsync;

        if(!beecraft) {
            beecraft = (await this._loadComponent(ctorOrAsync, name, source)).beecraft;
        }

        // 可能存在不同模板的情况，届时需要将options作为参数传进去
        const result = beecraft(this.options);

        if (!(result instanceof Promise)) {
            if (result?.$$data?.template) {
                await this._loadComponentsByTemplate(result.$$data.template, source);
            }
            return beecraft;
        }

        const { default: defaultBeecraft } = await result;
        const beeData = defaultBeecraft(this.options);

        if (beeData?.$$data?.template) {
            await this._loadComponentsByTemplate(beeData.$$data.template, source);
        }

        return defaultBeecraft;
    }
}

/**
 * 加载解析器
 * @param {string} name - 组件名称
 * @param {string} Ctor - 构造函数
 * @param {Object} compLoader - 组件加载器
 * @returns {Promise<Map>}
 */
export const loadResolver = (name, source = 'paas', options) => {
    return new ResolverLoader(options).load(name, source);
};