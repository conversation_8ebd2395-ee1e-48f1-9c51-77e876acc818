import Vue from 'vue';
import * as fxui from '@beecraft/fxui';
import * as materials from '@beecraft/materials';

function isGridLayout(name) {
    return name === 'GridLayout' || name === 'SlideGridLayout';
}

/**
 * @module engine/plugins/BuiltinInitEditor
 * @desc 设计器初始化
 */
export default class BuiltinInitEditor {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        config.set('resolver', {
            ...(options.noUseFxUI ? {} : fxui),
            ...materials,
        });
        config.set('mediator', new Vue());

        // 默认标准样式
        if (options.isUseDefaultStyle) {
            config.set('node.$$data.style', function (node, { query }) {
                // 根组件样式处理
                // 无外边距，只有没边距
                if (!node.parent) {
                    if(isGridLayout(node.name)){
                        return {
                            'background-color': '#FAFAFA',
                            'box-sizing': 'border-box',
                            'border': '0.5px solid transparent'
                        }
                    }
                    return {
                        'padding': '0 8px',
                        'background-color': '#FAFAFA',
                        'height': '100%',
                        'box-sizing': 'border-box',
                        'border': '0.5px solid transparent'
                    }
                }

                const parentNode = query.node(node.parent).get();

                if (isGridLayout(parentNode.name)) {
                    return {
                        'padding': '16px',
                        'background': '#fff'
                    }
                }

                // 非根容器组件处理
                // 如果是三级及以下，则有外边距和内边距；如果是二级，则只有内边距
                if (node.$$data.isCanvas) {
                    if (!parentNode.parent) {
                        return {
                            'margin': '8px 0',
                            'padding': '16px',
                            'background': '#fff'
                        }
                    } else {
                        return {
                            'padding': '16px',
                            'background': '#fff'
                        }
                    }
                }

                // todo 暂定template为父容器的象征
                if (node.$$data.template) {
                    return {
                        'margin': '8px 0'
                    }
                } else {
                    return {
                        'margin': '8px',
                        'padding': '16px',
                        'background': '#fff'
                    }
                }
            });
        }
    }
}