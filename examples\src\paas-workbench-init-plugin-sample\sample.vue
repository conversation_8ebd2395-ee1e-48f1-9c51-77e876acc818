<template>
    <div class="h-100" :key="timestamp">
        <div class="switch">
            是否开启浮层
            <fx-switch v-model="enableFloatLayer" size="small" @change="handleChange">
            </fx-switch>
        </div>
        <div ref="content"></div>
    </div>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import Page from '../components/Page.vue';
import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
export default {
    data() {
        return {
            enableFloatLayer: true,
            timestamp: +new Date()
        }
    },
    mounted() {
        this.initEditor();
    },
    methods: {
        handleChange() {
            this.timestamp = +new Date();
            this.$forceUpdate();
            this.$nextTick(() => {
                this.initEditor();
            })
        },
        initEditor() {
            init(
                this.$refs.content,
                {
                    resolver: {
                        <PERSON><PERSON>,
                        <PERSON>
                    },
                    import: [{
                        name: '<PERSON>'
                    }],
                    workbench: {
                        materials: [{
                            "title": "示例组件",
                            "children": [{
                                "title": "示例",
                                "name": "Demo"
                            }]
                        }],
                        enableFloatLayer: this.enableFloatLayer
                    },
                    plugins: [
                        builtinPlugins.PaasWorkbenchInit,
                    ],
                    useDefaultPluginPackages: false
                }
            )
        }
    }
}
</script>
<style lang="less" scoped>
.switch {
    position: absolute;
    width: 150px;
    left: 0;
    right: 0;
    top: 5px;
    margin: auto;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>