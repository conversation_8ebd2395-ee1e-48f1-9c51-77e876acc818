<template>
    <fx-popover :visible-arrow="false" width="258" trigger="click" class="bc-background-img-popover"
        popper-class="bc-background-img-dropdown">
        <div class="bc-background-img-body" slot="reference">
            <div class="bc-background-img-body-left">
                <fx-image v-if="imgUrl" class="bc-background-img" :src="imgUrl"></fx-image>
            </div>
            <div class="bc-background-img-body-main">
                <span class="bc-img-container-label">{{ $t('图片') }}</span>
            </div>
            <!-- <div class="background-img-body-right">
        <span class="transparency-percentage">{{transparency}}%</span>
      </div> -->
        </div>
        <div class="bc-background-img-dropdown-body">
            <fx-upload class="bc-background-img-uploader" url="#" :max-size="maxSize" :show-file-list="false"
                :accept="'.png,.jpg,.jpeg'" :on-change="onUploadChange" :on-exceed="onUploadExceed">
                <div class="bc-uploader-aera">
                    <fx-image v-if="imgUrl" class="bc-uploader-background-img" :src="imgUrl"></fx-image>
                    <i v-if="imgUrl" class="el-icon-delete bc-uploader-delete-icon" @click.stop="deleteImg"></i>
                    <i v-else class="el-icon-plus bc-uploader-updown-icon"></i>
                </div>
                <div slot="tip" class="el-upload__tip" v-if="maxSize">
                    {{ maxSizeInMBLabel }}
                </div>
            </fx-upload>
            <fx-select width="100%" :placeholder="$t('请选择')" size="small" v-model="globalValue.backgroundSize"
                :options="options" @change="handleAlignTypeChange">
            </fx-select>
            <div class="bc-alignment-aera">
                <span class="bc-background-img-label">{{$t('对齐方式')}}</span>
                <div class="bc-alignment-box">
                    <AlignmentScale
                        v-model="globalValue.backgroundPosition"
                        @change="handleAlignChange"
                    ></AlignmentScale>
                </div>
            </div>
            <!-- <div class="transparency-aera">
        <span class="background-img-label">透明度</span>
        <fx-slider
          class="img-transparency-slider"
          v-model="transparency"
          size="mini"
          @change="handleTransparencyChange"
        ></fx-slider>
      </div> -->
        </div>
    </fx-popover>
</template>
<script>
import AlignmentScale from './alignment-scale.vue';
export default {
    components: {
        AlignmentScale,
    },
    props: {
        globalValue: Object,
    },
    data() {
        return {
            maxSize: 1536,
            options: [],
            // transparency: '',
            imgUrl: '',
        };
    },
    computed: {
        maxSizeInMBLabel() {
            const name = 'jpg,png,jpeg';
            const maxSizeInMB = this.maxSize > 0 ? (this.maxSize / 1024).toFixed(1) : '0';
            return $t(
                'beecraft.setters.backgroundImg.size',
                { name, size: maxSizeInMB},
                '仅支持{{name}}格式；大小不超过{{size}}M',
            );
        },
    },
    created() {
        this.options = [
            {
                label: $t('beecraft.setters.backgroundImg.contain', {}, '等比缩放'),
                value: 'contain',
            },
            {
                label: $t('beecraft.setters.backgroundImg.cover', {}, '填充容器'),
                value: 'cover',
            },
        ];
        this.init();
    },

    methods: {
        init() {
            this.dealImgUrl();
            // this.dealTransparency();
        },
        // 处理初始化imgUrl
        dealImgUrl() {
            if (this.globalValue.backgroundImage && this.globalValue.backgroundImage !== 'none') {
                const backgroundImage = this.globalValue.backgroundImage.trim();
                // 正则用于提取 url 或 base64 数据
                const urlPattern = /^url\(["']?(.*?)["']?\)$/;
                const match = backgroundImage.match(urlPattern);
                if (match && match[1]) {
                    const extractedUrl = match[1].trim();
                    this.imgUrl = extractedUrl;
                }
            }
        },
        // dealTransparency() {
        //   // TODO: 透明度处理的不对，背景图片没有单独的css属性控制它的透明度
        //   if (this.globalValue.backgroundColor) {
        //     const backgroundColor = this.globalValue.backgroundColor.trim();
        //     const rgbaPattern = /^rgba?\((\d+),\s*(\d+),\s*(\d+),\s*(\d*\.?\d+)?\)$/;
        //     const match = backgroundColor.match(rgbaPattern);
        //     if (match) {
        //       const r = parseInt(match[1], 10);
        //       const g = parseInt(match[2], 10);
        //       const b = parseInt(match[3], 10);
        //       const transparency = match[4] ? parseFloat(match[4]) * 100 : 100; // 转换成百分比
        //       this.transparency = transparency;

        //       // TODO:可以删
        //       console.log(`Red: ${r}, Green: ${g}, Blue: ${b}, Alpha: ${transparency}%`);
        //       this.backgroundColorRgba = {r, g, b, transparency};
        //     } else {
        //       this.transparency = 100;
        //     }
        //   } else {
        //     this.transparency = 100;
        //   }
        // },
        onUploadChange(file) {
            const rawFile = file.raw;
            const reader = new FileReader();
            reader.onload = (e) => {
                const base64data = e.target.result;
                this.imgUrl = base64data;
                const backgroundImage = `url('${base64data}') `;
                this.globalValue.backgroundImage = backgroundImage;
                this.initBackgroundPosition(this.globalValue.backgroundSize);
                this.$emit('change');
            };
            reader.readAsDataURL(rawFile);
        },
        onUploadExceed(file, fileList, msg){
            console.log(file, fileList, msg);
            if(msg.msg){
                this.$message({
                    message: msg.msg,
                    type: 'error'
                })
            }
        },
        deleteImg() {
            this.imgUrl = '';
            this.globalValue.backgroundImage = 'none';
            this.$emit('change');
        },
        handleAlignTypeChange(value) {
            this.initBackgroundPosition(value);
            this.$emit('change');
        },
        handleAlignChange(value) {
            this.globalValue.backgroundPosition = value;
            this.$emit('change');
        },
        // handleTransparencyChange(value) {
        //   const decimal = (value / 100).toFixed(2);
        //   this.globalValue.backgroundColor = `rgba(255,255,255,${decimal})`;
        //   this.$emit('change');
        // },
        // 提交前初始化backgroundPosition
        initBackgroundPosition(value) {
            if (value === 'contain' && !this.globalValue.backgroundPosition) {
                this.globalValue.backgroundPosition = 'left top';
            } else if (value === 'cover' && !this.globalValue.backgroundPosition) {
                this.globalValue.backgroundPosition = 'top';
            }
        },
        // change时修改imgUrl的值，使其回显正常
        // updateImgUrl(backgroundImage){

        //     this.imgUrl = newUrl
        // }
    },
};
</script>

<style lang="less" scoped>
.bc-background-img-popover {
    width: 100%;

    .bc-background-img-body {
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 28px;
        max-height: 48px;
        border: 1px solid var(--color-neutrals07, #c1c5ce);
        border-radius: 4px;
        cursor: pointer;

        &:focus {
            border: 1px solid var(--color-primary06, #ff8000);

            .bc-background-img-body-left {
                border: 1px solid var(--color-primary06, #ff8000);
            }

            .bc-background-img-body-right {
                border-left: 1px solid var(--color-primary06, #ff8000);
            }
        }

        .bc-background-img-body-left {
            width: 20px;
            height: 20px;
            border: 1px solid var(--color-neutrals07, #c1c5ce);
            margin-left: 4px;

            .bc-background-img {
                width: 20px;
                height: 20px;
            }
        }

        .bc-background-img-body-main {
            flex: 1;

            .bc-img-container-label {
                margin-left: 4px;
            }
        }

        .bc-background-img-body-right {
            width: 44px;
            height: 28px;
            border-left: 1px solid var(--color-neutrals07, #c1c5ce);
        }
    }
}

.bc-background-img-dropdown {
    .bc-background-img-dropdown-body {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .bc-background-img-uploader {
            min-height: 88px;

            .bc-uploader-aera {
                position: relative;
                width: 258px;
                height: 68px;
                background: var(--color-neutrals02, #fafafa);

                .bc-uploader-background-img {
                    width: 258px;
                    height: 68px;
                }

                .bc-uploader-delete-icon {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    font-size: 16px;
                    top: 0;
                    left: 0;
                    line-height: 68px;
                    background-color: #212b3699;
                    color: var(--color-neutrals01, #ffffff);
                }

                .bc-uploader-updown-icon {
                    position: absolute;
                    font-size: 16px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #171313;
                }
            }
        }

        .bc-alignment-aera {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 8px;

            .bc-alignment-box {
                width: 34px;
                height: 34px;
            }
        }

        .bc-transparency-aera {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 8px;
        }

        .bc-background-img-label {
            color: var(--color-neutrals15);
        }
    }
}
</style>
<style lang="less">
.bc-background-img-dropdown.el-popper {
    margin-top: 0;
}

.bc-background-img-dropdown {
    padding: 12px;

    .bc-background-img-dropdown-body {
        .bc-background-img-uploader {
            .el-upload {
                width: 100%;
                border: 1px dashed var(--color-neutrals05);
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .el-upload:hover,
            .el-upload:focus {
                border-color: var(--color-primary05, #ff9b29);
            }

            .el-upload:active {
                border-color: var(--color-primary07, #d96500);
            }

            .el-upload__tip {
                margin-top: -1px;
            }
        }
    }
}
</style>