import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        import: [{
            id: 'gridlayout',
            name: 'GridLayout',
            data: {
                rowHeight({ viewportHeight, margin }) {
                    // return (viewportHeight - margin[1]) / 10 - margin[1];
                    return 16;
                },
                margin: [12, 12],
                layout: []
            },
            children: [
            //     {
            //     name: 'Demo',
            //     $$data: {
            //         layer: {
            //             w: 6,
            //             h: 6,
            //             x: 2,
            //             y: 2,
            //         }
            //     }
            // }
        ]
            // children: []
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                },{
                    "title": "轮播图",
                    "name": "SlideImage"
                }]
            }]
        },
        isUseDefaultStyle: true,
        enabled: true,
        plugins: [

        ]
    }
).then((editor: any) => {

})