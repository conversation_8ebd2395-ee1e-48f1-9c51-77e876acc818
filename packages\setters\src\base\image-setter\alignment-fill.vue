<template>
    <fx-tooltip effect="light" placement="right" :visible-arrow="false" v-model="showOverlay" manual
        popper-class="alignment-scale-dropdown">
        <div class="small-grid-container" @click="openOverlay">
            <div v-for="(position, index) in positions" :key="index" class="small-grid-item"
                :class="{ highlighted: selectedIndex === index }"></div>
        </div>
        <div slot="content" class="overlay" @mouseleave="closeOverlay">
            <div class="large-grid-container">
                <div v-for="(position, index) in positions" :key="'large-' + index" class="large-grid-item" :class="{
        highlighted: selectedIndex === index,
        hoverHighlight: highlightedIndex === index,
    }" @mouseover="highlightCell(index)" @click="selectPosition(index)"></div>
            </div>
        </div>
    </fx-tooltip>
</template>

<script>
export default {
    props: {
        value: String,
        default: 'top', // 默认值可以是 'top', 'center', 'bottom'
    },
    data() {
        return {
            positions: [
                { label: '上', value: 'top' },
                { label: '中', value: 'center' },
                { label: '下', value: 'bottom' },
            ],
            highlightedIndex: null,
            selectedIndex: null,
            showOverlay: false,
        };
    },
    mounted() {
        // 设置默认选中项
        this.setSelectedIndex(this.value);
    },
    watch: {
        // 监听父组件传入的 value 变化
        value(newValue) {
            this.setSelectedIndex(newValue);
        },
    },
    methods: {
        highlightCell(index) {
            this.highlightedIndex = index;
        },
        selectPosition(index) {
            this.selectedIndex = index;
            this.showOverlay = false;
            this.$emit('change', this.positions[index].value);
        },
        openOverlay() {
            this.showOverlay = true;
        },
        closeOverlay() {
            this.highlightedIndex = null;
            this.showOverlay = false;
        },
        setSelectedIndex(value) {
            // 根据传入的 value 设置选中项
            const index = this.positions.findIndex((position) => position.value === value);
            this.selectedIndex = index !== -1 ? index : 0; // 默认选择第一个
        },
    },
};
</script>

<style scoped>
.small-grid-container,
.large-grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2px;
}

.small-grid-container {
    width: 34px;
    height: 34px;
    overflow: hidden;
    border-radius: 2px;
}

.large-grid-container {
    width: 64px;
    height: 64px;
}

.small-grid-item,
.large-grid-item {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-neutrals05, #dee1e8);
    cursor: pointer;
    transition: background-color 0.3s;
}

.small-grid-item.highlighted,
.large-grid-item.highlighted {
    background-color: var(--color-primary06, #ff8000);
}

.large-grid-item.hoverHighlight {
    background-color: var(--color-primary07, #d96500);
}
</style>
<style lang="less">
.alignment-scale-dropdown {
    padding: 12px;
}
</style>