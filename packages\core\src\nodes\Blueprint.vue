<script>
import invariant from 'tiny-invariant';
import { loadResolver } from '@beecraft/shared';

export default {
    name: 'Blueprint',

    props: {
        template: {
            type: [Object, String],
            required: true
        },
    },

    inject: ['useInternalEditor'],

    render(createElement) {
        return this.$slots.default?.[0];
    },

    mounted() {
        const schemaOrName = this.template;

        const { connectors: editorConnectors, actions, options } = this.useInternalEditor(({ options }) => {
            return {
                options
            }
        });

        let template = typeof schemaOrName == 'string'
            ? { name: schemaOrName }
            : schemaOrName;

        // editorConnectors.create(this.$el, template, {
        //     onDragStart: () => {
        //         return new Promise((resolve) => {
        //             resolve();
        //         })
        //     }
        // });
        
        editorConnectors.create(this.$el, {
            // 拖拽开始时，加载对应的组件
            onDragStart: () => {
                return new Promise((resolve) => {
                    const getResolver = options.resolver[template.name]
                        ? Promise.resolve(options.resolver)
                        : loadResolver(template.name, template.source, options);

                    getResolver.then((result) => {
                        actions.history.ignore().setOptions(options => {
                            Object.assign(options.resolver, result);
                        });

                        const beecraftData = result[template.name].beecraft(options);

                        const templateFromBeecraft = beecraftData.$$data?.template
                        if(beecraftData.$$data?.template) {
                            template = {
                                ...templateFromBeecraft,
                                ...template,
                                data: {
                                    ...(templateFromBeecraft.data || {}),
                                    ...(template.data || {}),
                                },
                                $$data: {
                                    ...(templateFromBeecraft.$$data || {}),
                                    ...(template.$$data || {}),
                                },
                            }
                        }

                        // const template = beecraftData.$$data?.template
                        //     ? beecraftData.$$data.template
                        //     : { name: template.name }

                        console.log('onDragStart', template);
                        resolve({ schema: template });
                    });
                })
            }
        });
    }
}
</script>