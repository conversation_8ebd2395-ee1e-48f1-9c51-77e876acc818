{"name": "beecraft", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "pnpm -F @beecraft/** run build", "changeset": "changeset", "setup": "npm run clean-npm --workspaces & pnpm install", "start:examples": "pnpm -F @beecraft/examples run start", "build:examples": "pnpm -F @beecraft/examples run build"}, "workspaces": ["packages/**", "examples"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"immer": "10.0.3", "less": "3.13.1", "tiny-invariant": "^1.3.1", "vue": "2.6.14"}, "devDependencies": {"@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.23.3", "@changesets/cli": "^2.27.1", "@rollup/plugin-babel": "^6.0.4", "@vitejs/plugin-legacy": "1.8.2", "@vitejs/plugin-vue2-jsx": "1.1.0", "babel-plugin-transform-async-functions": "^6.22.0", "babel-plugin-transform-class-properties": "^6.24.1", "transform-class-properties": "1.0.0-beta", "tslib": "2.6.2", "typescript": "4.9.5", "vite": "2", "vite-plugin-build": "^0.10.0", "vite-plugin-checker": "0.6.2", "vite-plugin-css-injected-by-js": "^3.3.0", "vite-plugin-externals": "0.6.2", "vite-plugin-vue2": "2.0.3", "vue-template-compiler": "2.6.14"}, "engines": {"node": ">=16", "pnpm": ">=8"}}