const settings = [{
    name: 'Set<PERSON><PERSON><PERSON>',
    data: {
        label: '测试1',
        display: 'block'
    },
    children: [{
        name: '<PERSON><PERSON><PERSON><PERSON>',
        data: {
            label: '测试1',
            display: 'block'
        },
        children: [{
            id: 'text',
            name: 'Set<PERSON><PERSON><PERSON>',
            data: {
                label: '单行文本',
                display: 'block',
                setter: {
                    component: 'StringSetter',
                    ranges: ['data', 'text']
                }
            }
        }]
    },{
        id: 'text1',
        name: 'Setter<PERSON><PERSON>',
        data: {
            label: '单行文本',
            display: 'block',
            setter: {
                component: 'StringSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        id: 'longtext',
        name: 'Set<PERSON><PERSON><PERSON>',
        data: {
            label: '多行文本',
            setter: {
                component: 'TextareaSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        id: 'select',
        name: 'SetterField',
        data: {
            label: '选择器',
            setter: {
                component: 'SelectSetter',
                ranges: ['data', 'text'],
                data: {
                    options: [{
                        value: '选项1',
                        label: '黄金糕'
                    }, {
                        value: '选项2',
                        label: '双皮奶'
                    }]
                }
            }
        }
    },{
        id: 'switch',
        name: 'Setter<PERSON><PERSON>',
        data: {
            label: '开关',
            setter: {
                component: 'SwitchSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '自定义',
            setter: {
                ranges: ['data', 'text'],
                resource: {
                    render: (h: any) => h('div', 2333)
                }
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '扩展setters',
            setter: {
                component: 'TestSetter',
                ranges: ['data', 'text'],
            }
        }
    }]
}];

const style = [
    {
        name: 'SetterField',
        data: {
            label: $t('边距', {}, '边距'),
            display: 'block',
            setter: {
                component: 'LayoutSetter',
                ranges: ['$$data', 'style'],
                data: {
                    type: 'margin'
                }
            }
        }
    },
    {
        name: 'SetterField',
        data: {
            label: $t('对齐方式', {}, '对齐方式'),
            display: 'block',
            helpText: $t('beecraft.setters.component.alignment', {}, '容器内组件的对齐方式'),
            setter: {
                component: 'AlignSetter',
                ranges: ['$$data', 'style'],
                data: {
                    type: 'vertical'
                }
            }
        }
    },
    {
        name: 'SetterField',
        data: {
            label: $t('beecraft.setters.component.spacing', {}, '组件间距'),
            display: 'block',
            setter: {
                component: 'ComponentSpacingSetter',
                ranges: ['$$data', 'style'],
            }
        }
    },
    {
        name: 'SetterField',
        data: {
            label: $t('背景', {}, '背景'),
            display: 'block',
            setter: {
                component: 'BackgroundSetter',
                ranges: ['$$data', 'style']
            }
        }
    },
]

export default function () {
    return {
        type: 'Demo',
        name: $t('测试'),
        data: {
            text: ''
        },
        $$data: {
            isCanvas: true,
            title: $t('示例组件'),
            layer: {
                w: 6,
                h: 6,
            },
            defaultSetting: 'style'
        },
        related: {
            styleSettings: style,
            attributeSettings: settings,
            settings: settings,
        },
        rules: {
            canDelete() {
                // console.log('canDelete', arguments)
                return true;
            }
        },
        hooks: {
            beforeRender() {
                console.log('beforeRender', arguments);
                return true;
            },
            rendered() {
                // console.log('rendered', arguments);
            },
            // mounted() {
            //     console.log('beforeCreate', arguments);
            // },
            created() {
                // console.log('created', arguments);
            },
            beforeDelete({ id }, { query }){
                console.log('beforeDelete', query.instance(id));
            },
            deleted(){
                // console.log('deleted', arguments);
            }
        }
    }
}