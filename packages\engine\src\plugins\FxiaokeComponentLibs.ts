import * as fxui from '@beecraft/fxui';
import * as materials from '@beecraft/materials';
// import { deepMerge } from '@beecraft/shared';

/**
 * @module engine/plugins/BuiltinComponentLibs
 * @desc 纷享组件库，安装后可直接使用纷享研发业务团队的组件
 */
export default class FxiaokeComponentLibs {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        config.add('normalizeNodes', function (node, { query, actions }) {
            if (!node._done) {
                (<any>window).Fx.getBizComponent(node.source, node.name).then(res => {
                    if (res?.beecraft) {
                        // 缓存组件到resolver当中
                        actions.history.ignore().setOptions((options) => {
                            options.resolver[node.name] = res;
                        });

                        const nNode = query.parseSerializedNode(node, { [node.name]: res }).toNode();
                        const { parent, children } = node;
                        actions.history.ignore().setState((state) => {
                            var oNode = state.nodes[node.id]; // 过度状态的节点
                            if(oNode) {
                                Object.assign(oNode, nNode, { parent, children });
                            }else {
                                Object.assign(node, nNode, { parent, children });
                            }
                        })

                    }
                })
            }
        });
    }
}