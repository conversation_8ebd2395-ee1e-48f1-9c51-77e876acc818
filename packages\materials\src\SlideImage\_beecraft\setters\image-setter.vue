<template>
    <div>
        <template v-if="!isFullHeight">
            <div>{{ cHeightTips }}</div>
            <div class="slide-height-content">
                <fx-slider class="slide-input" :disabled="!!cItems.length || uploading" v-model="slideHeight" @input="handleSliderInput" :min="min" :max="max"></fx-slider>
                <fx-input class="num-input" :disabled="!!cItems.length || uploading" type="number" v-model="slideHeight" size="mini" :min="min" :max="max"
                    @change="handleInput"></fx-input>
            </div>
        </template>
        <div class="slide-items-content">
            <fx-draggable :list="cItems" @change="updateImages">
                <slide-item v-for="(image, index) in cItems" :class="{
                actived: isactived(index)
            }" :isActive="isactived(index, image)" :key="image.id" :image="image" :index="index" @remove="removeItem"
                    @change="updateItem" @click.native="switchItem(index)" />
            </fx-draggable>
        </div>
        <div v-if="showUploader" class="slide-add-images">
            <fx-upload class="image-uploader" :show-file-list="false" :on-success="handleImageSuccess"
                :on-error="handleImageError" :before-upload="beforeImageUpload" accept="image/*">
                <i class="el-icon-plus uploader-icon"></i>
                <div class="tips">
                    1. {{ cMaxCountTips }}
                    <br />
                    2. {{ $t('仅支持jpg、png格式') }}
                    <br />
                    3. {{ $t('ui_custom.preset.mobile.newSliderImageDesc', { height: sliderHeight }, '图片大小不超过1.5M') }}
                </div>
            </fx-upload>
        </div>
    </div>
</template>
<script>
import SlideItem from './image-slide-item.vue';

export default {
    inject: ['useInternalEditor', 'useInternalNode'],

    components: {
        SlideItem
    },

    data() {
        const { slideHeight, isFullHeight } = this.useInternalNode((node) => {
            return {
                slideHeight: node.data.height,
                isFullHeight: node.data.isFullHeight,
            }
        })
        return {
            uploading: false,
            sliderHeight: this.getHeight(),
            activeItem: -1,
            slideHeight,
            min: 150,
            max: 900,
            isFullHeight
        }
    },

    updated() {
        // todo 图片闪烁
        console.log('updated');
    },

    computed: {
        showUploader() {
            return this.cItems.length < this.cMaxCount;
        },

        cItems() {
            const { items } = this.useInternalNode((node) => {
                return {
                    items: node.data.items || [],
                }
            });

            return items;
        },

        cMaxCount() {
            return (this.useInternalNode((node) => {
                return {
                    maxCount: node.data.maxCount || 10,
                }
            })).maxCount;
        },

        cMaxCountTips() {
            return $t('ui_custom.preset.mobile.maxSlideImages', { maxCount: this.cMaxCount }, '最多设置10张');
        },

        cHeightTips(){
            return $t('beecraft.materials.setCarouselImageHeight', { num: 1080 }, '设置轮播图高度 (宽度为{{num}})')
        }
    },

    methods: {
        isactived(index, e) {
            return this.activeItem === index;
        },
        uploadImage() {
            this.$refs.uploader.$el.click();
        },
        getHeight() {
            return parseInt(this.node?.data?.imgHeight ?? 450, 10);
        },
        switchItem(index) {
            this.activeItem = index;

            this.$nextTick(() => {
                const { instance } = this.useInternalNode();
                instance.setActiveItem(index);
            })
            // // todo 在这里先粗暴的更新display组件
            // const disDots = document.getElementById('j-cs-slideimgae-dots');
            // const nextDot = disDots.querySelectorAll('.dot')?.[index];
            // if (nextDot) {
            //     nextDot.click();
            // }
        },

        updateItem({ index, value }) {
            const { actions } = this.useInternalNode();
            const { icon, limit, img, isUpdateImage, ...data } = value;

            actions.setCustom(props => {
                const image = props.items[index];
                Object.assign(image, data);
                Object.keys(image).forEach(key => {
                    if (isUpdateImage) {
                        image.img = img;
                    } else {
                        // 清空老得数据
                        if (key !== 'img') {
                            if (!data[key]) {
                                image[key] = '';
                            }
                        }
                    }
                });
            });
        },

        // 顺序变更
        updateImages({ moved: { newIndex, oldIndex } }) {
            const { actions } = this.useInternalNode();
            const { activeItem } = this;

            actions.setCustom(props => {
                const activeImg = props.items[activeItem].img;
                const image = props.items.splice(oldIndex, 1);
                props.items.splice(newIndex, 0, ...image);
                //保证激活的轮播项是正确的
                this.switchItem(props.items.findIndex(item => item.img === activeImg));
            });

        },

        removeItem(index) {
            const { actions } = this.useInternalNode();

            actions.setCustom(props => {
                props.items.splice(index, 1);
            });
        },

        handleImageSuccess(fileData) {
            this.uploading = false;
            const { actions } = this.useInternalNode();
            actions.setCustom(props => {
                props.items.push({
                    action: '',
                    img: fileData.TempFileName,
                    menuType: 4
                });
                this.switchItem(props.items.length - 1);
            });
        },

        handleImageError() {
            this.uploading = false;
        },

        /**
         * @desc 校验图片
         * 1、仅支持jpg、jpeg、png格式
         * 2、图片大小不超过1.5M
         * 3、图片尺寸必须是1280*{高度}
         */
        beforeImageUpload(files) {
            this.uploading = true;
            files = Array.from(files);

            let error = '';
            files.some((file) => {
                if (/\.(?:png|jpeg|jpg)$/.test(file.name)) {
                    if (file.size > 1.5 * 1024 * 1024) {
                        error = $t('ui_custom.preset.mobile.newSliderImageSize', null, '图片大小不超过1.5M！');
                    } else if (false) {
                        error = $t(
                            'ui_custom.preset.mobile.newSliderImageWidth',
                            { height: this.slideHeight },
                            '图片大小有误'
                        );
                    }
                } else {
                    error = $t('请上传格式为jpg或者png的图片！');
                }
                return !!error;
            });

            if (error) {
                this.uploading = false;
                this.$alert(error, {
                    confirmButtonText: $t('确定'),
                    type: 'warning'
                });
                return false;
            }
        },

        onOrderChange() {

        },

        handleSliderInput(val) {
            const { actions } = this.useInternalNode();

            actions.setCustom((props) => {
                props.height = val;
            })
        },

        handleInput(val) {
            if(val < this.min) {
                this.value = this.min;
            }else if(val > this.max) {
                this.value = this.max;
            }
        }
    }
}
</script>
<style lang="less" scoped>
.image-uploader {
    width: 100%;

    /deep/.fx-upload {
        width: 100%;
    }
}

.uploader-icon {
    font-size: 24px;
    color: var(--color-primary06);
    height: 104px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 1px;
    border: 1px dashed #c1c5ce;
    cursor: pointer;
}

.tips {
    margin-top: 8px;
    font-size: 10px;
    // text-align: center;
    text-align: left;
    line-height: normal;
    color: #c1c5ce;
}

.add-slide-images {
    // margin-top: 16px;
}

.slide-height-content {
    display: flex;
    width: 100%;
    align-items: center;

    .slide-input {
        flex: 1;
        margin-right: 10px;
    }

    .num-input {
        width: 70px;
    }
}
</style>