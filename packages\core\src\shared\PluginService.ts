import Config from '../shared/config';

type Plugins = Function[];

export default class PluginService {

    private _context: any = null;

    private _service: any = null;

    private _useService: Promise<void> | null = null;

    constructor(context, plugins) {

        this._context = context;

        //@ts-ignore
        const Plug: any = window.Plug;

        if(Plug) {
            const service = this._service = new Plug({
                appId: 'beecraft'
            });
            this.use(plugins);
        }

    }

    use(plugins: Plugins = []): Promise<void> {
        const service = this._service;

        const avaliablePlugins = plugins.map(Plugin => {
            return typeof Plugin === 'function' ? {
                pluginApiName: Plugin.name,
                resource: function(){
                    return Plugin
                }
            } : Plugin;
        });

        this._useService = new Promise((resolve, reject) => {
            if(service) {
                service.use(avaliablePlugins).then(resolve, reject);
            }else {
                resolve();
            }
        });

        return this._useService;
    }

    run(name, params) {
        const service = this._service;
        const context = this._context;
        const useService: any = this._useService;

        const config = new Config({});

        // 合并上下文至参数
        params = Object.assign(Object.create(context), { config, ...params });

        function _next(resolve, reject) {
            service.run(name, params).then((rst) => {
                if(rst.StatusCode === 0) {
                    const configData = config.toJSON();
                    if(Object.keys(configData).length > 0) {
                        rst.Value = configData;
                    }
                }
                resolve(rst);
            }, reject)
        }

        return new Promise((resolve, reject) => {
            if(useService) {
                useService.then(() => _next(resolve, reject))
            }else {
                _next(resolve, reject);
            }
        });
    }
}