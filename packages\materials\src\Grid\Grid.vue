<template>
    <div class="bc-grid d-flex">
        <slot></slot>
    </div>
</template>
<script lang="js">
    export default {
        beecraft: function () {
            return {
                name: 'Grid',
                displayName: $t('栅格容器'),
                $$data: {
                    // noWrapper: true,
                    template: {
                        name: '<PERSON>rid',
                        children: [{
                            name: 'GridItem',
                            data: {
                                ratio: 6
                            }
                        },{
                            name: 'GridItem',
                            data: {
                                ratio: 6
                            }
                        }]
                    }
                }
            }
        }
    }
</script>
<style lang="less" scoped>
    
</style>