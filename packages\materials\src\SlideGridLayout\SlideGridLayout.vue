<template>
    <div class="slide-grid-layout h-100">
        <GridLayout ref="gridLayout" v-bind="$props" @layout:update="onLayoutUpdate" :handleDragOver="onDragOver"
            :handleDrop="onDrop" :handleDrag="onDrag" :handleLayoutChange="onLayoutChange"
            :handleDragStart="onDragStart" :handleDragEnter="onDragEnter" :handleDragStop="onDragStop"
            :handleResizeStop="onResizeStop" :layout="layoutData" :enabled="cEnabled">
            <slot></slot>
        </GridLayout>
    </div>
</template>
<script>
import GridLayout from '../GridLayout/components/GridLayout.vue';
import {
    findBestPos,
    moveElement,
    compactType,
    compact
} from '../GridLayout/components/utils';

function arraysAreEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
        return false;
    }

    const sortedArr1 = arr1.slice().sort();
    const sortedArr2 = arr2.slice().sort();

    for (let i = 0; i < sortedArr1.length; i++) {
        if (sortedArr1[i] !== sortedArr2[i]) {
            return false;
        }
    }

    return true;
}

const defaultDragPos = { "w": 6, "h": 6, "i": null };

export default {
    props: {
        isFullHeight: Boolean,
        ...GridLayout.props,
    },
    components: {
        GridLayout
    },
    inject: [
        'useInternalNode',
        'useInternalEditor',
        'useEventHandler'
    ],
    watch: {
        layout: {
            immediate: true,
            handler(val = []) {
                this.layoutData = val;
                // 为什么要保留备份？为了监听GridLayout子节点数量变化（新增/删除），此时需要用layoutData和children的数量作比较，但是layoutData有响应式处理，任何变化都会触发此次比较的逻辑，所以需要一个没有响应式处理的备份。
                this.copyLayoutData = JSON.parse(JSON.stringify(val));
                setTimeout(() => {
                    this.dispatchEvent('BEECRAFT_GRIDLAYOUT_WIDTH_UPDATE');
                }, 250);
            }
        },
        mode(val) {
            let layout = this.layout;

            if (val === 'carousel') {
                layout = compact(layout, compactType(this), this.colNum, this.allowOverlap, this.maxRowsPerPage, this.cEnabled);
            } else {
                layout = compact(layout.map(item => {
                    return {
                        ...item,
                        h: item.oh || item.h
                    }
                }), compactType(this), this.colNum);
            }

            const { id } = this.useInternalNode();
            const { actions } = this.useInternalEditor();

            actions.setCustom(id, data => {
                data.layout = layout;
            });
            layout.forEach(item => {
                actions.setProp(item.i, ($$data) => {
                    $$data.layer = Object.assign($$data.layer || {}, {
                        w: item.w,
                        h: item.h,
                        x: item.x,
                        y: item.y,
                        oh: item.oh
                    });
                });
            });
        }
    },
    data() {
        return {
            layoutData: []
        }
    },
    computed: {
        cEnabled() {
            const { enabled } = this.useInternalEditor((state) => ({ enabled: state.options.enabled }));
            return enabled;
        }
    },
    created() {
        // this.options = { ...this.$attrs }
        this.observeChildren();
    },
    mounted() {
        this.isMounted = true;
    },
    methods: {

        onLayoutUpdate(target, layout) {

        },

        onDragOver(e) {
            const { query, indicator, actions } = this.useInternalEditor((state) => {
                return {
                    indicator: state.indicator
                }
            });

            if (indicator) {
                if (indicator.placement.parent?.name === 'SlideGridLayout') {
                    actions.setIndicator(null);
                } else {
                    return false;
                }
            }
            const dragTarget = this.useEventHandler().positioner?.dragTarget;

            return dragTarget.tree.nodes[dragTarget.tree.rootNodeId].$$data?.layer || {};
        },

        onDrop(layout, item) {
            // node.children中添加节点
            const { id } = this.useInternalNode();
            const { actions, indicator } = this.useInternalEditor();
            const dragTarget = this.useEventHandler().positioner.dragTarget;

            if (indicator) {
                return;
            }

            actions.addNodeTree(dragTarget.tree, id, 1);
            this.useEventHandler().dropElement(function () { }); // 拖拽会触发drag事件，所以需要手动做一次清空
            this.copyDropItem = item;
            this.dispatchEvent('BEECRAFT_GRIDLAYOUT_DRAG_STOP');
        },

        onDragStop() {
            this.dispatchEvent('BEECRAFT_GRIDLAYOUT_DRAG_STOP');
        },

        onDrag(layout, oldDragItem, l, placeholder) {
            this.dispatchEvent('BEECRAFT_GRIDLAYOUT_DRAG');
        },

        onDragStart() {
            this.dispatchEvent('BEECRAFT_GRIDLAYOUT_DRAG_START');
        },

        onDragEnter(e) {

        },

        onResizeStop(newLayout, oldResizeItem, l) {
            this.dispatchEvent('BEECRAFT_GRIDLAYOUT_RESIZE_STOP');

            const { actions } = this.useInternalEditor();
            actions.setNodeEvent('selected', [l.i]);
        },

        onLayoutChange(newLayout, oldLayout) {
            const { id, actions } = this.useInternalNode();
            const { actions: editorActions } = this.useInternalEditor();

            actions.setCustom(data => {
                data.layout = newLayout;
            });
            newLayout.forEach(item => {
                editorActions.history.merge().setProp(item.i, ($$data) => {
                    $$data.layer = Object.assign($$data.layer || {}, {
                        w: item.w,
                        h: item.h,
                        x: item.x,
                        y: item.y,
                    });
                })
            });
            this.useEventHandler().dropElement();
        },

        dispatchEvent(eventName) {
            document.dispatchEvent(new CustomEvent(eventName, {
                bubbles: true,
                cancelable: true,
            }));
        },

        // 监听子元素变更
        observeChildren() {
            new Vue({
                render: () => {
                    console.log('监听到子元素变更');
                    const { colNum } = this;
                    const copyDropItem = this.copyDropItem;
                    let { query, actions } = this.useInternalEditor();
                    const { id, children } = this.useInternalNode(node => {
                        return {
                            children: node.children // 访问一下，触发依赖
                        }
                    });
                    let layout = JSON.parse(JSON.stringify(this.copyLayoutData));
                    actions = this.isMounted ? actions.history.merge() : actions.history.ignore();

                    if (!arraysAreEqual(layout.map(item => item.i), children)) {
                        let newLayout = [];

                        // 先把留下的留下。为了保证在添加新组件时，排序是可以计算在内的
                        layout.forEach(item => {
                            if (children.indexOf(item.i) > -1) {
                                newLayout.push(item);
                            }
                        });

                        children.forEach(id => {
                            const itemExistLayout = newLayout.find(item => item.i === id);

                            if (!itemExistLayout) {
                                const node = query.node(id).get();
                                const componentDefaultLayer = node.$$data?.layer || {};

                                const l = {
                                    x: copyDropItem ? copyDropItem.x : componentDefaultLayer.x ?? defaultDragPos.x,
                                    y: copyDropItem ? copyDropItem.y : componentDefaultLayer.y ?? defaultDragPos.y,
                                    h: copyDropItem ? copyDropItem.h : componentDefaultLayer.h ?? defaultDragPos.h,
                                    w: copyDropItem ? copyDropItem.w : componentDefaultLayer.w ?? defaultDragPos.w,
                                    i: id,
                                    static: componentDefaultLayer.static
                                }

                                const oldL = {
                                    x: -1,
                                    y: -1,
                                    w: l.w,
                                    h: l.h,
                                    i: l.i,
                                    static: l.static
                                };

                                if (l.x === undefined || l.y === undefined) {
                                    const bestPos = findBestPos(colNum, newLayout, l.w, l.h);
                                    l.x = bestPos.x;
                                    l.y = bestPos.y;
                                }

                                if (l.x + l.w > colNum) {
                                    l.x = colNum - l.w;
                                }

                                newLayout.push(oldL)
                                newLayout = moveElement(
                                    newLayout,
                                    oldL,
                                    l.x,
                                    l.y,
                                    this.isUserAction,
                                    this.preventCollision,
                                    compactType(this),
                                    colNum,
                                    this.allowOverlap
                                );

                                actions.setCustom(id, data => {
                                    if (data.isFullHeight === undefined) {
                                        data.isFullHeight = true;
                                    }
                                });
                            }
                        });

                        layout = newLayout;

                        layout = compact(layout, compactType(this), colNum);

                        actions.setCustom(id, data => {
                            data.layout = layout;
                        });
                        layout.forEach(item => {
                            actions.setProp(item.i, ($$data) => {
                                $$data.layer = Object.assign($$data.layer || {}, {
                                    w: item.w,
                                    h: item.h,
                                    x: item.x,
                                    y: item.y,
                                });
                            });
                        });

                        this.copyDropItem = null;
                    }
                }
            }).$mount();
        }
    }
};
</script>
<style lang="less" scoped></style>