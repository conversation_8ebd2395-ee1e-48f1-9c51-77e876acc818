<template>
    <div class="picture-component" :class="{'overflow-y-auto': isFullHeight}">
        <div>
            <img src="https://a9.fspage.com/FSR/official-site/img/home-page/banner/banner-crm100.jpg" alt="picture-component">
        </div>
        <div v-if="cText">{{cText}}</div>
        <div><fx-button @click="handleClick">{{ $t('按钮') }}</fx-button></div>
        <div>
            <slot></slot>
        </div>
    </div>
</template>
<script lang="js">
    export default {
        props: ['text', 'isFullHeight'],
        computed: {
            cText() {
                return this.text;
            }
        },

        methods: {
            handleClick() {
                this.$emit('change');
            },

            update() {
                console.log('update');
            }
        },

        beforeDestroy() {
            console.log('组件销毁');
        }
    }
</script>
<style lang="less" scoped>
    .picture-component {
        width: 100%;
        box-sizing: border-box;
        img {
            width: 100%;
            height: 300px;
        }
    }
</style>