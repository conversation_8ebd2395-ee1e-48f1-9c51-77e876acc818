<script>
    import { Vue, getParentVInstanceByType } from '@beecraft/shared';

    const FxCarouselItem = {
        extends: Vue.options.components.FxCarouselItem,

        beecraft: function() {
            return {
                name: 'FxCarouselItem',
                displayName: $t('uipaas.widget.slideimageitem', {}, '轮播面板'),
                $$data: {
                    isCanvas: true,
                    noWrapper: true, // 不需要包装器
                },
                rules: {
                    canDrag: () => false,
                    canSelect: () => false,
                    canDelete: () => false,
                    canCopy: () => false,
                },
                hooks: {
                    beforeSelect(e, node, { query, actions }) {
                        const parentNode = query.node(node.parent).get();

                        if(parentNode.name === 'FxCarousel') {
                            actions.setNodeEvent('selected', [parentNode.id])
                        }
                    }
                }
            }
        },

        created() {
            // 重写$parent，可能存在风险
            this.$parent = getParentVInstanceByType(this, 'FxCarousel')
        }
    }

    export default FxCarouselItem;
</script>
<style lang="less" scoped>
    .el-carousel__item {
        overflow: hidden;
    }
</style>