<template>
    <fx-tooltip
        effect="light"
        placement="right"
        :visible-arrow="false"
        v-model="showOverlay"
        manual
        popper-class="bc-alignment-scale-dropdown"
    >
        <div class="bc-small-grid-container" @click="openOverlay">
            <div
                v-for="(position, index) in positions"
                :key="index"
                class="bc-small-grid-item"
                :title="position.label"
                :class="{highlighted: selectedIndex === index}"
            ></div>
        </div>
        <div slot="content" class="bc-overlay" @mouseleave="closeOverlay">
            <div class="bc-large-grid-container">
                <div
                    v-for="(position, index) in positions"
                    :key="'large-' + index"
                    class="bc-large-grid-item"
                    :title="position.label"
                    :class="{
                        highlighted: selectedIndex === index,
                        hoverHighlight: highlightedIndex === index,
                    }"
                    @mouseover="highlightCell(index)"
                    @click="selectPosition(index)"
                ></div>
            </div>
        </div>
    </fx-tooltip>
</template>

<script>
export default {
    props: {
        value: String,
        default: 'left top',
    },
    data() {
        return {
            positions: [
                {label: '左上', value: 'left top'},
                {label: '中上', value: 'center top'},
                {label: '右上', value: 'right top'},
                {label: '左中', value: 'left center'},
                {label: '居中', value: 'center center'},
                {label: '右中', value: 'right center'},
                {label: '左下', value: 'left bottom'},
                {label: '中下', value: 'center bottom'},
                {label: '右下', value: 'right bottom'},
            ],

            highlightedIndex: null,
            selectedIndex: null,
            showOverlay: false,
        };
    },
    mounted() {
        // 新增部分: 设置默认选中项
        this.setSelectedIndex(this.value);
    },
    watch: {
        // 新增部分: 监听父组件传入的 value 变化
        value(newValue) {
            this.setSelectedIndex(newValue);
        },
    },
    methods: {
        highlightCell(index) {
            this.highlightedIndex = index;
        },
        selectPosition(index) {
            this.selectedIndex = index;
            this.showOverlay = false;
            this.$emit('change', this.positions[index].value);
        },
        openOverlay() {
            this.showOverlay = true;
        },
        closeOverlay() {
            this.highlightedIndex = null;
            this.showOverlay = false;
        },
        setSelectedIndex(value) {
            // 新增方法: 根据传入的 value 设置选中项
            const index = this.positions.findIndex((position) => position.value === value);
            this.selectedIndex = index !== -1 ? index : 0; // 默认选择第一个
        },
    },
};
</script>

<style scoped>
.bc-small-grid-container,
.bc-large-grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
}

.bc-small-grid-container {
    width: 34px;
    height: 34px;
    overflow: hidden;
    border-radius: 2px;
}

.bc-large-grid-container {
    width: 64px;
    height: 64px;
}

.bc-small-grid-item,
.bc-large-grid-item {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-neutrals05, #dee1e8);
    cursor: pointer;
    transition: background-color 0.3s;
}

.bc-small-grid-item.highlighted,
.bc-large-grid-item.highlighted {
    background-color: var(--color-primary06, #ff8000);
}

.bc-large-grid-item.hoverHighlight {
    background-color: var(--color-primary07, #d96500);
}
</style>
<style lang="less">
.bc-alignment-scale-dropdown {
    padding: 12px;
}
</style>
