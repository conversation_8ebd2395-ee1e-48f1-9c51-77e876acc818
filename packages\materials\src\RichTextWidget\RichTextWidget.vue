<template>
    <EditorRenderDoc :node="content"></EditorRenderDoc>
</template>
<script>
const warn = (content) => {
    console.log(
        `%c beecraft %c ${content} `,
        'padding: 2px 1px; border-radius: 3px 0 0 3px; color: #fff; background: #606060; font-weight: bold;font-size: 10px;',
        'padding: 2px 1px; border-radius: 0 3px 3px 0; color: #fff; background: #ff522a; font-weight: bold;font-size: 10px;',
    )
}

export default {
    props: ['content'],
    created() {
        warn('富文本组件提交前还需转换图片路径TNPath->NPath')
    },
    components: {
        'EditorRenderDoc': function () {
            return new Promise(resolve => {
                seajs.use('fs-modules/feed/map', function () {
                    seajs.use('xx-xxvui/sdk.js', function (res) {
                        Vue.options.components.XXVUIEditorRenderDoc().then(res => {
                            resolve(res?.default || {
                                render: h => h('div', this.$t('beecraft.workbench.noWidget', {}, '暂无此组件'))
                            })
                        });
                    })
                });
            })
        }
    }
}
</script>