<template>
    <div class="iamge-item">
        <fx-upload class="image-uploader" :class="{ actived: isActive }" :show-file-list="false"
            :on-success="handleImageSuccess" :on-error="handleImageError" :before-upload="beforeImageUpload"
            accept="image/*" :disabled="!isActive">
            <div class="item-content" :class="{ actived: isActive }">
                <fx-image :src="getImage(image.img)" v-loading="uploading">
                    <div slot="placeholder" class="image-slot" v-loading="true"></div>
                </fx-image>
                <p class="jump-desc">{{ jumpDesc }}</p>
                <div class="hover-mask">
                    <span>{{ $t('更换') }}</span>
                </div>
                <div class="delete-image" @click.stop="remove">
                    <i class="el-icon-delete"></i>
                </div>
            </div>
        </fx-upload>
        <div class="action-wrapper" v-if="isActive">
            <p class="jump-title">{{ $t('图片跳转') }}</p>
            <div class="jump-content">
                <fx-select class="jump-select" size="mini" v-model="value" :options="options"
                    @change="change"></fx-select>
            </div>
            <div class="action-content">
                <div v-if="showLink" class="url">
                    <p class="jump-title">
                        {{ $t('跳转至') }}
                    </p>
                    <fx-input size="mini" :value="jumpLink" :placeholder="$t('请输入http或者https')"
                        @change="updateLink"></fx-input>
                    <p v-if="showErrMsg" class="el-form-item__error error-message">
                        {{ $t('请输入正确的网址') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getImgUrl } from './utils';
const HTTP_REG = /(ht)tp(s?):\/\/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*(:(0-9)*)*(\/?)([a-zA-Z0-9\-.?,'/\\+&amp;%$#_]*)?/;
const ACTION_TYPES = {
    normal: 'none',
    link: 'link',
    module: 'module'
};

export default {
    props: {
        index: Number,
        image: Object,
        isActive: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            value: ACTION_TYPES.normal,
            uploading: false,
            showErrMsg: false,
            options: [
                {
                    value: ACTION_TYPES.normal,
                    label: $t('不跳转'),
                    types: [4]
                },
                {
                    value: ACTION_TYPES.link,
                    label: $t('链接地址'),
                    types: [3]
                }
            ],
            cache: {
                [ACTION_TYPES.link]: null,
                [ACTION_TYPES.module]: null
            }
        }
    },

    computed: {
        jumpDesc() {
            if (this.value === ACTION_TYPES.normal) {
                return $t('不跳转');
            }

            if (this.showLink) {
                return `${$t('跳转至：')}${this.jumpLink}`;
            }

            return `${$t('跳转至：')}${this.jumpModuleName}`;
        },
        showLink() {
            return this.value === ACTION_TYPES.link;
        },
        jumpLink() {
            return this.cache[ACTION_TYPES.link]?.action ?? '';
        },
    },

    methods: {
        getImage(path) {
            return getImgUrl(path, 2);
        },

        updateImage() {
            // this.$refs.uploader.$el.click();
        },

        remove() {
            this.$emit('remove', this.index);
        },

        updateLink(url) {
            const value = {
                action: url,
                menuType: 3
            };
            if (HTTP_REG.test(url)) {
                this.showErrMsg = false;
                this.emitChange(value);
            } else {
                this.showErrMsg = true;
            }
            this.cache[ACTION_TYPES.link] = value;
        },

        updateJumpModule() {

        },

        emitChange(value) {
            this.$emit('change', {
                value,
                index: this.index
            });
        },

        change(type) {
            this.update(type, this.cache[type]);
        },

        update(type, data) {
            if (type === ACTION_TYPES.link) {
                if (data) {
                    this.updateLink(data?.action);
                }
            } else if (type === ACTION_TYPES.module) {
                if (data) {
                    this.updateJumpModule({ props: data });
                }
            } else {
                this.$emit('change', {
                    value: {},
                    index: this.index
                });
            }
        },

        init() {
            const { image } = this;
            const type = this.getTypeByMenuType(image.menuType);

            this.value = type;
            this.update(type, image);
        },

        getTypeByMenuType(type) {
            const res = this.options.find(data => {
                return data.types.includes(type);
            });

            return res?.value || ACTION_TYPES.normal;
        },

        beforeImageUpload() {
            if (!this.isActive) {
                return false;
            }
            this.uploading = true;
        },

        handleImageSuccess(file) {
            this.uploading = false;
            this.emitChange({
                img: file.TempFileName,
                isUpdateImage: true
            });
        }
    },

    created() {
        this.init();
    }
};
</script>
<style lang="less" scoped>
.iamge-item {
    margin-bottom: 16px;

    .item-content {
        position: relative;
        height: 104px;
        line-height: 104px;
        text-align: center;
        border: 1px solid #cccccc;
        cursor: pointer;

        &:hover,
        &.actived {
            border-color: var(--color-primary06);

            .jump-desc {
                display: none;
            }
        }

        &.actived:hover {
            .delete-image {
                display: flex;
            }

            .hover-mask {
                display: block;
            }
        }

        .image-slot,
        .el-image,
        img {
            width: 100%;
            height: 100%;
        }

        .el-loading-spinner {
            margin-top: -35px;
        }
    }

    .jump-desc {
        position: absolute;
        top: 80px;
        right: 0;
        left: 0;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        padding-left: 10px;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
        background-color: rgba(24, 28, 37, 0.8);
    }

    .hover-mask {
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.1);
        user-select: none;
        cursor: move;
    }

    .delete-image {
        display: none;
        position: absolute;
        width: 40px;
        height: 32px;
        top: 0;
        right: 0;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        z-index: 2;
        background-color: hsla(0, 0%, 100%, 0.6);
        cursor: pointer;
    }

    .action-content {
        position: relative;

        .module {
            p {
                display: inline-block;
            }

            .btn {
                // color: var(--color-primary06);
                cursor: pointer;
            }
        }
    }

    .jump-title {
        padding: 8px 0px;
        line-height: normal;
        font-size: 12px;
        color: #545861;

        &.module {
            display: inline-block;
        }
    }

    .jump-select {
        width: 100%;
    }

    .error-message {
        padding-top: 2px;
    }
}

.image-uploader {
    width: 100%;

    /deep/.fx-upload {
        width: 100%;
    }
}
</style>
