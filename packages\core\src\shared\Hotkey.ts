import hotkeys from 'hotkeys-js';

export default class Hotkey {

    private directMap: Record<string, Function[]> = {};
    
    bind(combos: string[] | string, callback: Function) {
        const { directMap } = this;

        combos = Array.isArray(combos) ? combos : [combos];
        combos.forEach(combo => {
            if(!directMap[combo]) {
                directMap[combo] = [];
                hotkeys(combo, function(...args) {
                    directMap[combo].forEach(callback => callback(...args));
                });
            }
            directMap[combo].push(callback);
        });
        return this;
    }

    unbind(combos: string[] | string, callback?: Function) {
        const { directMap } = this;

        combos = Array.isArray(combos) ? combos : [combos];
        combos.forEach(combo => {
            if(!directMap[combo]) {
                return;
            }
            if(!callback) {

            }
            const idx = directMap[combo].findIndex(callback => callback);
            
            if(idx !== -1) {
                directMap[combo].splice(idx, 1);
                hotkeys.unbind(combo, callback as any);
            }
        });
        return this;
    }

    active() {
        const { directMap } = this;

        Object.keys(directMap).forEach(combo => {
            hotkeys(combo, function(...args) {
                directMap[combo].forEach(callback => callback(...args));
            });
        });
    }

    unactive() {
        hotkeys.unbind();
    }

}