<template>
    <div>
        <XXVUIEditor class="uipaas-rich-text-widget" ref="editor" :content="content" :editable="true"
            :extensionsConfig="extensionsConfig" v-on:update="update"></XXVUIEditor>
    </div>
</template>
<script>
export default {
    inject: ['useInternalNode','useInternalEditor'],

    data() {
        const { content } = this.useInternalNode((node) => {
            return {
                content: node.data.content
            }
        })
        return {
            content: {
                __xt: {
                    __json: content || {
                        type: "doc",
                        content: []
                    }
                }
            },
            extensionsConfig: {
                at: false,
                topic: false,
                paasobj: false,
            }
        }
    },

    methods: {
        update() {
            const { actions } = this.useInternalNode();

            actions.setCustom((data) => {
                data.content = this.$refs.editor?.getContent().__json;
            })
        },
    },
    created(){
        const { query } = this.useInternalEditor();
        const extensionsConfig = query.getOptions().node.$$data?.richtextConfig?.extensionsConfig || {};
        if(Object.keys(extensionsConfig).length){
            this.extensionsConfig = Object.assign({}, this.extensionsConfig, extensionsConfig);
        }
    },
}
</script>
<style lang="less" scoped></style>