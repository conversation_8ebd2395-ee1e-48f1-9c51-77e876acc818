/* eslint-disable */
import * as path from 'path';
import checker from 'vite-plugin-checker';
import legacyPlugin from '@vitejs/plugin-legacy';
import { createVuePlugin } from 'vite-plugin-vue2';
import vue2Jsx from './scripts/vite-plugin-vue2-jsx';
import lessLoader from './scripts/vite-less-resources-loader';
import { viteExternalsPlugin } from 'vite-plugin-externals';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'


export default ({ command, mode }) => {
    let rollupOptions = {};

    let optimizeDeps = {
        exclude: ['node_modules']
    };

    let alias = {
        '@beecraft/core': path.resolve(__dirname, '../packages/core/src'),
        '@beecraft/shared': path.resolve(__dirname, '../packages/shared/src'),
        '@beecraft/workbench': path.resolve(__dirname, '../packages/workbench/src'),
        '@beecraft/materials': path.resolve(__dirname, '../packages/materials/src'),
        '@beecraft/fxui': path.resolve(__dirname, '../packages/fxui/src'),
        '@beecraft/setters': path.resolve(__dirname, '../packages/setters/src'),
        '@beecraft/engine': path.resolve(__dirname, '../packages/engine/src'),
    };

    let proxy = {
        // '^/test.html': {
        //     configure: (proxy, _options) => {
        //         proxy.on('proxyRes', (proxyRes, req, res) => {

        //             // res.removeHeader("Access-Control-Allow-Origin")
        //             // res.removeHeader("access-control-allow-origin")
        //             // res.setHeader("Access-Control-Allow-Origin", "*")
        //             res.setHeader("Content-Security-Policy", "frame-ancestors https://*.fxiaokeyun.com/")

        //             proxyRes.pipe(res)
        //         });
        //     }
        // }
    };

    let define = {
        'process.env.NODE_ENV': command === 'serve' ? '"development"' : '"production"',
        'seajsRequire': 'window.require',
    };

    let esbuild = {};

    return {
        base: '.', // index.html文件所在位置
        root: '.', // js导入的资源路径，src
        resolve: {
            alias,
        },
        define: define,
        server: {
            // 代理
            proxy,
            host: "0.0.0.0",
            hmr: false,
            headers: {
                'Content-Security-Policy': 'frame-ancestors https://*.fxiaokeyun.com/ file://*'
            }
        },
        build: {
            target: 'es2015',
            minify: 'terser', // 是否进行压缩,boolean | 'terser' | 'esbuild',默认使用terser
            manifest: false, // 是否产出maifest.json
            sourcemap: false, // 是否产出soucemap.json
            outDir: 'build', // 产出目录
            rollupOptions,
        },
        esbuild,
        optimizeDeps,
        plugins: [
            checker({ typescript: true }),
            vue2Jsx({}),
            createVuePlugin({
                jsx: true,
            }),
            legacyPlugin({
                targets: ['> 1%', 'last 2 versions', 'Firefox ESR', 'not ie <= 10'],
            }),
            viteExternalsPlugin({
                vue: 'Vue',
            }),
            cssInjectedByJsPlugin()
        ],
        css: {
            preprocessorOptions: {
                less: {
                    // 支持内联 JavaScript
                    javascriptEnabled: true,
                    plugins: [
                        // new lessLoader(['../packages/StyleVariables.less'])
                        new lessLoader([])
                    ],
                },
            },
        },
    }
}