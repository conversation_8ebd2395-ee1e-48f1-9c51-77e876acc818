<template>
    <div class="bc-layout-row d-flex">
        <slot></slot>
    </div>
</template>
<script lang="js">
    export default {
        beecraft: function () {
            return {
                name: 'LayoutRow',
                $$data: {
                    noWrapper: true,
                    style: {
                        margin: 0,
                        padding: 0
                    }
                },
                
            }
        }
    }
</script>
<style lang="less" scoped>
    .bc-layout-row {
        border-bottom: 1px solid #DEE1E8;
    }
    .bc-layout-row:last-child {
        border-bottom: unset;
    }
</style>