import SlideGridLayout from './SlideGridLayout.vue';

SlideGridLayout.beecraft = function () {
    return {
        name: 'SlideGridLayout',
        displayName: $t('轮播栅格布局容器'),
        data: {
            isDroppable: true,
            layout: []
        },
        $$data: {
            isCanvas: true,
            noWrapper: true,
        },
        rules: {
            canSelect: () => false,
            canHover: () => false
        },
        hooks: {
            beforeDragOver(){
                return false;
            }
        }
    }
}

export {
    SlideGridLayout,
}