function isGridLayout(name) {
    return name === 'GridLayout' || name === 'SlideGridLayout';
}

function findNonLeafNodes(root) {
    let result = [];
    function traverse(node) {
        if (node === null) return;
        if (node.children?.length) {
            result.push(node.name);
            node.children.forEach((child) => {
                traverse(child)
            })
        }
    }
    traverse(root);
    return result.slice(1);
}

function findLeafNodes(root) {
    let result = [];
    function traverse(node) {
        if (node === null) return;
        if (node.children?.length) {
            node.children.forEach((child) => {
                traverse(child)
            })
        }else {
            result.push(node.id);
        }
    }
    traverse(root);
    return result;
}

/**
 * @module engine/plugins/BuiltinComponentLibs
 * @desc 内置布局主题
 */
export default class FxiaokeComponentLibs {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        let _temp = [];
        let _done = {};
        config.set('node.$$data.style', function (node, { query }) {
            // 根节点
            if (!node.parent) {
                return {
                    'height': '100%',
                }
            }
            if (node.name === 'Layout') {
                return {
                    'height': '100%',
                    'box-sizing': 'border-box',
                    'border': '0.5px solid transparent',
                    'background-color': '#FAFAFA',
                }
            }
            if (node.$$data.template) {
                if (!_done[node.name]) {
                    _temp = _temp.concat(findNonLeafNodes(node.$$data.template));
                    _done[node.name] = 1;
                }
                return {
                    'margin': '8px'
                }
            }
            if (_temp.indexOf(node.name) > -1 || node.$$data.templateAncestors) {
                return {};
            }
            if (node.$$data.isCanvas) {
                return {
                    'padding': '8px'
                };
            }
            if (node.$$data.noWrapper) {
                return {
                    'margin': '8px',
                    'background': '#fff'
                }
            }
            return {
                'margin': isGridLayout(query.node(node.parent).get()) ? '8px' : '',
                'padding': '16px',
                'background': '#fff'
            }
        });
    }
}