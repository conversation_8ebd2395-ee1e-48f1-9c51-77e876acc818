<template>
  <div class="bc-setter-component-spacing">
    <fx-input-number class="bc-setter-component-spacing-input-number" v-model="localValue.num" :min="0" :max="32"
      size="mini" @change="handleChange">
    </fx-input-number>
    <fx-select class="bc-setter-component-spacing-select" v-model="localValue.unit" :options="options" size="mini"
      @change="handleChange"></fx-select>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Object,
      default: () => ({
        gap: '10px',
      }),
    },
  },
  data() {
    return {
      localValue: this.initializeLocalValue(),
      options: [
        {
          value: 'px',
          label: 'PX',
        },
        {
          value: 'rem',
          label: 'REM',
        },
        {
          value: '%',
          label: '%',
        },
      ],
    };
  },
  methods: {
    initializeLocalValue() {
      console.log('gap', this.value.gap);
      // 使用正则表达式来匹配数值和单位
      const gapRegex = /^(\d*\.?\d+)([a-z%]*)$/i;
      const match = this.value?.gap?.match(gapRegex);

      if (match) {
        return {
          num: parseFloat(match[1]),
          unit: match[2] || 'PX',
        };
      } else {
        return {
          num: 0,
          unit: 'PX',
        };
      }
    },
    handleChange() {
      const value = this.localValue.num + this.localValue.unit
      this.$emit('change', { gap: value });
    },
  },
};
</script>
<style lang="less" scoped>
.bc-setter-component-spacing {
  display: flex;
  justify-content: space-between;
  gap: 8px;

  .bc-setter-component-spacing-input-number {
    flex: 2;
  }

  .bc-setter-component-spacing-select {
    flex: 1;
  }
}
</style>