import Vue from 'vue';
import {
    NodeId,
    Nodes,
    NodeTree,
    Node,
    NodeMap,
    SerializedNode,
    Options,
    EditorState,
    NodeSelector,
    NodeEventTypes,
    NodeSelectorType,
} from '../interfaces';
import invariant from 'tiny-invariant';
import { getNodesFromSelector } from '../shared/getNodesFromSelector';


const methods = (
    state: EditorState,
    query: any
) => {
    const removeNodeFromEvents = (state: EditorState, nodeId: NodeId) => {
        const events = state.events as any;

        Object.keys(events).forEach((key) => {
            const eventSet = events[key];

            if (eventSet && eventSet.has && eventSet.has(nodeId)) {
                events[key] = new Set(Array.from(eventSet).filter((id) => nodeId !== id));
            }
        })
    }

    const deleteNode = (id: NodeId) => {
        const { nodes } = state;

        const targetNode = nodes[id];
        const parentNode = nodes[targetNode.parent];
        
        // const nodeHelper = query.node(id);
        // const oNode = nodeHelper.get();

        // nodeHelper.callHook('beforeDelete'); // 不支持拦截

        if (targetNode.children) {
            [...targetNode.children].forEach((id) => deleteNode(id));
        }

        const childIndex = parentNode.children?.findIndex(nodeId => nodeId === id) as number;

        if (childIndex > -1) {
            const parentChildren = parentNode.children;
            parentChildren?.splice(childIndex, 1);
        }

        removeNodeFromEvents(state, id);
        delete nodes[id];

        //TODO 特殊支持节点删除钩子
        // nodeHelper.callHook('deleted', oNode, { query });
        // nodeHelper.callHook('deleted');
    };

    const addNodeTreeToParent = (
        tree: NodeTree,
        parentId?: NodeId,
        index?: number
    ) => {
        if (!parentId) {
            invariant(
                parentId,
                'Cannot add non-root Node without a parent'
            );
      
            return;
        }

        const iterateChildren = (id: NodeId, parentId: NodeId = '') => {
            const node = tree.nodes[id];

            node.parent = parentId;
            Vue.set(state.nodes, id, node);

            node.children.forEach(childNodeId => {
                iterateChildren(childNodeId, id);
            });
        }

        iterateChildren(tree.rootNodeId, parentId);

        const parent = state.nodes[parentId];

        parent.children.splice(index ?? parent.children.length, 0, tree.rootNodeId);
    }

    const parseSerializedNodes = (serializedNodes: SerializedNode[], parentId?: NodeId, normalize?: (Node) => void) => {

        if(typeof parentId === 'function') {
            normalize = parentId;
            parentId = undefined;
        }
        
        function parseNode(nodeTree: NodeTree, serializedNode: SerializedNode, parentId?: NodeId) {
            const node = query.parseSerializedNode(serializedNode).toNode((node: Node) => {
                node.parent = parentId as string;
                node.children = serializedNode.children?.map(child => parseNode(nodeTree, child, node.id)) || [];
                if(!normalize) {
                    normalize = state.options.normalizeNode;
                }
                if(normalize) {
                    normalize(node);
                }
            });
            
            nodeTree.nodes[node.id] = node;
            nodeTree.rootNodeId = node.id;

            return node.id;
        }
        
        return serializedNodes.map(serializedNode => {
            const nodeTree = {
                nodes: {},
                rootNodeId: ''
            };
            
            parseNode(nodeTree, serializedNode, parentId);

            return nodeTree;
        });
    }

    return {

        add(nodeToAdd: any | any[], parentId: NodeId, index?: number) {
            let nodes = Array.isArray(nodeToAdd) ? nodeToAdd : [nodeToAdd];

            return nodes.map((node) => {
                const nodeTree = node.id && node._done
                    ? { nodes: { [node.id]: node }, rootNodeId: node.id }
                    : parseSerializedNodes([node])[0];

                addNodeTreeToParent(nodeTree, parentId, index);
                return nodeTree.rootNodeId;
            });
        },

        addNodeTree(tree: NodeTree, parentId?: NodeId, index?: number) {
            addNodeTreeToParent(tree, parentId, index);
        },

        delete(selector: NodeSelector<NodeSelectorType.Id>, isForce) {
            if(!Array.isArray(selector)) {
                selector = [selector];
            }
            const { nodes } = state;
            const targets = getNodesFromSelector(nodes, selector, {
                existOnly: true,
                idOnly: true,
            });

            targets.forEach(({ node }) => {
                // invariant(
                //     !query.node(node.id).isTopLevelNode(),
                //     ERROR_DELETE_TOP_LEVEL_NODE
                // );

                if(isForce || query.node(node.id).isDeletable()) {
                    deleteNode(node.id);
                }
            });
        },

        /**
         * 替换节点
         * @param nodeToAdd 
         * @param targetId 
         * @todo 未处理node为已经是格式化之后的数据结构的情况
         */
        replace(nodeToAdd: any, targetId: NodeId) {
            const nodeHelper = query.node(targetId);
            const targetNode = nodeHelper.get();

            this.delete([targetId]);
            this.add(nodeToAdd, targetNode.parent, nodeHelper.index);
        },

        parseSerializedNodes: parseSerializedNodes,

        deserialize(input: string | SerializedNode[], normalize?: (Node) => void) {
            const dehydratedNodes =
                typeof input == 'string' ? JSON.parse(input) : input;

            if(!dehydratedNodes.length) {
                return;
            }

            const nodeTrees = parseSerializedNodes(dehydratedNodes, undefined, normalize);

            // todo 只考虑单个情况
            this.replaceNodes(nodeTrees[0].nodes);
        },

        /**
         * 移动节点
         * @param selector 
         * @param newParentId 
         * @param index 
         * @param isForce 是否强制执行
         */
        move(selector: NodeSelector, newParentId: NodeId, index: number, isForce) {
            if(!Array.isArray(selector)) {
                selector = [selector];
            }
            
            const { nodes } = state;

            const targets = getNodesFromSelector(nodes, selector, {
                existOnly: true,
            });

            const newParent = nodes[newParentId];

            const nodesArrToCleanup = new Set<NodeId[]>();

            targets.forEach(({ node: targetNode }, i) => {
                const targetId = targetNode.id;
                const currentParentId = targetNode.parent;

                if(isForce !== true) {
                    query.node(newParentId).isDroppable([targetId], (err: any) => {
                        throw new Error(err);
                    });
                }

                const currentParent = nodes[currentParentId];
                const currentParentNodeIds = currentParent.children;

                nodesArrToCleanup.add(currentParentNodeIds);

                const oldIndex = currentParentNodeIds.findIndex(id => id === targetId);
                currentParentNodeIds[oldIndex] = '$$';

                newParent.children.splice(index + i, 0, targetId);

                nodes[targetId].parent = newParentId;
            });

            nodesArrToCleanup.forEach((nodes) => {
                const length = nodes.length;

                [...nodes].reverse().forEach((value, index) => {
                    if (value as unknown as string !== '$$') {
                        return;
                    }

                    nodes.splice(length - 1 - index, 1);
                });
            });
        },

        /**
         * 替换节点
         */
        replaceNodes(nodes: NodeMap) {
            this.clearEvents();
            state.nodes = nodes;
        },

        /**
         * 清除事件
         */
        clearEvents() {
            this.setNodeEvent('selected', null);
            this.setNodeEvent('hovered', null);
            this.setNodeEvent('dragged', null);
            this.setIndicator(null);
        },

        /**
         * 重置设计器状态
         */
        reset() {
            this.clearEvents();
            this.replaceNodes({});
        },

        /**
         * 设置设计器配置
         */
        setOptions(cb: (options: Partial<Options>) => void) {
            cb(state.options);
        },

        /**
         * 添加组件实例
         * @param nodeId 
         * @param vm 
         */
        setInstance(nodeId: NodeId, vm: Vue) {
            const { instances } = state;

            // if (!instances[nodeId]) {
                instances[nodeId] = vm;
            // }
        },

        /**
         * 设置节点的事件状态
         */
        setNodeEvent(
            eventType: NodeEventTypes,
            nodeIdSelector: NodeSelector<NodeSelectorType.Id> | null
        ) {
            const {
                nodes,
                events
            } = state;

            events[eventType].forEach((id) => {
                if (nodes[id]) {
                    nodes[id].events[eventType] = false;
                }
            });

            events[eventType] = new Set();

            if (!nodeIdSelector) {
                return;
            }

            const targets = getNodesFromSelector(nodes, nodeIdSelector, {
                idOnly: true,
                existOnly: true,
            });

            const nodeIds: Set<NodeId> = new Set(targets.map((target) => target.node.id));

            nodeIds.forEach((id: NodeId) => {
                nodes[id].events[eventType] = true;
            });

            state.events[eventType] = nodeIds;
        },

        /**
         * 设置节点的DOM元素
         */
        setDOM(id: NodeId, dom: HTMLElement) {
            const node = state.nodes[id];

            if (!node) {
                return;
            }

            node.dom = dom;
        },

        /**
         * 设置指针
         * @param indicator 
         */
        setIndicator(indicator: any | null) {
            if (
                indicator &&
                (!indicator.placement.parent.dom ||
                    (indicator.placement.currentNode &&
                        !indicator.placement.currentNode.dom))
            ) {
                return;
            }

            state.indicator = indicator;
        },

        /**
         * 更新节点$$data属性
         */
        setProp(
            selector: NodeSelector<NodeSelectorType.Id>,
            cb: (props: any) => void
        ) {
            if(!Array.isArray(selector)) {
                selector = [selector];
            }

            const { nodes } = state;

            const targets = getNodesFromSelector(nodes, selector, {
                idOnly: true,
                existOnly: true,
            });

            targets.forEach(({ node }) => cb(nodes[node.id].$$data));
        },

        /**
         * 更新节点data属性
         */
        setCustom(
            selector: NodeSelector<NodeSelectorType.Id>,
            cb: (props: any) => void
        ) {
            if(!Array.isArray(selector)) {
                selector = [selector];
            }

            const { nodes } = state;

            const targets = getNodesFromSelector(nodes, selector, {
                idOnly: true,
                existOnly: true,
            });

            targets.forEach(({ node }) => cb(nodes[node.id].data));
        },

        /**
         * 隐藏节点
         */
        setHidden(id: NodeId, bool: boolean) {
            // state.nodes[id].$$data.hidden = bool;
        },

        /**
         * 选择节点
         */
        selectNode(nodeIdSelector?: NodeSelector<NodeSelectorType.Id>) {
            if (nodeIdSelector) {
                const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {
                    idOnly: true,
                    existOnly: true,
                });

                this.setNodeEvent(
                    'selected',
                    targets.map(({ node }) => node.id)
                );
            } else {
                this.setNodeEvent(
                    'selected',
                    null
                );
            }

            this.setNodeEvent('hovered', null);
        }
    }
}

export const ActionMethods = (
    state: EditorState,
    query: any
) => {
    return {
        ...methods(state, query),
        setState(
            cb: (
                state: EditorState,
                actions: any
            ) => void
        ) {
            const { history, ...actions } = this as any;

            cb(state, actions);
        }
    }
}