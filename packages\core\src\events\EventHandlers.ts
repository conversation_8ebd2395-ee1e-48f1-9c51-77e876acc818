import {
    Editor,
    NodeId,
    NodeSelector,
    NodeSelectorType,
    RegisteredConnector,
    DragTarget
} from '../interfaces';
import ConnectorRegistry from '../events/ConnectorRegistry';
import { createShadow } from './createShadow';
import Positioner from './Positioner';
import { createNodeFromVNode } from '../nodes/createNodeFromVNode';
import { isEventBlockedByDescendant } from './isEventBlockedByDescendant';

/**
 * 事件处理器
 */

export default class EventHandlers<O extends Record<string, any> = {}> {

    options: O;

    draggedElementShadow: HTMLElement | null = null;

    dragTarget: DragTarget | null = null;

    positioner: Positioner | null = null;

    private registry: ConnectorRegistry = new ConnectorRegistry();


    constructor(options: O) {
        this.options = options;
    }

    /**
     * 事件处理程序的函数
     */
    handlers() {
        const store = this.options.store;

        const handler = {
            // 没有按照原来的去开发
            connect: (el: HTMLElement, id: NodeId) => {
                store.actions.setDOM(id, el);

                const connectorsToCleanup: any[] = [];

                connectorsToCleanup.push(handler.select(el, id));
                connectorsToCleanup.push(handler.hover(el, id));
                connectorsToCleanup.push(handler.drop(el, id));

                return () => {
                    connectorsToCleanup.forEach((cleanup: Function) => {
                        cleanup();
                    })
                }
            },

            /**
             * todo options类型不对
             */
            create: (el: HTMLElement, options: any, handlers: any = {}) => {
            // create: (el: HTMLElement, schema: any, options: any, handlers: any = {}) => {
                el.setAttribute('draggable', 'true');

                const unbindDragStart = this.addCraftEventListener(
                    el,
                    'dragstart',
                    (e: Event) => {
                        if (!options.onDragStart) {
                            options.onDragStart = function () {
                                return Promise.resolve();
                            };
                        }

                        // @ts-ignore
                        // mac电脑下拖拽鼠标释放后，会出现元素回到原位置的视觉效果。以下方式可以避免该问题的发生
                        (e as any).dataTransfer.dropEffect = 'copy';

                        options.onDragStart().then((result: any = {}) => {
                            const { actions, query } = store;

                            const schema = result.schema;

                            console.log('shcema', schema);

                            e.stopPropagation();
                            const nodeTree = actions.parseSerializedNodes([schema])[0];

                            if (!nodeTree.rootNodeId) {
                                return;
                            }

                            actions.clearEvents();

                            this.dragTarget = {
                                type: 'new',
                                tree: nodeTree
                            };

                            this.positioner = new Positioner(
                                store,
                                this.dragTarget
                            );
                        })
                    }
                );

                const unbindDragEnd = this.addCraftEventListener(
                    el,
                    'dragend',
                    (e: Event) => {
                        const { actions } = store;

                        (e as any).craft.stopPropagation();

                        this.dropElement((dragTarget, indicator) => {
                            if (!dragTarget) {
                                return;
                            }
                            if (dragTarget.type === 'existing') {
                                return;
                            }

                            const index =
                                indicator.placement.index +
                                (indicator.placement.where === 'after' ? 1 : 0);

                            actions.addNodeTree(dragTarget.tree, indicator.placement.parent.id, index);
                            actions.setNodeEvent('selected', [(dragTarget.tree as any).rootNodeId]);

                            options?.onCreate?.(dragTarget.tree);
                        });
                    }
                );

                return () => {
                    el.removeAttribute('draggable');
                    unbindDragStart();
                    unbindDragEnd();
                }
            },

            select: (el: HTMLElement, id: NodeId) => {
                const unbindOnClick = this.addCraftEventListener(
                    el,
                    'click',
                    (e: Event) => {
                        (e as any).craft.stopPropagation();

                        const { query, actions } = store;

                        const enabled = query.getOptions().enabled;

                        if (!enabled) {
                            return;
                        }

                        const nodeAccessor = query.node(id);

                        if (
                            nodeAccessor.callHook('beforeSelect', e, nodeAccessor.get(), store) === false ||
                            !nodeAccessor.isSelectable()
                        ) {
                            return;
                        }

                        let newSelectedElementIds: NodeSelector<NodeSelectorType.Id> = query.getEvent('selected').all();

                        newSelectedElementIds = [id];

                        actions.setNodeEvent('selected', newSelectedElementIds);

                        // todo 此处处理多选
                    }
                );

                return () => {
                    unbindOnClick();
                }
            },

            hover: (el: HTMLElement, id: NodeId) => {
                const unbindMouseover = this.addCraftEventListener(
                    el,
                    'mouseover',
                    (e: Event) => {
                        (e as any).craft.stopPropagation();

                        const { query } = store;
                        const nodeAccessor = query.node(id);

                        let relateId = '';

                        // 当鼠标悬停在组件的操作栏上，若该操作栏属于已悬停的组件，将保持悬停状态不变，如果不属于，则进行清空。
                        const closestBar = (e as any).target.closest(`[data-relate-id]`);
                        if (closestBar) {
                            if (query.getEvent('hovered').contains(closestBar.dataset.relateId)) {
                                return true;
                            } else {
                                relateId = closestBar.dataset.relateId;
                            }
                        }

                        if (
                            nodeAccessor.callHook('beforeHover', e, nodeAccessor.get(), store) === false ||
                            !nodeAccessor.isHoverable()
                        ) {
                            return;
                        }

                        store.actions.setNodeEvent('hovered', [relateId || id]);
                    }
                );

                const unbindMouseleave = this.addCraftEventListener(
                    el,
                    'mouseleave',
                    (e: Event) => {
                        // (e as any).craft.stopPropagation();
                        // store.actions.setNodeEvent('hovered', null);
                    }
                );

                return () => {
                    unbindMouseover();
                    unbindMouseleave();
                };
            },

            drag: (el: HTMLElement, id: NodeId) => {

                if (!store.query.node(id)?.isDraggable()) {
                    return () => { };
                }

                el.setAttribute('draggable', 'true');

                const unbindDragStart = this.addCraftEventListener(
                    el,
                    'dragstart',
                    (e: DragEvent) => {
                        // console.log('dragstart');
                        (e as any).craft.stopPropagation();
                        // e.preventDefault();
                        // e.stopPropagation();
                        // e.preventDefault(); //部分内置元素（img）的拖拽会有默认行为，所以此处需要阻止


                        const { query, actions } = store;
                        let selectedElementIds: NodeSelector<NodeSelectorType.Id> = query.getEvent('selected').all();

                        if (!selectedElementIds.includes(id)) {
                            if (false) {
                                selectedElementIds = [...selectedElementIds, id];
                            } else {
                                selectedElementIds = [id];
                            }
                            // actions.setNodeEvent('selected', selectedElementIds);
                        }

                        actions.clearEvents();
                        actions.setNodeEvent('dragged', selectedElementIds);

                        const selectedDOMs = [el];

                        // this.draggedElementShadow = createShadow(
                        //     e,
                        //     selectedDOMs,
                        //     true
                        // );

                        this.dragTarget = {
                            type: 'existing',
                            nodes: selectedElementIds,
                        };

                        this.positioner = new Positioner(
                            store,
                            this.dragTarget
                        );

                        // if (store.query.getOptions().onDragStart?.(e, el, id, store, this)) {
                        //     return;
                        // }

                    }
                );

                const unbindDragEnd = this.addCraftEventListener(
                    el,
                    'dragend',
                    (e: DragEvent) => {
                        console.log('dragend');
                        const { actions } = store;

                        (e as any).craft.stopPropagation();

                        this.dropElement((dragTarget, indicator) => {

                            // if (store.query.getOptions().onDragEnd?.(e, el, id, store, this)) {
                            //     return;
                            // }
                            if (!dragTarget) {
                                return;
                            }

                            if (dragTarget.type === 'new') {
                                return;
                            }

                            const index =
                                indicator.placement.index +
                                (indicator.placement.where === 'after' ? 1 : 0);

                            actions.move(
                                (dragTarget as any).nodes,
                                indicator.placement.parent.id,
                                index
                            );
                            actions.setNodeEvent('selected', dragTarget.nodes);
                        })
                    }
                );

                return () => {
                    el.setAttribute('draggable', 'false');

                    unbindDragStart();
                    unbindDragEnd();
                }
            },

            drop: (el: HTMLElement, id: NodeId) => {
                const unbindDragOver = this.addCraftEventListener(
                    el,
                    'dragover',
                    (e: DragEvent) => {
                        const { query } = store;
                        const nodeAccessor = query.node(id);

                        if (
                            !this.positioner ||
                            nodeAccessor.callHook('beforeDragOver', e, this.positioner.dragTarget, nodeAccessor.get(), store) === false
                        ) {
                            // 如果返回了false，则意味不需要再计算指针位置
                            store.actions.setIndicator(null);
                            this.positioner?.clearIndicator();
                            return;
                        }

                        (e as any).craft.stopPropagation();
                        e.preventDefault();

                        const indicator = this.positioner.computeIndicator(
                            id,
                            e.clientX,
                            e.clientY
                        );

                        if (!indicator) {
                            return;
                        }

                        store.actions.setIndicator(indicator);
                    }
                );

                const unbindDragEnter = this.addCraftEventListener(
                    el,
                    'dragenter',
                    (e: DragEvent) => {
                        const { query } = store;
                        const nodeAccessor = query.node(id);

                        if (
                            !this.positioner ||
                            nodeAccessor.callHook('beforeDragEnter', e, this.positioner.dragTarget, nodeAccessor.get(), store) === false
                        ) {
                            return;
                        }

                        (e as any).craft.stopPropagation();
                        e.preventDefault();
                    }
                );

                const unbindDragLeave = this.addCraftEventListener(
                    el,
                    'dragleave',
                    (e: DragEvent) => {
                        const { query } = store;
                        const nodeAccessor = query.node(id);

                        if (
                            !this.positioner ||
                            nodeAccessor.callHook('beforeDragLeave', e, this.positioner.dragTarget, nodeAccessor.get(), store) === false
                        ) {
                            return;
                        }

                        (e as any).craft.stopPropagation();
                        e.preventDefault();
                    }
                );

                const unbindDrop = this.addCraftEventListener(
                    el,
                    'drop',
                    (e: DragEvent) => {
                        const { query } = store;
                        const nodeAccessor = query.node(id);

                        // todo 的执行顺序
                        if (
                            !this.positioner ||
                            nodeAccessor.callHook('beforeDrop', e, this.positioner.dragTarget, nodeAccessor.get(), store) === false
                        ) {
                            return;
                        }

                        (e as any).craft.stopPropagation();
                        e.preventDefault();
                    }
                );

                return () => {
                    unbindDragOver();
                    unbindDragEnter();
                    unbindDragLeave();
                    unbindDrop();
                }
            },
        }

        return handler;
    }

    /**
     * 绑定DOM事件
     */
    addCraftEventListener(el: HTMLElement, eventName: string, listener: Function, options?: any) {
        const bindedListener = (e: any) => {
            // listener(e);
            if (!isEventBlockedByDescendant(e, eventName, el)) {
                e.craft.stopPropagation = () => {
                    if (!e.craft.blockedEvents[eventName]) {
                        e.craft.blockedEvents[eventName] = [];
                    }

                    e.craft.blockedEvents[eventName].push(el);
                }

                listener(e);
            }
        };

        el.addEventListener(eventName, bindedListener, options);

        return () => el.removeEventListener(eventName, bindedListener, options);
    }

    /**
     * 创建管控类的连接器
     */
    createConnectorsUsage() {
        const handlers = this.handlers();

        const activeConnectorIds: Set<string> = new Set();
        let canRegisterConnectors = false;

        const connectorsToRegister: Map<
            string,
            () => RegisteredConnector
        > = new Map();

        const connectors = Object.entries(handlers).reduce((accum, [name, handler]) => {
            return {
                ...accum,
                [name]: (el: HTMLElement, id: NodeId, options: any) => {
                    const registerConnector = () => {
                        const connector = this.registry.register(el, {
                            required: id,
                            name,
                            options,
                            connector: handler as any
                        });

                        activeConnectorIds.add(connector.id);

                        return connector;
                    };

                    connectorsToRegister.set(
                        this.registry.getConnectorId(el, name),
                        registerConnector
                    );

                    if (canRegisterConnectors) {
                        registerConnector();
                    }

                    return el;
                }
            }
        }, {});

        return {
            connectors,
            register: () => {
                canRegisterConnectors = true;
                connectorsToRegister.forEach((registerConnector) => {
                    registerConnector();
                });
            },
            cleanup: () => {
                canRegisterConnectors = false;
                activeConnectorIds.forEach((connectorId) => {
                    this.registry.remove(connectorId)
                });
            }
        }
    }

    /**
     * 启用事件
     */
    enable(): void {
        this.registry.enable();
    }

    /**
     * 禁用事件
     */
    disable(): void {
        this.registry.disable();
    }

    /**
     * todo placement类型不对
     */
    private dropElement(
        onDropNode: (dragTarget: DragTarget, placement: any) => void
    ) {
        const store = this.options.store;

        if (!this.positioner) {
            return;
        }

        const draggedElementShadow = this.draggedElementShadow;

        const indicator = this.positioner.getIndicator();

        if (this.dragTarget && indicator && !indicator.error) {
            onDropNode(this.dragTarget, indicator);
        }

        if (draggedElementShadow) {
            // draggedElementShadow.parentNode.removeChild(draggedElementShadow);
            this.draggedElementShadow = null;
        }

        this.dragTarget = null;

        store.actions.setIndicator(null);
        store.actions.setNodeEvent('dragged', null);

        this.positioner.cleanup();
        this.positioner = null;
    }

}