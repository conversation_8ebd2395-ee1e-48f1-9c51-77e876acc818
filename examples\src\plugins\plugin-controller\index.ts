/**
 * @desc 控制器示例
 * 
 * 该示例说明两个组件是如何建立交互关系的
 */

class ControllerDemo{

    apply() {
        return [{
            event: 'beecraft.node.render.after',
            functional: this.nodeRenderAfter.bind(this)
        }]
    }

    nodeRenderAfter(service, { id, query }) {
        const { mediator } = query.getOptions();

        mediator.$on('GridLayout.item-resized', function() {
            console.log('item-resized', arguments);
        })
        mediator.$on('GridLayout.container-resized', function() {
            console.log('container-resized', arguments);
        })
        // const node = query.node(id).get();
        
        // if (node.name === 'Demo') {
        //     const vm = query.instance(id);

        //     vm.$on('change', (...args) => {
        //         mediator.$emit(`${node.name}.change`, ...args);
        //         mediator.$emit(`${node.id}.change`, ...args);
        //     });

        //     mediator.$on('Demo.change', () => {
        //         vm.update();
        //     });
        // }
    }
}

export default ControllerDemo;