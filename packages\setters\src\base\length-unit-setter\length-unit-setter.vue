<template>
    <div class="length-unit-setter-wrapper">
        <fx-input
            type="number"
            isPositiveNum
            :placeholder="$t('请输入')"
            :size="size"
            v-bind="$attrs"
            v-model="unitValue"
            @change="onLengthChange"
        >
            <div class="length-unit-suffix" slot="append">
                <fx-select
                    v-if="unitOptions.length > 1"
                    v-model="dCurrentUnit"
                    :options="unitOptions"
                    :size="size"
                    @change="onLengthUnitChange"
                ></fx-select>
                <span style="color: #181C25;" v-else>{{ dCurrentUnit }}</span>
            </div>
        </fx-input>
    </div>
</template>
<script>
    const splitHeight = (height, defaultUnit) => {
        if(!height) {
            return ['', defaultUnit]
        }

        // 正则表达式匹配数字和非数字字符
        const pattern = /(\d+)|(\D+)/g;
        let match;
        let result = [];

        while ((match = pattern.exec(height)) !== null) {
            result.push(match[0]);
        }

        return result;
    }

    export default {
        name: 'LengthUnitSetter',
        props: {
            value: {
                type: String
            },
            units: {
                type: Array,
                default: () => ['px']
            },
            unitOption: {
                type: String,
            },
            maxlength: {
                type: Number,
                default: 100
            },
            minlength: {
                type: Number,
                default: 1
            },
            size: {
                type: String,
                default: 'mini'
            },
            // 精度
            precision: {
                type: Number,
                default: 0
            },
            // 是否允许为空
            allowEmpty: {
                type: Boolean,
                default: false
            }
        },
        data() {
            let defaultUnit = this.unitOption || this.units[0];
            let unitValue = ''
            if(this.value){
                const parts = splitHeight(this.value, defaultUnit);
                defaultUnit = parts[parts.length - 1];
                unitValue = Number(parts.slice(0, -1).join('')).toFixed(this.precision);
            }
            return {
                unitValue: unitValue,
                dCurrentUnit: defaultUnit
            }
        },
        computed: {
            unitOptions() {
                return this.units.map(item => {
                    return {
                        value: item,
                        label: item
                    }
                })
            }
        },
        methods: {
            onLengthChange(v) {
                if(!v && this.allowEmpty) {
                    this.unitValue = '';
                    this.$emit('change', '');
                    return;
                }
                if (v > this.maxlength) {
                    if (this.dCurrentUnit == '%') {
                        this.unitValue = this.maxlength > 100 ? 100 : this.maxlength;
                    } else {
                        this.unitValue = this.maxlength;
                    }
                } else if (v < this.minlength) {
                    if (this.dCurrentUnit == '%') {
                        this.unitValue = this.minlength;
                    } else {
                        this.unitValue = this.minlength;
                    }
                }
                this.$emit('change', `${this.unitValue}${this.dCurrentUnit}`);
            },
            onLengthUnitChange(v) {
                if(!this.unitValue && this.allowEmpty) {
                    this.dCurrentUnit = v;
                    this.$emit('change', '');
                    return;
                }
                this.dCurrentUnit = v;
                if (this.dCurrentUnit == '%') {
                    this.unitValue = this.maxlength > 100 ? 100 : this.maxlength;
                } else {
                    this.unitValue = this.maxlength;
                }
                this.$emit('change', `${this.unitValue}${this.dCurrentUnit}`);
            },
            isEmpty() {
                return this.unitValue === '';
            }
        }
    }
</script>
<style lang="less">
    .length-unit-setter-wrapper {
        .length-unit-suffix {
            width: 20px;
        }
        .el-input-group__append, .el-input-group__prepend{
            background-color: #fff;
        }
    }
</style>
