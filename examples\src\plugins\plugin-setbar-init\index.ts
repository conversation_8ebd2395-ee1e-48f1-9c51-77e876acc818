import Vue from 'vue';
/**
 * @desc 设置器初始化
 * 
 */

class InitSetbarPlugin{

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore({ preData }, { actions, config }) {
        // 覆盖默认全局筛选器的处理方式
        // const globalItem = preData.workbench.setbar.items.find(item => item.name === 'global');
        // if(globalItem) {
        //     globalItem.component = () => import('./components/GlobalSetters.vue');
        // }
        config.add('workbench.setbar.items', {
            label: '自定义1',
            name: 'custom1'
        });
        config.add('workbench.setbar.items', {
            label: '自定义2',
            name: 'custom2',
            component: {
                render: h => h('div', '自定义2')
            }
        });
    }
}

export default InitSetbarPlugin;