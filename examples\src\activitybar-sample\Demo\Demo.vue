<template>
    <div class="picture-component">
        <div>
            <img src="https://a9.fspage.com/FSR/official-site/img/home-page/banner/banner-crm100.jpg" alt="picture-component">
        </div>
        <div v-if="cText">{{cText}}</div>
        <div><fx-button @click="handleClick">按钮</fx-button></div>
        <div>
            <slot></slot>
        </div>
    </div>
</template>
<script lang="js">
    export default {
        props: ['text'],

        computed: {
            cText() {
                return this.text;
            }
        },

        methods: {
            handleClick() {
                this.$emit('change');
            },

            update() {
                console.log('update');
            }
        }
    }
</script>
<style lang="less" scoped>
    .picture-component {
        width: 100%;
        // padding: 10px;
        // border: 1px solid @success;
        box-sizing: border-box;
        img {
            width: 100%;
            height: 300px;
        }
    }
</style>