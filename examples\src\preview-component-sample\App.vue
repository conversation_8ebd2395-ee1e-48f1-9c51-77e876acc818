<template>
    <div class="w-100 h-100">
        <Render v-bind="options" @creatAfter="handleCreated"></Render>
    </div>
</template>
<script lang="js">
    import { Render } from '@beecraft/engine';
    import Demo from './Demo.vue';

    export default {
        components: {
            Render
        },
        created() {
            this.options = {
                resolver: {
                    Demo
                },
                import: [{
                    name: 'FxTabs',
                    data: {
                        "value": "item1"
                    },
                    children: [{
                        name: 'FxTabPane',
                        data: {
                            "label": "标签1",
                            "name": "item1"
                        },
                        children: [{
                            name: 'Demo',
                            $$data: {
                                title: '测试'
                            },
                            children: [{
                                name: 'Demo',
                                $$data: {
                                    title: '测试'
                                }
                            }]
                        }]
                    },{
                        name: 'FxTabPane',
                        data: {
                            "label": "标签2",
                            "name": "item2"
                        },
                        children: [{
                            name: 'Demo',
                            $$data: {
                                title: '测试'
                            }
                        }]
                    }]
                }],
                mode: 'preview',
                node: {
                    $$data: {
                        style: {
                            'margin': '8px 0',
                            'padding': '16px',
                            'background-color': '#fff'
                        }
                    }
                }
            }
        },
        methods: {
            handleCreated(editor) {
                // 此处可以调用editor的各种API
                console.log('editor', editor);
            }
        }
    }
</script>
<style lang="less" scoped>
    .picture-component {
        width: 100%;
        box-sizing: border-box;
        img {
            width: 100%;
            height: 300px;
        }
    }
</style>