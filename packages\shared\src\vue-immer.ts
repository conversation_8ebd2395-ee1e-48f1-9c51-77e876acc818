import { Vue } from './component';
import {
    produceWithPatches as rawProduceWithPatches, // 副作用比较大：会冻结数据
    enableMapSet,
    enablePatches,
    setAutoFreeze,
    applyPatches as rawApplyPatches
} from 'immer';
import { deepClone } from './object';
import { isSimpleType } from './types';

enableMapSet();
console.log('enablePatches');
enablePatches();
setAutoFreeze(false);

const SHADOW_KEY = Symbol('vue-immer-shadow');

export function useImmer<T> (initial: T, options: any = {}) {
    const shadowState = deepClone(initial);
    
    let ignoreReactiveValues = {
        [SHADOW_KEY]: shadowState
    };
    if (options.ignoreReactiveForProps) {
        options.ignoreReactiveForProps.forEach(key => {
            ignoreReactiveValues[key] = initial[key];
            delete initial[key];
        });
    }
    
    const initialState: any = Vue.observable(initial);

    Object.assign(initialState, ignoreReactiveValues);
    
    return initialState;
}

export function produceWithPatches(state, cb) {
    // const cloneState = deepClone(state[SHADOW_KEY]);

    // return rawProduceWithPatches(cloneState, cb);
    return rawProduceWithPatches(state[SHADOW_KEY], cb);
}

// 不能被响应式的数据
function _isNotReactive(value) {
    return value instanceof Vue || value instanceof HTMLElement;
}


function _applyPatches(state, patches) {
    patches.forEach(patch => {
        const { path, op } = patch;

        let base: any = state;

        for (let i = 0; i < path.length - 1; i++) {
            const parentType = getType(base);

            let p = path[i];
            if (typeof p !== "string" && typeof p !== "number") {
                p = "" + p;
            }

            if(
                (parentType === 'object' || parentType === 'array') &&
                (p === "__proto__" || p === "constructor")
            ) {
                // die
            }

            if (typeof base === "function" && p === "prototype") {
                // die
            }

            base = get(base, p);

            if (typeof base !== "object") {
                // die
            }
        }

        const type = getType(base);
        const isNotReactive = _isNotReactive(patch.value);
        const value = isSimpleType(patch.value)
            ? deepClone(patch.value)
            : patch.value;

        const key = path[path.length - 1];

        switch (op) {
            case 'replace':
                switch (type) {
                    case 'Map':
                        return base.set(key, value);
                    case 'Set':
                        // die(errorOffset)
                    default:
                        if(isNotReactive) {
                            delete base[key];
                            return (base[key] = value);
                        }
                        // return (base[key] = value)
                        Vue.set(base, key, value);
                        return value;
                }
            case 'add':
                switch (type) {
                    case 'Array':
                        return key === "-"
                            ? base.push(value)
                            : base.splice(key as any, 0, value)
                    case 'Map':
                        return base.set(key, value)
                    case 'Set':
                        return base.add(value)
                    default:
                        // return (base[key] = value)
                        if(isNotReactive) {
                            delete base[key];
                            return (base[key] = value);
                        }
                        Vue.set(base, key, value);
                        return value;
                }
            case 'remove':
                switch (type) {
                    case 'Array':
                        return base.splice(key as any, 1)
                    case 'Map':
                        return base.delete(key)
                    case 'Set':
                        return base.delete(patch.value)
                    default:
                        // delete不会触发vue响应式机制中的setter函数，所以先设置值再删除属性
                        base[key] = undefined;
                        return delete base[key]
                }
            default:
                // die(errorOffset + 1, op)
        }

    });

    return state;
}

export function applyPatches(state, patches) {
    _applyPatches(state, patches);
    _applyPatches(state[SHADOW_KEY], patches);
}

export {
    rawApplyPatches
};

export function getType (thing: any) {
    return Object.prototype.toString.apply(thing).slice(8, -1);
}

export function get(thing: any, prop: string): any {
	// @ts-ignore
	return getType(thing) === 'Map' ? thing.get(prop) : thing[prop];
}