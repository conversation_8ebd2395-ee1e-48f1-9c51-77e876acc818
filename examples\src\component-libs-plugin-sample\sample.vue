<template>
    <div></div>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import Page from '../components/Page.vue';
import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
export default {
    mounted() {
        init(
            this.$el,
            {
                resolver: {
                    Demo,
                    Page
                },
                import: [{
                    name: '<PERSON>',
                    children: [{
                        name: 'Test',
                        "source": 'paasxt',
                    }]
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "示例",
                            "name": "Demo"
                        }]
                    }, {
                        "title": "业务组件库",
                        "children": [{
                            "title": "Test",
                            "name": "Test",
                            "source": 'paasxt'
                        }]
                    }, {
                        "title": "PAAS组件库",
                        "children": [{
                            "title": "historypanel",
                            "name": "historypanel",
                            "source": 'paas'
                        },{
                            "title": "test",
                            "name": "test",
                            "source": 'paas'
                        }]
                    }]
                },
                plugins: [
                    // 加载PAAS组件库
                    builtinPlugins.PaasComponentLibs,
                    // 加载业务组件库
                    builtinPlugins.BizComponentLibs,
                    builtinPlugins.BuiltinInitWorkbench
                ],
                useDefaultPluginPackages: false
            }
        )
    }
}
</script>