<template>
    <div class="w-100 h-100"></div>
</template>
<script lang="js">
import Page from '../components/Page.vue';
import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
import Repeater from './components/Repeater.vue';
import RepeaterItem from './components/RepeaterItem.vue';
import Text from './components/text.vue';

export default {
    mounted() {
        this.$el.innerHTML = ''; // 粗暴隐藏，内存泄漏
        const $container = document.createElement('div');
        this.$el.appendChild($container);
        init(
            $container,
            {
                resolver: {
                    Page,
                    Repeater,
                    RepeaterItem,
                    Text
                },
                import: [{
                    name: '<PERSON>',
                    children: []
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "中继器",
                            "name": "Repeater"
                        }, {
                            "title": "文本",
                            "name": "Text"
                        }]
                    }]
                },
                plugins: [
                    builtinPlugins.PaasWorkbenchInit,
                    builtinPlugins.PaasComponentLibs,
                    builtinPlugins.PaasSetterLibs
                ],
                useDefaultPluginPackages: false,
                disableDefaultWrapper: true
            }
        )
    }
}
</script>