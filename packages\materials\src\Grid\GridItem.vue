<template>
    <div class="bc-grid-item" :style="style">
        <slot></slot>
    </div>
</template>
<script lang="js">
    export default {
        props: ['ratio'],
        computed: {
            style() {
                return `flex: ${this.ratio};`
            }
        },
        beecraft: function () {
            return {
                name: 'GridItem',
                displayName: $t('栅格面板'),
                $$data: {
                    noWrapper: true,
                    isCanvas: true,
                    isDeletable: false,
                    style: {
                        'padding': '8px'
                    }
                },
                rules: {
                    canDrag: () => false,
                    canSelect: () => false
                },
                hooks: {
                    beforeSelect(e, node, { query, actions }) {
                        const parentNode = query.node(node.parent).get();

                        if(parentNode.name === 'Grid') {
                            actions.setNodeEvent('selected', [parentNode.id])
                        }
                    }
                }
            }
        },
    }
</script>
<style lang="less">
    .bc-grid-item {
        min-height: 100px;
        // border-right: 1px solid #DEE1E8;
        // position: relative;
    }
    .bc-grid-item:last-child {
        border-right: unset;
    }
</style>