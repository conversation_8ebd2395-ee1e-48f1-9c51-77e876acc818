import Vue from 'vue';

//@ts-ignore
const FxTimeline = Vue.options.components['FxTimeline'];

FxTimeline.beecraft = function() {
    return {
        name: 'FxTimeline',
        displayName: $t('时间线')
    }
}



//@ts-ignore
const FxTimelineItem = Vue.options.components['FxTimelineItem'];

FxTimelineItem.beecraft = function() {
    return {
        name: FxTimelineItem,
        displayName: $t('时间点'),
        $$data: {
            isCanvas: true
        },
        rules: {
            canDrag: () => false
        },
    }
}

export {
    FxTimeline,
    FxTimelineItem
}