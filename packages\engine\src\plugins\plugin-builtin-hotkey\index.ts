/**
 * @module engine/plugins/BuiltinHotkeys
 * @desc 内置快捷键
 */

export default class BuiltinHotkeys{

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { hotkey, query, actions, options }) {
        
        if(options.enabled) {
            // 删除
            hotkey.bind(['backspace', 'del'], (e: KeyboardEvent) => {
                const selectedIds = query.getEvent('selected').all();

                if(selectedIds.length) {
                    actions.delete(selectedIds);
                }

                e.preventDefault();
            });

            // 撤销
            hotkey.bind(['ctrl+z'], (e: KeyboardEvent) => {
                actions.history.undo();
                e.preventDefault();
            });

            // 恢复
            hotkey.bind(['ctrl+y'], (e: KeyboardEvent) => {
                actions.history.redo();
                e.preventDefault();
            });
        }
        
    }
}