/**
 * @module engine/plugins/BizComponentLibs
 * @desc PAAS组件库，安装后可直接使用纷享PAAS组件
 */
export default class BizComponentLibs {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        const Fx = (<any>window).Fx as any;

        let requireHandles: any[] = [];
        let doing = {};
        if (options?.workbench?.materials) {
            options?.workbench?.materials.forEach(item => {
                item.children.forEach(item => {
                    if (
                        item.source !== 'paas' &&
                        !options.resolver[item.name] &&
                        !config.get(`resolver.${item.name}`)
                    ) {
                        const key = `${item.source}.${item.name}`;
                        if (doing[key]) {
                            return doing[key];
                        }
                        doing[key] = Fx.getBizComponent(item.source, item.name).then(res => {
                            config.set(`resolver.${item.name}`, res);
                        });

                        requireHandles.push(
                            doing[key]
                        );
                    }
                })
            });
        }


        // 渲染引擎使用
        config.add('normalizeNodes', function (node, { query, actions }) {
            if ((node.source && node.source !== 'paas') && !node._done) {
                const { resolver } = query.getOptions();

                let require: Promise<void>;
                if (resolver[node.name]) {
                    require = Promise.resolve(resolver[node.name]);
                } else {
                    require = new Promise((resolve, reject) => {
                        Fx.getBizComponent(node.source, node.name).then(Ctor => {
                            if (Ctor) {
                                // 缓存组件到resolver
                                actions.history.ignore().setOptions((options) => {
                                    options.resolver[node.name] = Ctor;
                                });
                                resolve(Ctor);
                            } else {
                                reject();
                            }
                        }, reject);
                    })
                }

                require.then((Ctor) => {
                    const nNode = query.parseSerializedNode(node, { [node.name]: Ctor }).toNode();
                    const { parent, children } = node;
                    actions.history.ignore().setState((state) => {
                        var oNode = state.nodes[node.id]; // 过度状态的节点
                        if (oNode) {
                            Object.assign(oNode, nNode, { parent, children });
                        } else {
                            Object.assign(node, nNode, { parent, children });
                        }
                    })
                })
            }
        });

        return Promise.all(requireHandles)
    }
}