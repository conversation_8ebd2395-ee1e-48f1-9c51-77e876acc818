import {
    isArray,
    isPlainObject,
    isObject
} from './types';
import { Vue } from './component';

function isIterable(val) {
    return val && (isPlainObject(val) || isArray(val));
}

function createAssigner(assigner) {
    return (object, ...sources) => {
        let index = -1
        let length = sources.length;
        let options = length > 2 && typeof sources[length - 1] === 'boolean' ? (length--, sources[length - 1]) : undefined

        object = Object(object);

        while (++index < length) {
            const source = sources[index]

            object = assigner(object, source, index, options);
        }

        return object;
    }
}

function baseFor(object, iteratee, keysFunc) {
    const iterable = Object(object)
    const props = keysFunc(object)
    let { length } = props
    let index = -1

    while (length--) {
        const key = props[++index]
        if (iteratee(iterable[key], key, iterable) === false) {
            break
        }
    }
    return object
}

function keysIn(object) {
    const result: string[] = [];
    for (const key in object) {
        result.push(key);
    }
    return result;
}

function baseAssignValue(object, key, value, isReactive = false) {
    if ((value !== undefined && object[key] !== value) ||
        (value === undefined && !(key in object))) {
        if (isReactive) {
            if(isArray(object)) {
                object.splice(key, 1, value);
            }else {
                Vue.set(object, key, value);
            }
        } else {
            object[key] = value;
        }
    }
}

function initClone(object, isReactive) {
    if(isArray(object)) {
        return isReactive ? Vue.observable([]) : [];
    }else {
        function isPrototype(value) {
            const Ctor = value && value.constructor;
            const proto = (typeof Ctor === 'function' && Ctor.prototype) || Object.prototype;
    
            return value === proto;
        }
    
        const cloneObj = (typeof object.constructor === 'function' && !isPrototype(object))
            ? Object.create(Object.getPrototypeOf(object))
            : {}

        return isReactive ? Vue.observable(cloneObj) : cloneObj;
    }
}

function baseCover(object, source, srcIndex, options = false) {
    if (object === source) {
        return object;
    }

    if(isIterable(source)) {
        // 数据类型保持一致
        if(isArray(source) && !isArray(object)) {
            object = initClone([], options);
        }else if(isPlainObject(source) && !isPlainObject(object)) {
            object = initClone({}, options);
        }

        baseFor(source, (srcValue, key) => {
            if(isIterable(srcValue)) {
                baseCoverDeep(object, source, key, srcIndex, options);
            }else {
                const newValue = srcValue;
    
                baseAssignValue(object, key, newValue, options);
            }
        }, keysIn);

        if(isArray(source)) {
            object.splice(source.length, object.length - source.length);
        }else {
            baseFor(object, (objValue, key) => {
                if (!(key in source)) {
                    delete object[key];
                }
            }, keysIn);
        }

        return object;
    }

    return source;

    function baseCoverDeep(object, source, key, srcIndex, options) {
        const objValue = object[key];
        const srcValue = source[key];

        let newValue = srcValue;

        if (isArray(srcValue)) {
            if (isArray(objValue)) {
                newValue = objValue;
            } else {
                newValue = initClone(srcValue, options);
            }
        } else if (isPlainObject(srcValue)) {
            newValue = objValue;
            if (typeof objValue === 'function' || !isObject(objValue)) {
                newValue = initClone(srcValue, options);
            }
        }

        newValue = baseCover(newValue, srcValue, key, options);
        baseAssignValue(object, key, newValue, options);
    }
}


function baseMerge(object, source, srcIndex, options = false) {
    if (object === source) {
        return object;
    }

    if(isIterable(source)) {
        // 数据类型保持一致
        if(isArray(source) && !isArray(object)) {
            object = initClone([], options);
        }else if(isPlainObject(source) && !isPlainObject(object)) {
            object = initClone({}, options);
        }

        baseFor(source, (srcValue, key) => {
            if(isIterable(srcValue)) {
                baseMergeDeep(object, source, key, srcIndex, options);
            }else {
                const newValue = srcValue;
    
                baseAssignValue(object, key, newValue, options);
            }
        }, keysIn);

        return object;
    }

    return source;

    function baseMergeDeep(object, source, key, srcIndex, options) {
        const objValue = object[key];
        const srcValue = source[key];

        let newValue = srcValue;

        if (isArray(srcValue)) {
            if (isArray(objValue)) {
                newValue = objValue;
            } else {
                newValue = initClone(srcValue, options);
            }
        } else if (isPlainObject(srcValue)) {
            newValue = objValue;
            if (typeof objValue === 'function' || !isObject(objValue)) {
                newValue = initClone(srcValue, options);
            }
        }

        baseMerge(newValue, srcValue, key, options);
        baseAssignValue(object, key, newValue, options);
    }
}

/**
 * deepMerge({name: 123}, {name: 333})
 * 
 * deepMerge({name: 123}, {name: 333, age: 123}, {age: {aaa: 1}})
 * 
 * deepMerge({name: 123}, {name: 333, age: [3,2,1]}, {age: [1,2,3, {aaa: 1}]})
 */
export const deepMerge = createAssigner(baseMerge);


/**
 * deepCover({name: 123, age: 333}, {name: 333});
 * 
 * deepCover({name: 123, age: 333, list: [1, 2, 3, 4]}, {name: 333, list: [1, 2, 3]});
 * 
 * deepCover({name: 123, age: 333, list: [1, 2,{ name: 333, list: [] },4]}, {name: 333, list: [3, 2, { name: 333, list: [1,2,3] }]});
 */
export const deepCover = createAssigner(baseCover);


export const deepClone = function(...args) {
    return deepMerge({}, ...args);
}

//@ts-ignore
window.xxx = {
    deepCover,
    deepClone,
    deepMerge
}

