import { NodeId, Node } from './nodes';

export type Connector = (el: HTMLElement, ...args: any) => any;

export type RegisteredConnector = {
  id: string;
  required: any;
  enable: () => void;
  disable: () => void;
  remove: () => void;
};

export type ConnectorPayload = {
  name: string,
  required: any;
  connector: Connector;
  options?: Record<string, any>;
}

export type NodeInfo = {
  id: NodeId;
} & DOMInfo;

export type DOMInfo = Record<
  | 'x'
  | 'y'
  | 'top'
  | 'left'
  | 'bottom'
  | 'right'
  | 'width'
  | 'height'
  | 'outerWidth'
  | 'outerHeight',
  number
> & {
  inFlow: boolean;
  margin: Record<'top' | 'left' | 'bottom' | 'right', number>;
  padding: Record<'top' | 'left' | 'bottom' | 'right', number>;
};

export interface DropPosition {
  parent: Node;
  index: number;
  where: string;
}

type ExistingDragTarget = {
  type: 'existing';
  nodes: NodeId[];
};

// todo
type NewDragTarget = {
  type: 'new';
  tree: Node | Node[];
};

export type DragTarget = ExistingDragTarget | NewDragTarget;