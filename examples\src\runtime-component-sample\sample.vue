<template>
    <Editor ref="runtime" v-bind="options">
        <Frame></Frame>
    </Editor>
</template>
<script>
import Demo from '../components/Demo.vue';
import { Editor, Frame } from '@beecraft/core'; // 导入编辑器和页面框架组件
import { builtinPlugins } from '@beecraft/engine'; // 导入内置插件

export default {
    components: {
        Editor,
        Frame
    },
    created() {
        this.options = {
            resolver: {
                Demo
            },
            import: [{
                name: 'FxTabs',
                data: {
                    value: 'label1'
                },
                children: [{
                    name: 'FxTabPane',
                    data: {
                        label: '页签1',
                        name: 'label1'
                    },
                    children: [{
                        name: 'Demo'
                    }]
                }, {
                    name: 'FxTabPane',
                    data: {
                        label: '页签2',
                        name: 'label2'
                    },
                    children: [{
                        name: 'FxTabs',
                        data: {
                            value: 'label1',
                            type: 'border-card'
                        },
                        children: [{
                            name: 'FxTabPane',
                            data: {
                                label: '页签1',
                                name: 'label1'
                            },
                            children: []
                        }, {
                            name: 'FxTabPane',
                            data: {
                                label: '页签2',
                                name: 'label2'
                            },
                            children: []
                        }]
                    }]
                }]
            }],
            enabled: false,
            plugins: [
                builtinPlugins.BuiltinComponentLibs
            ]
        }
    }
}
</script>