<template>
    <div>
        <div class="group" v-for="item in examples">
            <div class="groupTitle">{{ item.title }}</div>
            <div class="groupDesc">{{ item.desc }}</div>
            <div class="kernel">
                <div class="kernelMain">
                    <router-link v-for="child in item.children" :to="child.path" class="kernelChildren">
                        <div class="kernelChildrenLeft">
                                <img class="vc-image image_ky3ti0pr"
                                    src="https://tianshu.alicdn.com/3a7376f0-055f-44ce-b6d8-3a9900fb6645.svg"
                                    alt="Image 404" title=""
                                    style="width: 72px; height: 72px; object-fit: cover; border-radius: 0px;">
                            </div>
                            <div class="kernelChildrenRight">
                                <div class="title" title="">{{ child.title }}</div>
                                <div class="desc" title="">{{ child.desc }}</div>
                            </div>
                    </router-link>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { examples } from './routes';
export default {
    data() {
        return {
            examples,
            // image: require('./assets/1720961388667drh14tst.jpg')
        }
    }
}
</script>
<style lang="less" scoped>
.group {
    margin: 40px 40px;
}

.kernelMain {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    // grid-gap: 8px;
    border: 0.5px solid rgba(31, 56, 88, 0.2);
    box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    background-color: #ffffff;
}

.kernelChildren {
    flex: 1;
    padding-right: 16px;
    padding-left: 31px;
    display: flex;
    align-items: center;
    border-right: 0.5px solid rgba(31, 56, 88, 0.2);
    border-bottom: 0.5px solid rgba(31, 56, 88, 0.2);
    cursor: pointer;
    height: 170px;
}

.kernelChildrenLeft {
    display: flex;
    width: 72px;
    height: 72px;

    img {
        width: 72px;
        height: 72px;
        object-fit: cover;
        border-radius: 0px;
        position: relative;
        top: -30px;
        padding-right: 16px;
    }
}

.kernelChildrenRight {
    flex: 1;

    .title {
        font-size: 20px;
        color: #000000;
        letter-spacing: 0;
        margin-bottom: 16px;
    }

    .desc {
        font-size: 14px;
        color: rgba(31, 56, 88, 0.4);
        letter-spacing: 0;
        line-height: 20px;
        opacity: 0.9;
        height: 40px;
    }
}

.groupTitle {
    font-size: 40px;
    color: rgba(0, 0, 0, 0.9);
    text-align: center;
    line-height: 64px;
    margin-bottom: 16px;
}

.groupDesc {
    font-size: 16px;
    color: #959ba6;
    letter-spacing: 0;
    line-height: 16px;
    text-align: center;
    margin-bottom: 48px;
    font-weight: 300;
}
</style>