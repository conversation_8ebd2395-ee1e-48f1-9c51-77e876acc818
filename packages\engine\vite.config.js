import fs from 'fs';
import path from 'path';
import checker from 'vite-plugin-checker';
import { dependencies } from './package.json';
import { createVuePlugin } from 'vite-plugin-vue2';
import vue2Jsx from './scripts/vite-plugin-vue2-jsx';
import lessLoader from './scripts/vite-less-resources-loader';
import { viteExternalsPlugin } from 'vite-plugin-externals';
import { getBabelOutputPlugin } from '@rollup/plugin-babel';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import { buildPlugin } from 'vite-plugin-build';

export default ({ command, mode }) => {
    
    const rollupOptions = {
        external: (id) => {
            if(id === 'immer') {
                return false;
            }
            return new RegExp(
                `^(?:${Object.keys(dependencies)
                    .concat(fs.readdirSync(path.join(__dirname, '../../node_modules')))
                    .map(value =>
                        value.replace(/[|\\{}()[\]^$+*?.]/g, '\\$&').replace(/-/g, '\\x2d')
                    )
                    .join('|')})`
                ).test(id);
        },
        output: {
            // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
            globals: {
                vue: 'Vue'
            }
        }
    };

    return {
        build: {
            minify: false,
            manifest: false,
            sourcemap: false,
        },
        plugins: [
            // checker({ typescript: true })
            buildPlugin({
                fileBuild: false,
                libBuild: {
                    buildOptions: [{
                        lib: {
                            entry: path.resolve(__dirname, 'src/index.ts'),
                            name: 'index',
                            fileName: (format) => `index.js`,
                            formats: ['es']
                        },
                        rollupOptions: {
                            ...rollupOptions,
                            output: {
                                ...rollupOptions.output,
                                dir: 'esm'
                            }
                        }
                    },{
                        lib: {
                            entry: path.resolve(__dirname, 'src/index.ts'),
                            name: 'index',
                            fileName: (format) => `index.js`,
                            formats: ['umd']
                        },
                        rollupOptions: {
                            ...rollupOptions,
                            output: {
                                ...rollupOptions.output,
                                dir: 'lib'
                            }
                        }
                    }]
                }
            }),
            vue2Jsx({}),
            createVuePlugin({
                jsx: true,
            }),
            viteExternalsPlugin({
                vue: 'Vue',
            }),
            cssInjectedByJsPlugin()
            // getBabelOutputPlugin({ presets: ['@babel/preset-env'] })
        ],
        css: {
            preprocessorOptions: {
                less: {
                    // 支持内联 JavaScript
                    javascriptEnabled: true,
                    plugins: [
                        new lessLoader([
                            // '../packages/StyleVariables.less'
                        ])
                    ],
                },
            },
        },
    }
}