import {
    NodeId,
    Nodes,
    NodeMap,
    Node,
    NodeEventTypes
} from '../interfaces';

export type Options = {
    resolver: Resolver;
    enabled: boolean;
    handlers: (store: any) => any;
    indicator?: any;
    plugins?: any[];
    normalizeNode?: () => any;
}

/**
 * todo 类型不准确
 */
export type Editor = any;

export type EditorEvents = Record<NodeEventTypes, Set<NodeId>>;

/**
 * @todo indicator类型；handlers类型
 */
export type EditorState = {
    nodes: NodeMap;
    instances: Record<NodeId, Vue>;
    events: EditorEvents;
    options: Options;
    handlers: any;
    indicator: any;
};

export type Resolver = Record<string, string | Vue>;