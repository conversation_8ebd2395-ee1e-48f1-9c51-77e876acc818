<template>
    <fx-tabs class="bc-set-bar" v-model="activeTab" @tab-click="handleClick">
        <template v-for="item in items">
            <fx-tab-pane class="tab-pane" :key="item.name" :label="item.label" :name="item.name">
                <SettingsPane v-if="Array.isArray(item.related)" :related="item.related" :selectedId="selectedNode?.id"
                    :type="item.type"></SettingsPane>
                <template v-else>
                    <NodeContext v-if="item.type === 'node' && selectedNode && item.related" :id="selectedNode.id"
                        :key="selectedNode.id">
                        <component :is="item.related"></component>
                    </NodeContext>
                    <component v-else-if="item.related" :is="item.related"></component>
                    <div v-else class="no-data">{{ $t('beecraft.workbench.noConfItemAvailable', {},
        '暂无可配置项') }}
                    </div>
                </template>
            </fx-tab-pane>
        </template>
    </fx-tabs>
</template>
<script>
import SettingsPane from '../../components/SettingsPane';
import { NodeContext } from '@beecraft/core';

export default {
    name: 'FloatingSetBar',

    inject: ['useInternalEditor'],

    components: {
        SettingsPane,
        NodeContext
    },

    computed: {
        setbar() {
            const { workbench } = this.useInternalEditor((state) => ({ workbench: state.options?.workbench || {} }));
            return workbench.setbar;
        },

        items() {
            const { items = [] } = this.setbar || {};

            return items.map(item => {
                return {
                    ...item,
                    related: this.getRelated(item)
                }
            })
        },

        selectedNode() {
            const { query } = this.useInternalEditor();
            const selectedNodeId = query.getEvent('selected').first();

            if (selectedNodeId) {
                const node = query.node(selectedNodeId).get();
                const { items = [] } = this.setbar || {};

                // 优先取缓存，没有缓存展示组件默认的设置区
                this.activeTab = this.activeTabCache[selectedNodeId] || node?.$$data?.defaultSetting || items.find(item => item.type === 'node')?.name;
                return node;
            }

            return null;
        },
    },

    data() {
        return {
            activeTab: '',
        }
    },

    created() {
        this.activeTabCache = {};
        this.activeTab = this.setbar.activeTab || this.setbar.items?.[0].name;
    },

    methods: {
        handleClick() {
            if (this.selectedNode) {
                this.activeTabCache[this.selectedNode.id] = this.activeTab;
            }
        },

        getRelated({ name, related, component }) {
            const { related: optionsRelated = {} } = this.setbar ?? {};
            const { related: nodeRelated = {} } = this.selectedNode ?? {};

            name = `${name}Settings`;

            // options.related > node.related > setbar.component > setbar.related
            return optionsRelated[name] || nodeRelated[name] || component || related;
        }
    },

    beforeDestroy() {
        this.activeTabCache = {};
    }
}
</script>
<style lang="less" scoped>
.bc-set-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 260px;
    background-color: #fff;
    height: 100%;
    border-left: 1px solid #DEE1E8;

    .tab-pane {
        height: 100%;
        overflow-y: auto;
        padding: 12px;
        box-sizing: border-box;
    }

    /deep/ .el-tabs__header {
        margin: 0;
        flex-shrink: 0;
    }

    /deep/ .el-tabs__content {
        flex: 1;
        overflow-y: auto;
    }

    /deep/ .el-tabs__nav-wrap {
        padding: 0 8px;
    }

    /deep/ .el-tabs__nav-wrap.is-scrollable {
        padding: 0 28px;
    }

    /deep/ .el-tabs__item {
        height: 40px;
        line-height: 40px;
    }

    // /deep/ .el-tabs__nav-wrap {
    //    padding-right: 80px;
    // }
    /deep/ .el-tabs__header {
        padding-right: 70px;
        border-bottom: 1px solid var(--color-neutrals05, #dee1e8);
    }
}

.no-data {
    line-height: 300px;
    text-align: center;
    font-size: 12px;
    color: #94979c;
}
</style>