<template>
  <div class="dht-category-tree">
    dht-category-tree
    <!-- 使用 v-if 确保数据加载后再渲染 -->
    <div v-if="isDataReady">
      

      <!-- 分类树 -->
      <fx-tree
        v-if="treeData.length"
        :data="treeData"
        :props="treeProps"
        @node-click="handleNodeClick">
      </fx-tree>
      <div v-else class="empty-tree">
        <fx-empty description="暂无分类数据"></fx-empty>
      </div>
    </div>    
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_category_tree',
  
  data() {
    return {
      // 初始化为空对象或默认值
      category: null,
      dhtContainerApi: null,
      
      // 分类树数据
      treeData: [],
      // 树组件配置
      treeProps: {
        children: 'children',
        label: 'name'
      }
    };
  },
  
  computed: {
    // 检查数据是否已准备好
    isDataReady() {
      return this.category !== null && this.dhtContainerApi !== null;
    }
  },
  
  watch: {
    // 监听category变化
    category: {
      handler(newVal) {
        if (newVal) {
          console.log('CategoryTree: category 已更新', newVal);
          // 可以在这里执行一些依赖于category的操作
        }
      },
      deep: true
    }
  },
  
  created() {
    // 初始化逻辑
    this.loadCategoryTree();
  },
  
  methods: {
    /**
     * 加载分类树数据
     */
    loadCategoryTree() {
      // 模拟API调用获取分类树
      setTimeout(() => {
        this.treeData = [
          {
            id: 1,
            name: '电子产品',
            children: [
              { id: 11, name: '手机' },
              { id: 12, name: '电脑' },
              { id: 13, name: '相机' }
            ]
          },
          {
            id: 2,
            name: '服装',
            children: [
              { id: 21, name: '男装' },
              { id: 22, name: '女装' },
              { id: 23, name: '童装' }
            ]
          }
        ];
      }, 500);
    },

    /**
     * 处理节点点击
     * @param {Object} data - 节点数据
     * @param {Object} node - 节点对象
     */
    handleNodeClick(data, node) {
      console.log('CategoryTree: 节点点击', data, node);

      if (!this.isDataReady) return;

      // 构建分类路径
      const path = this.buildCategoryPath(node);

      // 触发分类变更事件
      this.$emit('category-change', {
        id: data.id,
        name: data.name,
        path: path
      });
    },

    /**
     * 处理分类选择（面包屑点击）
     * @param {Object} category - 分类对象
     */
    handleCategorySelect(category) {
      console.log('CategoryTree: 分类选择', category);

      if (!this.isDataReady) return;

      // 触发分类变更事件
      this.$emit('category-change', {
        id: category.id,
        name: category.name,
        path: this.category.path.slice(0,
          this.category.path.findIndex(item => item.id === category.id) + 1
        )
      });
    },

    /**
     * 处理重置分类
     */
    handleResetCategory() {
      console.log('CategoryTree: 重置分类');

      if (!this.isDataReady) return;

      // 触发分类变更事件
      this.$emit('category-change', {
        id: null,
        name: '',
        path: []
      });
    },

    /**
     * 构建分类路径
     * @param {Object} node - 当前节点
     * @returns {Array} 分类路径数组
     */
    buildCategoryPath(node) {
      const path = [];
      let currentNode = node;

      while (currentNode && currentNode.data) {
        path.unshift({
          id: currentNode.data.id,
          name: currentNode.data.name
        });
        currentNode = currentNode.parent;
      }

      return path;
    }
  }
};
</script>

<style scoped>
.dht-category-tree {
  padding: 15px;
}

.empty-tree {
  padding: 20px 0;
  text-align: center;
}
</style>
