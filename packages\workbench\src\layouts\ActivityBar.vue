<template>
    <div class="bc-activity-bar" :class="rootClassName" v-if="activityBar?.activityArea">
        <div class="bc-fix-pane">
            <div class="bc-left-area-top">
                <template v-for="item in activityBar.activityArea">
                    <template v-if="item.data && item.data.align === 'top'">
                        <div class="activity-button"
                            :class="{ 'is-active': currentDock === item.name, [`activity-button-${item.name}`]: true }"
                            @click="handleClick(item)">
                            <fx-tooltip effect="dark" :content="item.data?.description" placement="right">
                                <SvgIcon v-if="item.data.icon?.content" :size="20" :viewBox="'0 0 1024 1024'"
                                    :content="item.data.icon.content">
                                </SvgIcon>
                                <span v-else :class="`icon ${item.data?.icon}`"></span>
                            </fx-tooltip>
                        </div>
                    </template>
                </template>
            </div>
            <div class="bc-left-area-bottom">
                <template v-for="item in activityBar.activityArea">
                    <template v-if="item.data && item.data.align === 'bottom'">
                        <div class="activity-button"
                            :class="{ 'is-active': currentDock === item.name, [`activity-button-${item.name}`]: true }"
                            @click="handleClick(item)">
                            <fx-tooltip effect="dark" :content="item.data?.description" placement="right">
                                <SvgIcon v-if="item.data.icon?.content" :size="20" :viewBox="'0 0 1024 1024'"
                                    :content="item.data.icon.content">
                                </SvgIcon>
                                <span v-else :class="`icon ${item.data?.icon}`"></span>
                            </fx-tooltip>
                        </div>
                    </template>
                </template>
            </div>
        </div>
        <div v-if="cDockItem" class="bc-float-pane" :class="floatPaneClassName" :style="`width: ${width}px;`">
            <div class="bc-pane-header">
                <div class="bc-pane-title">{{ cDockItem.paneldata?.title }}</div>
                <fx-tooltip effect="dark" :content="$t('beecraft.btn.type_switch', '固定')" placement="top" v-if="workbench.enableTypeSwitch">
                    <div
                        class="bc-pane-btn"
                        :class="`${workbench.type === 'floatingLayer' ? 'fx-icon-obj-app29' : 'fx-icon-f-obj-app29'}`"
                        @click="handleTypeSwitch">
                    </div>
                </fx-tooltip>
                <fx-tooltip effect="dark" :content="$t('关闭')" placement="top">
                    <div class="bc-pane-btn fx-icon-close" @click="handleClose"></div>
                </fx-tooltip>
            </div>
            <div class="bc-pane-content">
                <keep-alive>
                    <component :is="cDockItem.paneldata?.component" v-bind="cDockItem.paneldata?.data"></component>
                </keep-alive>
            </div>
        </div>
    </div>
</template>
<script>
import { SvgIcon } from '@beecraft/shared';

export default {
    name: 'ActivityBar',

    components: {
        SvgIcon
    },

    data() {
        return {
            currentDock: '',
            icons: {
                thumbtack: `
                        <path d="M0 0h1024v1024H0z" opacity=".01"></path>
                        <path d="M238.933333 102.4a34.133333 34.133333 0 0 1 34.133334-34.133333h477.866666a34.133333 34.133333 0 1 1 0 68.266666h-68.266666v357.205334l95.812266 63.829333c56.183467 37.4784 29.661867 125.098667-37.888 125.098667H546.133333v238.933333a34.133333 34.133333 0 1 1-68.266666 0v-238.933333H283.409067c-67.584 0-94.071467-87.586133-37.888-125.064534L341.333333 493.738667V136.533333H273.066667a34.133333 34.133333 0 0 1-34.133334-34.133333z m170.666667 34.133333v375.466667a34.133333 34.133333 0 0 1-15.189333 28.398933L283.409067 614.4h457.181866l-111.0016-74.001067A34.133333 34.133333 0 0 1 614.4 512V136.533333h-204.8z"></path>
                    `
            },
            width: 260
        }
    },

    inject: ['useInternalEditor'],

    computed: {

        rootClassName() {
            return {
                'unfold': !!this.cDockItem,
                'floatingLayer': this.workbench.type === 'floatingLayer'
            }
        },

        floatPaneClassName() {
            return {}
        },

        workbench() {
            return this.useInternalEditor((state) => ({ workbench: state.options?.workbench })).workbench;
        },

        activityBar() {
            return this.workbench?.activityBar;
        },
        cDockItem() {
            const { currentDock, activityBar } = this;
            return currentDock ? activityBar.activityArea.find(item => item.name === currentDock) : null;
        }
    },

    mounted() {
        this.currentDock = this.activityBar.currentDock;
    },

    methods: {

        handleClose() {
            this.currentDock = null;
        },

        handleTypeSwitch() {
            const { actions } = this.useInternalEditor();
            actions.history.ignore().setOptions(options => {
                if(options.workbench.type === 'normal') {
                    options.workbench.type = 'floatingLayer';
                }else {
                    options.workbench.type = 'normal';
                }
            })
        },

        handleClick(val) {
            if (val.type === 'link') {
                window.open(val.linkdata?.url);
                return;
            }

            const currentDock = this.currentDock;

            if (currentDock && currentDock === val.name) {
                this.currentDock = null;
            } else {
                this.currentDock = val.name;
                this.width = val.paneldata?.width ?? 260;
            }
        }
    }
}
</script>
<style lang="less" scoped>
.bc-activity-bar {
    position: relative;
    background-color: #fff;
    z-index: 201;
    height: 100%;
    display: flex;

    .activity-button {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        text-align: center;
        height: var(--bc-g-wb-size, 40px);
        width: var(--bc-g-wb-size, 40px);
        color: #737c8c;

        .icon:before {
            color: #737c8c;
            font-size: 20px;
        }

        &.is-active {
            color: var(--color-primary06);

            .icon:before {
                color: var(--color-primary06);
            }
        }
    }

    .bc-fix-pane {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: var(--bc-g-wb-size, 40px);
        height: 100%;
        box-sizing: border-box;
        border-right: 1px solid #DEE1E8;
        background-color: #fff;
    }

    .bc-float-pane {
        display: flex;
        flex-direction: column;
        width: 260px;
        height: 100%;
        border-right: 1px solid #DEE1E8;
        background-color: #fff;

        // &--float {
        //     position: absolute;
        //     top: 0;
        //     bottom: 0;
        //     left: 100%;
        //     box-shadow: 4px 6px 6px 0 rgba(31, 50, 88, .08);
        //     border-right: none;
        // }
    }

    .bc-pane-header {
        display: flex;
        align-items: center;
        height: var(--bc-g-wb-size, 40px);
        padding: 0 14px;
        border-bottom: 1px solid #DEE1E8;
        box-sizing: border-box;
        flex-shrink: 0;
    }

    .bc-pane-title {
        font-size: 14px;
        color: #0f1726;
        font-weight: 700;
        margin-right: auto;
    }

    .bc-pane-btn {
        font-size: 14px;
        width: 16px;
        text-align: center;
        margin: 0 4px;
        cursor: pointer;

        &:before {
            color: var(--color-special02);
        }
    }

    .bc-pane-content {
        padding: 0 12px;
        flex: 1;
        height: 0;
        overflow-y: auto;
    }
}

// 浮层样式
.floatingLayer {
    filter: drop-shadow(0 4px 12px #00000026);
    
    .bc-activity-bar {
        height: unset;
        position: unset;
    }

    .bc-fix-pane {
        position: absolute;
        top: calc(var(--bc-g-wb-size, 40px) + 8px);
        left: 10px;
        z-index: 201;
        height: unset;
        border-radius: 2px 0px 0px 2px;
        // box-shadow: 0 4px 12px 0px rgba(0, 0, 0, 0.15);
        border-right: none;
    }

    .bc-pane-header {
        // box-shadow: 0px -1px 0px 0px #DEE1E8 inset;
    }

    .bc-float-pane {
        position: absolute;
        top: calc(var(--bc-g-wb-size, 40px) + 8px);
        left: 50px;
        z-index: 201;
        height: unset;
        border-right: none;
        // box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
        max-height: calc(100% - 50px);
        min-height: 600px;
    }
}

// 收起
.unfold {
    .bc-fix-pane {
        border-right: 1px solid #DEE1E8;
    }
}
</style>