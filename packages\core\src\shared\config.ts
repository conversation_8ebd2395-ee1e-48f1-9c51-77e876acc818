import { deepMerge, deepClone, isObject, isPlainObject, isArray } from "@beecraft/shared";

// /**
//  * @module shared/config
//  */

// const _ = window._;

// const TYPES_NOT_HANDLE = [HTMLElement, Function];

/**
 * @desc 设计器配置
 */
export default class Config {

    /**
     * @desc 存储完整配置 
     */
    private _config: object;
    
    
    constructor(config: object, defaultConfig?: object) {

        this._config = {};

        if (defaultConfig) {
            this.define(deepClone(defaultConfig));
        }

        if ( config ) {
			this._setObjectToTarget( this._config, config );
		}

    }


    /**
     * @desc 设置属性和值
     * 
     * this.config.set(key, value);
     * 
     * this.config.set({
     *      key: value
     * });
     */
    public set(name: string|object, value?: any) {
        this._setToTarget(this._config, name, value);
    }

    public add(name: string, value: any) {
        let target = this.get(name);

        if(target === undefined) {
            target = [];
            this.set(name, target);
        }

        if(isArray(target)) {
            this.set(`${name}.${target.length}`, value);
        }
    }

    
    /**
     * @desc 获取值
     * 
     * const value = this.config.get(key);
     */
    public get(name: string): any {
        return this._getFromSource(this._config, name);
    }


    /**
     * @desc 注入相关配置。如果每个配置已经存在，将不会进行覆盖。
     */
    public define(name: string|object, value?: any): void {
        const isCoverIfExist = false;

        this._setToTarget(this._config, name, value, isCoverIfExist);
    }


    private _setToTarget(target: any, name: any, value: any, isCoverIfExist = true): void {
        if (isObject(name)) {
			this._setObjectToTarget(target, name, isCoverIfExist);

            return;
		}

        const parts: [string] = name.split( '.' );

        name = parts.pop();

        for(const part of parts) {
            if(!isObject(target[part])) {
                // target[part] = isNaN(+part) ? {} : [];
                target[part] = {};
            }

            target = target[part];
        }

        if(isPlainObject(value)) {
            if(!isObject(target[name])) {
                target[name] = {};
            }

            target = target[name];

            this._setObjectToTarget(target, value, isCoverIfExist);

            return;
        }

        if (!isCoverIfExist && target[name] !== undefined) {
            return;
        }

        target[name] = value;
    }


    /**
     * @desc 将对象配置保存在目标对象上
     */
    private _setObjectToTarget(target: any, configuration: any, isCoverIfExist?: boolean): void {
        
        Object.keys(configuration).forEach((key: string) => {
            this._setToTarget(target, key, configuration[key], isCoverIfExist);
        });
    }


    private _getFromSource(source: any, name: string): any {
        const parts = name.split( '.' );

        name = <string>parts.pop();
        
        for (const part of parts) {
			if (!isObject(source[part])) {
				source = null;
				break;
			}

			// Nested object becomes a source.
			source = source[part];
		}

        return source ? deepClone(source[name]) : undefined;
    }

    public toJSON() {
        return deepClone(this._config);
    }
}
