<script>
    export default {
        inject: ['_contextNode'],
        props: {
            name: {
                type: String
            },
            address: {
                type: String
            }
        },
        render(createElement) {
            return createElement('div', this.name);
        },
        beecraft() {
            return {
                name: 'text',
                displayName: '文本组件',
            }
        },
    }
</script>
<style lang="less" scoped>
    div {
        height :50px;
        color: red;
    }
</style>