import { isObject } from '@beecraft/shared';

export const getRangesFromTarget = (target, ranges = []) => {
    const parts = ranges.slice(0, -1);
    const key = ranges[ranges.length - 1];
    
    for (const part of parts) {
        if (!isObject(target[part])) {
            target = null;
            break;
        }

        target = target[part];
    }

    return target ? target[key] : undefined;
}

export const setRangesToTarget = (target, ranges = [], value) => {
    const parts = ranges.slice(0, -1);
    const key = ranges[ranges.length - 1];
    
    for(const part of parts) {
        if(!isObject(target[part])) {
            target[part] = {};
        }

        target = target[part];
    }
    
    // data、$$data、data.style、$$data.style 都进行合并
    if(key === 'style' || !key) {
        if(key) {
            if(!target[key]) {
                target[key] = {};
            }
            target = target[key];
        }

        Object.keys(value).forEach(key => {
            if(value[key] === undefined) {
                delete target[key];
            }else {
                target[key] = value[key];
            }
        });
    }else {
        target[key] = value;
    }
}