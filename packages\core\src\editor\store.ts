import { Vue } from '@beecraft/shared';
import {
    EditorState,
    Options,
    NodeId,
    Node,
    Resolver
} from '../interfaces';
import { QueryMethods } from './query';
import { ActionMethods } from './actions';
import EventHandlers from '../events/EventHandlers';
import { deepCover, deepClone } from '@beecraft/shared';
import { History, HISTORY_ACTIONS } from '../shared/History';
import { useImmer, produceWithPatches, applyPatches } from '@beecraft/shared';
import Hotkey from '../shared/Hotkey';
import PluginService from '../shared/PluginService';

function matchWildcard(str: string, pattern: string) {
    // 将通配符转换为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')    // 转义点号
      .replace(/\*/g, '.*')     // * 转为 .*
      .replace(/\?/g, '.');     // ? 转为 .
    
    // 创建正则表达式对象并测试
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(str);
}


// //@ts-ignore
// window.applyPatches = applyPatches;
// //@ts-ignore
// window.produceWithPatches = produceWithPatches;

export type EditorStore = {
    actions: any,
    query: any,
    history: any,
    pluginService: any,
    hotkey: any,
    getState: () => EditorState
}


export const ActionMethodsWithConfig = {
    ignoreHistoryForActions: [
        'setDOM',
        'setNodeEvent',
        'selectNode',
        'clearEvents',
        // 'setOptions',
        'setIndicator',
        'setInstance'
    ]
}

/**
 * 格式化resover：将所有resover处理成构造函数，使vue组件参数免受响应式影响。
 */
export function normalizeResolver(resolver: Resolver) {
    return Object.keys(resolver).reduce((accum, name) => {
        const CtorOrOptions = resolver[name] as any;

        if (CtorOrOptions && typeof CtorOrOptions === 'object') {
            accum[name] = Vue.extend(CtorOrOptions);
            accum[name].beecraft = CtorOrOptions.beecraft;
        } else {
            accum[name] = resolver[name];
        }

        return accum;
    }, {} as any)
}


export const useEditorStore = (
    options: Partial<Options> = {}
): EditorStore => {    
    // const state = useImmer({
    //     ...editorInitialState,
    //     options: {
    //         ...editorInitialState.options,
    //         ...options
    //     },
    // }, { ignoreReactiveForProps: ['instances', 'handlers', 'options'] });
    
    /**
     * 编辑器初始值
     * @todo 代码注释掉了
     * 
     */
    const editorInitialState: EditorState = {
        nodes: {},
        events: {
            dragged: new Set<NodeId>(),
            selected: new Set<NodeId>(),
            hovered: new Set<NodeId>(),
        },
        instances: {},
        indicator: null,
        handlers: null,
        options: {
            resolver: {},
            enabled: true,
            indicator: {
                error: 'var(--color-danger06)',
                success: 'var(--color-primary06)',
            },
            handlers: (store) => new EventHandlers({ store, isMultiSelectEnabled: false }),
            // normalizeNode: () => {},
        },
    }

    const state = useImmer({
            ...editorInitialState,
            options: {
                ...editorInitialState.options,
                ...options
            },
        }, { ignoreReactiveForProps: ['instances', 'handlers'] });

    const ignoreHistoryForActions = ActionMethodsWithConfig.ignoreHistoryForActions;

    const getState = function () {
        return state;
    }

    let store:any = {};

    // 历史记录
    const history = new History();

    // 插件引擎
    const pluginService = new PluginService(store, options.plugins);

    // 快捷键
    const hotkey = new Hotkey();

    // 查询类API
    const query = {
        // @ts-ignore
        ...QueryMethods(getState()),
        history: {
            canUndo: () => history.canUndo(),
            canRedo: () => history.canRedo(),
        },
    };

    const actionMethods = ActionMethods(getState(), query);

    /**
     * 根据动作类型记入历史
     */
    const dispatch = function (action: any) {

        let result;

        const [nextState, patches, inversePatches]: any = produceWithPatches(state, (draft: any) => {
            switch (action.type) {
                case HISTORY_ACTIONS.UNDO: {
                    return history.undo(draft);
                }
                case HISTORY_ACTIONS.REDO: {
                    return history.redo(draft);
                }
                case HISTORY_ACTIONS.CLEAR: {
                    history.clear();
                    return {
                        ...draft,
                    };
                }
                case HISTORY_ACTIONS.IGNORE:
                case HISTORY_ACTIONS.MERGE:
                case HISTORY_ACTIONS.THROTTLE: {
                    const [type, ...params] = action.payload;
                    result = ActionMethods(draft, query)[type](...params);
                    break;
                }
                default:
                    // 所附的值也会被immer冻结，从而导致响应式生效。例如indicator中有对node的引用
                    // const params = action.payload.map(item => deepClone(item));
                    const params = action.payload;
                    result = (ActionMethods(draft, query) as any)[action.type](...params);
            }
        });

        /**
         * 如果存在状态变更
         */
        if (patches.length) {
            try {
                if (![
                    ...ignoreHistoryForActions,
                    HISTORY_ACTIONS.UNDO,
                    HISTORY_ACTIONS.REDO,
                    HISTORY_ACTIONS.IGNORE,
                    HISTORY_ACTIONS.CLEAR,
                ].includes(action.type)) {
                    if (action.type === HISTORY_ACTIONS.THROTTLE) {
    
                    } else if (action.type === HISTORY_ACTIONS.MERGE) {
                        history.merge(patches, inversePatches);
                    } else {
                        history.add(patches, inversePatches);
                    }
                }
                applyPatches(state, patches);

                state.options.onStateChange?.(
                    {
                        nodes: state.nodes,
                        options: state.options,
                    },
                    patches
                );

                // patchesListeners
                if (Object.keys(patchesListeners).length) {
                    patches.forEach(patch => {
                        const { path } = patch;
                        const eventId = path.join('.');
                        Object.keys(patchesListeners).forEach(prop => {
                            if(matchWildcard(eventId, prop)) {
                                const target = path.reduce((memo, key) => {
                                    return memo[key];
                                }, state);
                                patchesListeners[prop]?.forEach(handler => handler(target, patch));
                            };
                        })
                    });
                }
            }catch(e) {
                console.log(e);
            }
        }

        return result;
        // return (ActionMethods(state, query) as any)[action.type](...action.payload);
    }

    const actionTypes = Object.keys(actionMethods);

    /**
     * 设计器动作类API
     */
    const actions = {
        ...actionTypes.reduce((accum, type) => {
            accum[type] = (...payload: any) => dispatch({ type, payload });

            return accum;
        }, {} as any),
        history: {
            undo() {
                return dispatch({
                    type: HISTORY_ACTIONS.UNDO,
                });
            },
            redo() {
                return dispatch({
                    type: HISTORY_ACTIONS.REDO,
                });
            },
            clear() {
                return dispatch({
                    type: HISTORY_ACTIONS.REDO,
                });
            },
            ignore: () => {
                return {
                    ...actionTypes
                        .filter((type) => !ignoreHistoryForActions.includes(type))
                        .reduce((accum, type) => {
                            accum[type] = (...payload) =>
                                dispatch({
                                    type: HISTORY_ACTIONS.IGNORE,
                                    payload: [type, ...payload],
                                });
                            return accum;
                        }, {} as any),
                };
            },
            merge: () => {
                return {
                    ...actionTypes
                        .filter((type) => !ignoreHistoryForActions.includes(type))
                        .reduce((accum, type) => {
                            accum[type] = (...payload) =>
                                dispatch({
                                    type: HISTORY_ACTIONS.MERGE,
                                    payload: [type, ...payload],
                                });
                            return accum;
                        }, {} as any),
                };
            }
        }
    }

    var stateListeners: any = {};
    var patchesListeners: any = {};

    store = Object.assign(store, {
        getState,
        query,
        actions,
        history,
        pluginService,
        hotkey,

        // @todo 自实现store监听
        onStateChange(prop, handler) {
            if(!stateListeners[prop]){
                stateListeners[prop] = [];
            }
            const vm = new Vue({
                //@ts-ignore
                render: function() {
                    let state = query.getState();
                    const arr = prop.split('.');
                    
                    arr.forEach(item => {
                        state = state?.[item];
                    });

                    if(this.isMounted){
                        handler && (handler(state));
                    }
                },
                mounted(){
                    this.isMounted = true;
                }
            })
            vm.$mount();
            stateListeners[prop].push(handler);
        },
        onPatchesUpdate(prop, handler) {
            if(!patchesListeners[prop]){
                patchesListeners[prop] = [];
            }
            patchesListeners[prop].push(handler);
        },
        destroy(){
            Object.keys(stateListeners).forEach(prop => {
                stateListeners[prop]?.forEach(vm => {
                    vm.$destroy();
                })
            })
            stateListeners = {};
        }
    })

    return store;
}
