<template>
    <div class="bc-node-wrapper">
        <div class="bc-node-wrapper-header d-flex align-items-center" v-if="!current.noTitle">
            <span class="bc-node-wrapper-title">{{ current.title }}</span>
        </div>
        <div class="bc-node-wrapper-content" :style="current.style">
            <slot></slot>
        </div>
    </div>
</template>
<script>

export default {
    name: 'DefaultRender',

    inject: ['useInternalNode', 'useInternalEditor'],

    computed: {
        // TODO 坑
        // contentClass() {
        //     const { query } = this.useInternalEditor();
        //     const richtextClass = query.getOptions().node.$$data?.richtextConfig?.richtextClass || {};
        //     return richtextClass
        // },
        current() {
            return this.useInternalNode((node) => {
                let result = {};

                if (node.$$data) {
                    result = Object.assign(result, {
                        title: node.$$data.title,
                        style: node.$$data.style,
                        noTitle: node.$$data.noTitle,
                    });
                }

                return result;
            });
        }
    },

}
</script>
<style lang="less" scoped>
.bc-node-wrapper-header {
    color: rgba(0, 0, 0, .85);
    font-weight: 600;
    font-size: 16px;
    background-color: #fff;
    padding-bottom: 12px;
}

.bc-node-wrapper {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.bc-node-wrapper-content {
    flex: 1;
    height: 0;
}
</style>