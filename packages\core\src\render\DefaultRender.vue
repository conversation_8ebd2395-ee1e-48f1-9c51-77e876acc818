<script>
import { deepMerge } from '@beecraft/shared';

export default {
    name: 'DefaultR<PERSON>',

    components: {
        NodeElement: () => import('../nodes/NodeElement.vue')
    },

    inject: ['useInternalNode', 'useInternalEditor'],

    render(createElement) {
        if (!this.isShow) {
            return '';
        }

        const { enabled, node, processChildItem } = this;

        // 优先组件在设计器的预览态，再使用组件的运行态
        const Ctor = enabled ? node.previewDisplay ?? node.type : node.type;

        // 是否通过中继器模式进行渲染
        const isRenderByRepeater = node.isRepeater && node.isExistDescendants;

        return createElement(Ctor, {
            key: node.id,
            props: {
                ...this.nodeData,
                ...(node.keepDataStyle ? {  } : { style: {} })
            },
            attrs: {
                'data-id': node.id,
            },
            class: 'bc-node',
            ref: 'instance',
            on: {
                'hook:created': this.handleCreated,
                'hook:mounted': this.handleMounted
            },
            scopedSlots: {
                // 中继器的渲染模板
                renderItems: isRenderByRepeater ? (node) => {
                    return processChildItem(createElement, node);
                } : undefined,
            }
        }, isRenderByRepeater ? undefined : processChildItem(createElement));
    },

    data() {
        return {
            isShow: false
        }
    },

    computed: {
        node() {
            const { query } = this.useInternalEditor();
            return this.useInternalNode((node, query) => {
                console.log('重新计算node', node.id);
                const { enabled } = query.getOptions();
                return {
                    id: node.id,
                    type: node.type,
                    data: this.dataHandler ? this.dataHandler(node.data) : node.data,
                    children: node.children,
                    hydrationTimestamp: node._hydrationTimestamp,
                    isCanvas: node.$$data.isCanvas,
                    isRepeater: node.$$data.isRepeater,
                    previewDisplay: node.related.previewDisplay,
                    allowOperation: !enabled || node.$$data.allowOperation !== false,
                    placeholder: node.$$data.placeholder,
                    isExistDescendants: query.node(node.id).descendants(true).length > node.children.length,
                    keepDataStyle: node.$$data.keepDataStyle
                }
            });
        },
        nodeData() {
            let { data, extra, id } = this.useInternalNode((node) => {
                return {
                    data: node.data,
                    extra: node.extra
                }
            });

            // 渲染前预处理数据
            data = this.preprocessData ? this.preprocessData(data, { extra }, this.useInternalEditor()) : data;
            console.log('重新计算nodeData', id);
            return data;
        }
    },

    created() {
        // this.enabled = this.useInternalEditor((state) => ({ enabled: state.options.enabled })).enabled;
        const { enabled, preprocessData } = this.useInternalEditor((state) => {
            return {
                enabled: state.options.enabled,
                preprocessData: state.options.preprocessData
            }
        });
        this.enabled = enabled;
        this.preprocessData = preprocessData;

        if (this.callHook('beforeRender') !== false) {
            this.isShow = true
        };
    },

    beforeDestroy() {
        // this.callHook('beforeDestroy');
    },

    methods: {
        handleMounted() {
            const { actions, pluginService, query } = this.useInternalEditor();
            const { id } = this.useInternalNode();

            const instance = this.$refs.instance;

            if (instance) {
                actions.setInstance(id, instance);
                if (this.node.allowOperation === false && window.getComputedStyle(instance.$el).position === 'relative') {
                    const maskElem = document.createElement('div');
                    maskElem.style.position = 'absolute';
                    maskElem.style.top = '0';
                    maskElem.style.left = '0';
                    maskElem.style.right = '0';
                    maskElem.style.bottom = '0';
                    maskElem.style.zIndex = '110';
                    instance.$el.appendChild(maskElem);
                }
            }

            query.node(id).callHook('afterRender', { id, query, actions });

            // todo 没有考虑插件和组件扩展的优先级
            pluginService.run('beecraft.node.render.after', { id });
            this.callHook('rendered');

            this.$emit('mounted');
        },
        callHook(name) {
            const { query, actions } = this.useInternalEditor();
            const { id } = this.useInternalNode();
            const nodeHelper = query.node(id);

            nodeHelper.callHook(name, nodeHelper.get(), { query, actions });
        },

        processChildItem(createElement, rNode) {
            const { enabled, node } = this;
            let childVnodes;

            if (enabled && node.isCanvas && !node.children?.length) {
                // container-placeholder类名和findPosition存在依赖，请勿改动
                // 空容器显示占位提示
                childVnodes = [
                    createElement('div', {
                        class: 'container-placeholder',
                        attrs: {
                            'data-placeholder': node.id,
                        }
                    }, node.placeholder || $t('beecraft.workbench.dragHere', {}, '拖拽组件到这里'))
                ];
            } else {
                // 非空容器，正常渲染子节点
                childVnodes = (node.children || []).map(id => {
                    return createElement('NodeElement', {
                        key: id,
                        props: {
                            id,
                            // contextNode: rNode ? rNode : undefined
                        }
                    })
                });
            }

            return childVnodes;
        }
    }
}
</script>
<style lang="less" scoped>
.container-placeholder {
    min-height: 60px;
    border: 1px dotted;
    color: #a7b1bd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.1);
    height: 100%;
}

.bc-node {
    box-sizing: border-box;
    position: relative;
}
</style>