import * as fxui from '@beecraft/fxui';
import * as materials from '@beecraft/materials';

/**
 * @module engine/plugins/BuiltinComponentLibs
 * @desc 内置组件库插件，安装后会启用内置插件
 */
export default class BuiltinComponentLibs {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        let defaultResolver = {
            ...fxui,
            ...materials,
        };
        
        config.set(
            'resolver',
            options.componentList?.length
                ? Object.keys(defaultResolver).reduce((memo, key) => {
                    if(options.componentList.indexOf(key) > -1) {
                        memo[key] = defaultResolver[key];
                    }
                    return memo;
                }, {})
                : defaultResolver
        );
    }
}