<template>
    <div class="lattice-background">
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
            <!-- 定义图案 -->
            <defs>
                <pattern id="repeatingPattern" width="100%" :height="rowHeight + margin[1]" patternUnits="userSpaceOnUse">
                    <template v-for="item in cGrids">
                        <rect
                            :style="`x: ${item.left}; y: ${item.top};`"
                            :width="item.width"
                            :height="item.height"
                            fill="transparent"
                            :stroke="gridRectStroke"
                            stroke-width="1"
                            stroke-dasharray="3">
                        </rect>
                    </template>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#repeatingPattern)"></rect>
        </svg>
    </div>
</template>
<script>
    export default {
        props: {
            colNum: {
                type: Number,
                default: 12
            },
            rowHeight: {
                type: Number,
                default: 150
            },
            margin: {
                type: Array,
                default: function _default() {
                    return [10, 10];
                }
            },
            gridRectStroke: {
                type: String,
                default: '#DEE1E8'
            },
            containerWidth: {
                type: Number
            }
        },
        computed: {
            cGrids() {
                const {
                    colNum,
                    rowHeight,
                    margin
                } = this;
                const colWidth = (this.containerWidth - margin[0] * (colNum + 1)) / colNum;

                let ret = [];
                for(var i = 0; i < colNum; i++) {
                    ret.push({
                        width: colWidth,
                        height: rowHeight,
                        left: colWidth * i + (i + 1) * margin[0],
                        top: margin[1]
                    });
                }

                return ret;
            },
            cLayerRect() {
                const { rowHeight, margin } = this;
                const containerRect = this.$el?.getBoundingClientRect();

                return {
                    width: containerRect.width,
                    height: rowHeight + margin[1]
                }
            }
        }
    };
</script>
<style lang="less" scoped>
    .lattice-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }
    svg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: -1;
    }
</style>