<template>
  <div class="dhtbiz-container-product-list">
    container product-list
    <slot
      :dhtPageData="dhtPageData"
      :dhtContainerApi="dhtContainerApi"
    ></slot>
  </div>
</template>

<script>
// import Vue from 'vue';
// import { loadCss, loadFxModule } from '@utils/load';

// 事件类型常量
const DHT_PAGE_EVENT_TYPES = {
  // 分类相关
  CATEGORY_CHANGE: 'category:change',
  CATEGORY_RESET: 'category:reset',

  // 搜索相关
  SEARCH_CHANGE: 'search:change',
  SEARCH_RESET: 'search:reset',

  // 筛选相关
  FILTER_CHANGE: 'filter:change',
  FILTER_RESET: 'filter:reset',

  // 列表相关
  LIST_REFRESH: 'list:refresh',
  LIST_LOADING: 'list:loading',
  LIST_ERROR: 'list:error',
  LIST_DATA_UPDATE: 'list:data:update',

  // 全局重置
  GLOBAL_RESET: 'global:reset'
};

export default {
  // const { id } = this.useInternalNode();
  inject: ['useInternalEditor', 'useInternalNode'],
  name: 'dht_web_container_product_list',
  props: {
    // 初始分类ID
    initialCategoryId: {
      type: [String, Number],
      default: null
    },
    // 初始搜索关键字
    initialKeyword: {
      type: String,
      default: ''
    },
    // API配置
    apiConfig: {
      type: Object,
      default: () => ({
        listApi: 'ShopMall',
        categoryApi: 'Category',
        searchApi: 'Search'
      })
    },
    // 分页配置
    pageConfig: {
      type: Object,
      default: () => ({
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100']
      })
    }
  },

  data() {
    return {
      show: false,
      // 事件总线
      eventBus: new Vue(),

      // 业务页面数据
      dhtPageData: {
        // 当前分类信息
        category: {
          id: this.initialCategoryId,
          name: '',
          path: []
        },

        // 搜索信息
        search: {
          keyword: this.initialKeyword,
          timestamp: 0
        },

        // 筛选条件
        filters: {
          price: { min: null, max: null },
          brand: [],
          attributes: {},
          custom: {}
        },

        // 列表状态
        list: {
          loading: false,
          // data: [],
          total: 0,
          page: 1,
          pageSize: this.pageConfig.pageSize,
          error: null
        },

        // 组件状态
        components: {
          categoryTree: {
            expanded: [],
            selected: this.initialCategoryId
          },
          shopList: {
            viewMode: 'grid',
            sortBy: 'default'
          },
          filters: {
            visible: true,
            collapsed: false
          }
        }
      }
    };
  },

  computed: {
    // 容器方法，供子组件调用
    dhtContainerApi() {
      return {
        // 获取当前查询参数
        getQueryParams: this.getQueryParams,
        // 重置指定组件数据
        resetComponentData: this.resetComponentData,
        // 刷新列表
        refreshList: this.refreshList,
        // 获取事件类型常量
        getEventTypes: () => DHT_PAGE_EVENT_TYPES
      };
    }
  },

  created() {
    this.initEventListeners();    
    this.initializeData();
  },
  mounted() {
    this.setContainerData();
    this.show = true;
  },

  beforeDestroy() {
    // 清理事件监听
    this.eventBus.$destroy();
  },

  methods: {
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
      // 分类变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.CATEGORY_CHANGE, this.handleCategoryChange);

      // 搜索变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.SEARCH_CHANGE, this.handleSearchChange);

      // 筛选变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.FILTER_CHANGE, this.handleFilterChange);

      // 列表刷新事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.LIST_REFRESH, this.handleListRefresh);

      // 全局重置事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.GLOBAL_RESET, this.handleGlobalReset);
    },

    setContainerData() {
      const { actions } = this.useInternalEditor();
      const { id } = this.useInternalNode();
      // actions.history.ignore().setCustom(($$data) => {
      //     $$data.contextNode.data.dhtPageData = this.dhtPageData;
      //     $$data.contextNode.data.dhtContainerApi = this.dhtContainerApi;
      // });
      // actions.history.ignore().setProp(($$data) => {
      //     $$data.contextNode.data.dhtPageData = this.dhtPageData;
      //     $$data.contextNode.data.dhtContainerApi = this.dhtContainerApi;
      // });

      actions.history.ignore().setProp(id, ($$data) => {
          $$data.contextNode.data.dhtPageData = this.dhtPageData;
          $$data.contextNode.data.dhtContainerApi = this.dhtContainerApi;
      });


    },

    /**
     * 初始化数据
     */
    initializeData() {
      // 如果有初始分类或搜索词，触发列表加载
      if (this.dhtPageData.category.id || this.dhtPageData.search.keyword) {
        this.$nextTick(() => {
          this.refreshList();
        });
      }
    },

    /**
     * 处理分类变更
     * @param {Object} categoryData - 分类数据 { id, name, path }
     */
    handleCategoryChange(categoryData) {
      console.log('Container: 处理分类变更', categoryData);

      // 更新分类数据
      this.dhtPageData.category = {
        id: categoryData.id,
        name: categoryData.name || '',
        path: categoryData.path || []
      };

      const { id } = this.useInternalNode();
      const { actions } = this.useInternalEditor();
      actions.history.ignore().setProp(id, ($$data) => {
          $$data.contextNode.data.dhtPageData.category = this.dhtPageData.category;
      });

      // 更新组件状态
      this.dhtPageData.components.categoryTree.selected = categoryData.id;

      // 重置筛选条件和分页
      this.resetFiltersAndPagination();

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理搜索变更
     * @param {Object} searchData - 搜索数据 { keyword }
     */
    handleSearchChange(searchData) {
      console.log('Container: 处理搜索变更', searchData);

      // 更新搜索数据
      this.dhtPageData.search = {
        keyword: searchData.keyword || '',
        timestamp: Date.now()
      };

      // 重置筛选条件和分页
      this.resetFiltersAndPagination();

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理筛选变更
     * @param {Object} filterData - 筛选数据
     */
    handleFilterChange(filterData) {
      console.log('Container: 处理筛选变更', filterData);

      // 合并筛选条件
      this.dhtPageData.filters = {
        ...this.dhtPageData.filters,
        ...filterData
      };

      // 重置分页到第一页
      this.dhtPageData.list.page = 1;

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理列表刷新
     * @param {Object} options - 刷新选项
     */
    handleListRefresh(options = {}) {
      console.log('Container: 处理列表刷新', options);

      // 更新分页信息
      if (options.page) {
        this.dhtPageData.list.page = options.page;
      }
      if (options.pageSize) {
        this.dhtPageData.list.pageSize = options.pageSize;
      }

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理全局重置
     */
    handleGlobalReset() {
      console.log('Container: 处理全局重置');

      // 重置所有数据到初始状态
      this.dhtPageData.category = {
        id: null,
        name: '',
        path: []
      };

      this.dhtPageData.search = {
        keyword: '',
        timestamp: 0
      };

      this.resetFiltersAndPagination();

      // 通知所有子组件重置
      this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.CATEGORY_RESET);
      this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.SEARCH_RESET);
      this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.FILTER_RESET);

      // 清空列表
      this.dhtPageData.list.data = [];
      this.dhtPageData.list.total = 0;
    },

    /**
     * 重置筛选条件和分页
     */
    resetFiltersAndPagination() {
      // 重置筛选条件
      this.dhtPageData.filters = {
        price: { min: null, max: null },
        brand: [],
        attributes: {},
        custom: {}
      };

      // 重置分页
      this.dhtPageData.list.page = 1;

      // 通知筛选组件重置
      this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.FILTER_RESET);
    },

    /**
     * 刷新列表数据
     */
    async refreshList() {
      try {
        // 设置加载状态
        this.dhtPageData.list.loading = true;
        this.dhtPageData.list.error = null;

        // 通知列表组件开始加载
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_LOADING, true);

        // 构建查询参数
        const queryParams = this.getQueryParams();

        console.log('Container: 刷新列表', queryParams);

        // 这里应该调用实际的API
        // const response = await this.callListApi(queryParams);

        // 模拟API调用
        const response = await this.mockApiCall(queryParams);

        // 更新列表数据
        this.dhtPageData.list.data = response.data || [];
        this.dhtPageData.list.total = response.total || 0;

        // 通知列表组件数据更新
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_DATA_UPDATE, {
          data: this.dhtPageData.list.data,
          total: this.dhtPageData.list.total,
          page: this.dhtPageData.list.page,
          pageSize: this.dhtPageData.list.pageSize
        });

      } catch (error) {
        console.error('Container: 列表刷新失败', error);
        this.dhtPageData.list.error = error.message || '加载失败';
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_ERROR, error);
      } finally {
        this.dhtPageData.list.loading = false;
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_LOADING, false);
      }
    },

    /**
     * 获取当前查询参数
     * @returns {Object} 查询参数对象
     */
    getQueryParams() {
      return {
        // 分类参数
        categoryId: this.dhtPageData.category.id,

        // 搜索参数
        keyword: this.dhtPageData.search.keyword,

        // 筛选参数
        filters: {
          ...this.dhtPageData.filters
        },

        // 分页参数
        page: this.dhtPageData.list.page,
        pageSize: this.dhtPageData.list.pageSize,

        // 排序参数
        sortBy: this.dhtPageData.components.shopList.sortBy
      };
    },

    /**
     * 重置指定组件数据
     * @param {String} componentName - 组件名称
     */
    resetComponentData(componentName) {
      switch (componentName) {
        case 'category':
          this.dhtPageData.category = { id: null, name: '', path: [] };
          this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.CATEGORY_RESET);
          break;
        case 'search':
          this.dhtPageData.search = { keyword: '', timestamp: 0 };
          this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.SEARCH_RESET);
          break;
        case 'filters':
          this.resetFiltersAndPagination();
          break;
        default:
          console.warn(`Container: 未知的组件名称 ${componentName}`);
      }
    },

    /**
     * 模拟API调用（实际项目中应该替换为真实API）
     * @param {Object} params - 查询参数
     * @returns {Promise} API响应
     */
    async mockApiCall(params) {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 模拟返回数据
      return {
        data: [
          { id: 1, name: '商品1', price: 100, category: params.categoryId },
          { id: 2, name: '商品2', price: 200, category: params.categoryId },
          { id: 3, name: '商品3', price: 300, category: params.categoryId }
        ].filter(item => {
          // 模拟搜索过滤
          if (params.keyword) {
            return item.name.includes(params.keyword);
          }
          return true;
        }),
        total: 100,
        page: params.page,
        pageSize: params.pageSize
      };
    }
  }
};
</script>

<style lang="less" scoped>
.dhtbiz-container-product-list {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;

  // 确保子组件能够正确布局
  display: flex;
  flex-direction: column;

  // 处理溢出
  overflow: hidden;

  // 为beecraft设计器提供基础样式
  &.beecraft-design-mode {
    border: 1px dashed #d9d9d9;
    background-color: #fafafa;

    &:hover {
      border-color: #1890ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dhtbiz-container-product-list {
    flex-direction: column;
  }
}
</style>


