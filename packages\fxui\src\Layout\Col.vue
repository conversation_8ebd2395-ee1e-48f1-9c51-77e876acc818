<script>
    import { Vue } from '@beecraft/shared';

    const FxCol = {
        extends: Vue.options.components.FxCol
    }

    FxCol.beecraft = function() {
        return {
            name: 'FxCol',
            displayName: '列',
            $$data: {
                isCanvas: true
            },
            rules: {
                canSelect: () => false,
                canDrag: () => false,
            },
            hooks: {
                beforeSelect(e, node, { query, actions }) {
                    const parentNode = query.node(node.parent).get();

                    if(parentNode.name === 'FxRow') {
                        actions.setNodeEvent('selected', [parentNode.id])
                    }
                }
            }
        }
    }

    export default FxCol;
</script>
<style lang="less">
    
</style>