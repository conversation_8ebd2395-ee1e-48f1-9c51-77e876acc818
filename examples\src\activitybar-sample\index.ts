import Demo from './Demo';
import { init } from '@beecraft/engine';
import ActivityBarPlugin from './ActivityBarPlugin';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'Demo',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }]
            }]
        },
        plugins: [
            ActivityBarPlugin
        ]
    }
)