import Demo from './Demo';
import { init } from '@beecraft/engine';
import SetBarPlugin from './SetBarPlugin';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'De<PERSON>',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }]
            }],
            setbar: {
                related: {
                    nodeSchemaSettings: () => import('./nodeSchemaSettings.vue'),
                    optionsSchemaSettings: () => import('./optionsSchemaSettings.vue')
                }
            }
        },
        plugins: [
            SetBarPlugin
        ]
    }
)