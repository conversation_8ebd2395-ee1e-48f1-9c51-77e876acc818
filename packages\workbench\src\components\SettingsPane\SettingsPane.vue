<template>
    <div class="bc-settings-pane">
        <template v-if="type === 'node'">
            <NodeContext :id="settingsNodeId" :key="settingsNodeId" v-if="settingsNodeId">
                <template v-if="settingsRelated">
                    <div v-if="Array.isArray(settingsRelated) && settingsRelated.length">
                        <NodeElement v-for="item in settingsRelated" :key="item.rootNodeId" :id="item.rootNodeId"
                            :nodes="item.nodes"></NodeElement>
                    </div>
                    <component v-else :is="settingsRelated" :nodeId="selectedId"></component>
                </template>
            </NodeContext>
            <div v-else class="no-data">{{ $t('paasbiz.workbench.settingsPane.noData', '请在左侧画布中选择组件') }}</div>
        </template>
        <template v-else>
            <div v-if="Array.isArray(settingsRelated) && settingsRelated.length">
                <NodeElement v-for="item in settingsRelated" :key="item.rootNodeId" :id="item.rootNodeId"
                    :nodes="item.nodes"></NodeElement>
            </div>
            <component v-else :is="settingsRelated" :nodeId="selectedId"></component>
        </template>
    </div>
</template>
<script>
import { NodeContext } from '@beecraft/core';
import NodeElement from './NodeElement.vue';

export default {

    inject: ['useInternalEditor'],

    props: ['related', 'selectedId', 'type'],

    components: {
        NodeElement,
        NodeContext
    },

    created() {
        this.updateRelated();
    },

    watch: {
        selectedId() {
            this.updateRelated();
        }
    },

    computed: {
        // todo 待查明原因，当使用parseSerializedNodes，父组件每次重新渲染都会导致settingsPane重新渲染
        // 经验：不要视图将actions使用在计算属性、render中
        // // 设置面板渲染的scheme数据
        // settingsRelated() {
        //     const { related } = this;
        //     const { actions } = this.useInternalEditor();

        //     // return Array.isArray(related) ? actions.parseSerializedNodes(related, (node) => {
        //     //     node.data = {
        //     //         ...node.data,
        //     //         ...this.$attrs
        //     //     }
        //     // }) : related;
        //     return actions.parseSerializedNodes([])
        // },

        settingsNodeId() {
            return this.selectedId;
        }
    },

    methods: {
        updateRelated() {
            const { related } = this;
            const { actions } = this.useInternalEditor();

            this.settingsRelated = Array.isArray(related) ? actions.parseSerializedNodes(related, (node) => {
                node.data = {
                    ...node.data,
                    ...this.$attrs
                }
            }) : related;
        }
    }
}
</script>
<style lang="less" scoped>
.bc-settings-pane {
    // padding: 0 12px;
}

.no-data {
    // padding-top: 50px;
    margin: 50px 0;
    text-align: center;
    font-size: 12px;
}
</style>