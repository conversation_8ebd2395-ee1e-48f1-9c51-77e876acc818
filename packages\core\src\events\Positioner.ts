import {
    Node,
    NodeId,
    NodeInfo,
    DropPosition,
    NodeSelectorWrapper
} from '../interfaces';
import { EditorStore } from '../editor/store';
import { getDOMInfo } from '../shared/getDOMInfo';
import { getNodesFromSelector } from '../shared/getNodesFromSelector';
import findPosition from './findPosition';


// Hack: to trigger dragend event immediate
// Otherwise we would have to wait until the native animation is completed before we can actually drop an block
const documentDragoverEventHandler = (e: DragEvent) => {
    e.preventDefault();
};


export default class Positioner {

    static BORDER_OFFSET = 10;
    // static BORDER_OFFSET = 30;

    private currentDropTargetId: NodeId | null;

    private currentDropTargetCanvasAncestorId: NodeId | null;

    private currentIndicator: any = null;

    // 允许放置的节点id
    private currentTargetId: NodeId | null;


    private currentTargetChildDimensions: NodeInfo[] | null;

    private dragError: string | null;
    private draggedNodes: NodeSelectorWrapper[];

    private onScrollListener: (e: Event) => void;

    constructor(readonly store: EditorStore, readonly dragTarget: any) {
        this.currentDropTargetId = null;
        this.currentDropTargetCanvasAncestorId = null;

        this.currentTargetId = null;
        this.currentTargetChildDimensions = null;

        this.currentIndicator = null;

        this.dragError = null;
        this.draggedNodes = this.getDraggedNodes();

        // this.validateDraggedNodes();

        this.onScrollListener = this.onScroll.bind(this);
        window.addEventListener('scroll', this.onScrollListener, true);
        window.addEventListener('dragover', documentDragoverEventHandler, false);
    }

    /**
     * 清除事件
     */
    cleanup() {
        window.removeEventListener('scroll', this.onScrollListener, true);
        window.removeEventListener('dragover', documentDragoverEventHandler, false);
    }

    private onScroll(e: Event) {

    }

    private getDraggedNodes() {
        const nodes = this.store.query.getNodes();

        if (this.dragTarget.type === 'new') {
            return getNodesFromSelector(
                nodes,
                this.dragTarget.tree.nodes[this.dragTarget.tree.rootNodeId]
            );
        }

        return getNodesFromSelector(
            nodes,
            this.dragTarget.nodes
        );

    }

    private validateDraggedNodes() {

    }

    /**
     * 是否靠近容器的边框
     */
    private isNearBorders(
        domInfo: ReturnType<typeof getDOMInfo>,
        x: number,
        y: number
    ) {
        const { top, bottom, left, right } = domInfo;

        if (
            top + Positioner.BORDER_OFFSET > y ||
            bottom - Positioner.BORDER_OFFSET < y ||
            left + Positioner.BORDER_OFFSET > x ||
            right - Positioner.BORDER_OFFSET < x
        ) {
            return true;
        }

        return false;
    }

    private isDiff(newPosition: DropPosition) {
        if (
            this.currentIndicator &&
            // todo ts 临时处理
            this.currentIndicator.placement.parent.id === (newPosition.parent as any).id &&
            this.currentIndicator.placement.index === newPosition.index &&
            this.currentIndicator.placement.where === newPosition.where
        ) {
            return false;
        }

        return true;
    }

    private getChildDimensions(newParentNode: Node) {
        const { query } = this.store;

        // Use previously computed child dimensions if newParentNode is the same as the previous one
        const existingTargetChildDimensions = this.currentTargetChildDimensions;
        if (
            this.currentTargetId === newParentNode.id &&
            existingTargetChildDimensions
        ) {
            return existingTargetChildDimensions;
        }

        return newParentNode.children.reduce((result: NodeInfo[], nodeId: NodeId) => {
            const { id, dom } = query.node(nodeId).get();

            if (dom) {
                result.push({
                    id,
                    ...getDOMInfo(dom),
                } as NodeInfo);
            }

            return result;
        }, [] as NodeInfo[]);

        return [];
    }

    private getCanvasAncestor(dropTargetId: NodeId) {
        const { query } = this.store;

        if (
            dropTargetId === this.currentDropTargetId &&
            this.currentDropTargetCanvasAncestorId
        ) {
            const node = query.node(this.currentDropTargetCanvasAncestorId).get();

            if (node) {
                return node;
            }
        }

        const getCanvas = (nodeId: NodeId): Node | null => {
            const nodeHelper = query.node(nodeId);
            const node = nodeHelper.get();

            // if (node && node.$$data.isCanvas) {
            //     return node;
            // }
            // 此处区别于源码，为了可以直接将指针定位到应该拖拽的区域内，此时需要通过rules规则直接获取到可能允许拖拽的区域
            if (node && node.$$data.isCanvas && (node.rules.canMoveIn?.(this.draggedNodes.map(({ node }) => node), node, nodeHelper) ?? true)) {
                return node;
            }

            if (!node?.parent) {
                return null;
            }

            return getCanvas(node?.parent);
        };

        return getCanvas(dropTargetId);
    }

    getIndicator() {
        return this.currentIndicator;
    }

    clearIndicator() {
        this.currentIndicator = null;
    }

    /**
     * 根据拖拽位置计算新指针
     */
    computeIndicator(dropTargetId: NodeId, x: number, y: number): any {
        const { query } = this.store;

        let newParentNode = this.getCanvasAncestor(dropTargetId);

        if (!newParentNode) {
            return;
        }

        this.currentDropTargetId = dropTargetId;
        this.currentDropTargetCanvasAncestorId = newParentNode.id;

        if (
            newParentNode.parent &&
            this.isNearBorders(getDOMInfo(newParentNode.dom), x, y)
        ) {
            // 此处区别于源码，为了可以直接将指针定位到应该拖拽的区域内，此时需要通过rules规则直接获取到可能允许拖拽的区域
            // newParentNode = query.node(newParentNode.parent).get();
            newParentNode = this.getCanvasAncestor(newParentNode.parent);
        }

        if (!newParentNode) {
            return;
        }

        this.currentTargetChildDimensions = this.getChildDimensions(newParentNode) as NodeInfo[];
        this.currentTargetId = newParentNode.id;

        const position = findPosition(
            newParentNode,
            this.currentTargetChildDimensions || [],
            x,
            y
        );

        if (!this.isDiff(position)) {
            return;
        }

        let error = this.dragError;

        if (!error) {
            query.node(newParentNode.id).isDroppable(
                this.draggedNodes.map(({ node }) => node),
                (dropError: any) => {
                    error = dropError;
                }
            );
        }

        const currentNodeId = newParentNode.children?.[position.index];
        const currentNode = query.node(currentNodeId).get();

        this.currentIndicator = {
            placement: {
                ...position,
                currentNode,
            },
            error,
        };

        return this.currentIndicator;
    }
}