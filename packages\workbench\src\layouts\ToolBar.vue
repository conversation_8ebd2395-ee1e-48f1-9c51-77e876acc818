<template>
    <div class="bc-tool-bar position-relative d-flex justify-content-between align-items-center">
        <div class="bc-tool-bar-left">
            <template v-for="item in lArea">
                <div class="bc-tool-item d-flex justify-content-center align-items-center cursor-pointer" @click="handleClick(item)">
                    <fx-tooltip effect="dark" :content="item.data?.description" placement="bottom">
                        <SvgIcon v-if="item.data.icon?.content" :size="16" :viewBox="'0 0 1024 1024'" :content="item.data.icon.content"> 
                        </SvgIcon>
                        <span v-else :class="`item-icon ${item.data?.icon}`"></span>
                    </fx-tooltip>
                </div>
            </template>
        </div>
        <div class="bc-tool-bar-center d-flex">
            <template v-for="item in cArea">
                <div class="bc-tool-item d-flex justify-content-center align-items-center cursor-pointer" @click="handleClick(item)">
                    <fx-tooltip effect="dark" :content="item.data?.description" placement="bottom">
                        <SvgIcon v-if="item.data.icon?.content" :size="16" :viewBox="'0 0 1024 1024'" :content="item.data.icon.content"> 
                        </SvgIcon>
                        <span v-else :class="`item-icon ${item.data?.icon}`"></span>
                    </fx-tooltip>
                </div>
            </template>
        </div>
        <div class="bc-tool-bar-right d-flex">
            <template v-for="item in rArea">
                <div class="bc-tool-item d-flex justify-content-center align-items-center cursor-pointer" @click="handleClick(item)">
                    <fx-tooltip v-if="item.data?.icon" effect="dark" :content="item.data?.description" placement="bottom">
                        <SvgIcon v-if="item.data.icon?.content" :size="16" :viewBox="'0 0 1024 1024'" :content="item.data.icon.content"> 
                        </SvgIcon>
                        <span v-else :class="`item-icon ${item.data?.icon}`"></span>
                    </fx-tooltip>
                    <span v-else-if="item.data?.text">{{item.data.text}}</span>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
    import { SvgIcon } from '@beecraft/shared';

    export default {
        name: 'ToolBar',

        props: {
            area: Array
        },

        components: {
            SvgIcon
        },

        inject: ['useInternalEditor'],

        computed: {
            // 左间区域
            lArea() {
                return this.area ? this.area.filter(item => item.data?.align === 'left') : [];
            },

            // 中间区域
            cArea() {
                return this.area ? this.area.filter(item => item.data?.align === 'center') : [];
            },

            // 右侧区域
            rArea() {
                return this.area ? this.area.filter(item => item.data?.align === 'right') : [];
            }
        },

        methods: {
            handleClick(tool) {
                const editor = this.useInternalEditor();
                
                tool.onClick?.(tool, editor)
            }
        }
    }
</script>
<style lang="less" scoped>
    .bc-tool-bar {
        height: var(--bc-g-wb-size, 40px);
        width: 100%;
        background-color: #fff;
        box-shadow: 0px -1px 0px 0px #DEE1E8 inset;
        z-index: 1;
    }
    .bc-tool-item {
        width: 24px;
        height: 24px;
        margin: 0 4px;
        font-size: 14px;
        color: var(--color-special02);
        .item-icon:before {
            color: var(--color-special02);
        }
    }
    .bc-tool-bar-left,
    .bc-tool-bar-center,
    .bc-tool-bar-right {
        padding: 0 12px;
        box-sizing: border-box;
    }
</style>