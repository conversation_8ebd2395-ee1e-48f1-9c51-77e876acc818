<template>
    <div class="bc-set-bar" :class="rootClassName" v-if="setbar">
        <div class="bc-set-bar-content" v-if="isShow">
            <fx-tabs v-model="activeTab" @tab-click="handleClick">
                <template v-for="item in items">
                    <fx-tab-pane class="tab-pane" :key="item.name" :label="item.label" :name="item.name">
                        <SettingsPane v-if="Array.isArray(item.related)" :related="item.related"
                            :selectedId="selectedNode?.id" :type="item.type"></SettingsPane>
                        <template v-else>
                            <NodeContext v-if="item.type === 'node' && selectedNode && item.related"
                                :id="selectedNode.id" :key="selectedNode.id">
                                <component :is="item.related"></component>
                            </NodeContext>
                            <component v-else-if="item.related" :is="item.related"></component>
                            <div v-else class="no-data">{{ $t('beecraft.workbench.noConfItemAvailable', {},
        '暂无可配置项') }}
                            </div>
                        </template>
                    </fx-tab-pane>
                </template>
            </fx-tabs>
        </div>
        <div class="btn-group" v-if="workbench.type === 'floatingLayer'">
            <fx-button class="btn-item btn-close" size="small" plain icon="fx-icon-close" square @click="_handleToggle"
                v-if="isShow"></fx-button>
            <fx-button class="btn-item btn-config" size="small" plain icon="fx-icon-configuration" square
                @click="_handleToggle" v-else></fx-button>
        </div>
    </div>
</template>
<script>
import SettingsPane from '../components/SettingsPane';
import { NodeContext } from '@beecraft/core';
import { getCssVariable, observeDomRect } from '@beecraft/shared';

export default {
    name: 'SetBar',

    inject: ['useInternalEditor'],

    components: {
        SettingsPane,
        NodeContext
    },

    computed: {
        rootClassName() {
            const { type } = this.workbench;
            return {
                'floatingLayer': type === 'floatingLayer',
                'noShow': !this.isShow
            }
        },

        workbench() {
            const { workbench } = this.useInternalEditor((state) => ({ workbench: state.options?.workbench }));
            
            if(workbench.type === 'floatingLayer') {
                this.initBounds();
            }

            return workbench;
        },

        setbar() {
            return this.workbench?.setbar;
        },

        items() {
            const { items = [] } = this.setbar || {};

            return items.map(item => {
                return {
                    ...item,
                    related: this.getRelated(item)
                }
            })
        },

        selectedNode() {
            const { query } = this.useInternalEditor();
            const selectedNodeId = query.getEvent('selected').first();

            if (selectedNodeId) {
                const node = query.node(selectedNodeId).get();
                const { items = [] } = query.getOptions().workbench?.setbar || {};

                // 优先取缓存，没有缓存展示组件默认的设置区
                this.activeTab = this.activeTabCache[selectedNodeId] || node?.$$data?.defaultSetting || items.find(item => item.type === 'node')?.name;
                this.isShow = true;
                return node;
            }

            return null;
        },
    },

    data() {
        return {
            activeTab: '',
            isShow: false,
        }
    },

    created() {
        this.activeTabCache = {};
        this.activeTab = this.setbar.activeTab;
        this.isShow = this.workbench.type !== 'floatingLayer';
        this.allowToggle = true;
    },

    mounted() {
        this.initBounds();
    },

    methods: {
        initBounds() {
            this.$container = this.$el.closest('.bc-workbench');
            this.uiBaseSize = getCssVariable(this.$container, 'bc-g-wb-size').slice(0, -2) * 1;

            this.syncWH();
            this.syncXY();
            this.observer = observeDomRect(this.$container, 'width', (newValue, preValue, entry) => {
                // this.updateBounds({
                //     x: width - 30 - this.uiBaseSize,
                //     y: 8
                // });
            });
        },
        handleClick() {
            if (this.selectedNode) {
                this.activeTabCache[this.selectedNode.id] = this.activeTab;
            }
        },

        getRelated({ name, related, component }) {
            const { query } = this.useInternalEditor();

            const { related: optionsRelated = {} } = query.getOptions().workbench?.setbar ?? {};
            const { related: nodeRelated = {} } = this.selectedNode ?? {};

            name = `${name}Settings`;

            // options.related > node.related > setbar.component > setbar.related
            return optionsRelated[name] || nodeRelated[name] || component || related;
        },

        _handleToggle() {
            if (this.allowToggle) {
                this.isShow = !this.isShow;
                this.syncWH();
            }
        },

        // 同步拖拽区域的宽高
        syncWH() {
            this.$nextTick(() => {
                const rect = this.$el.getBoundingClientRect();
                this.$emit('update:w', rect.width);
                this.$emit('update:h', rect.height);
            });
        },

        syncXY() {
            const rect = this.$container.getBoundingClientRect();
            this.$emit('update:x', rect.width - 30 - this.uiBaseSize);
            this.$emit('update:y', 8);
        }
    },

    beforeDestroy() {
        this.activeTabCache = {};
    }
}
</script>
<style lang="less" scoped>
.bc-set-bar {
    position: relative;
    box-sizing: border-box;
    padding-top: 1px;
    height: 100%;

    .bc-set-bar-content {
        width: 260px;
        height: 100%;
        border-left: 1px solid #DEE1E8;
        background-color: #fff;
        display: flex;
    }

    .tab-pane {
        height: 100%;
        overflow-y: auto;
        border: 0px solid var(--color-neutrals04, #eaebee); // ???
        padding: 12px;
    }

    .no-data {
        line-height: 300px;
        text-align: center;
        font-size: 12px;
        color: #94979c;
    }

    // .btn-group {
    // position: absolute;
    // top: 0;
    // right: 0;
    // height: var(--bc-g-wb-size, 40px);
    // line-height: var(--bc-g-wb-size, 40px);
    // z-index: 201;
    // }

    .btn-item {
        border: none;
        width: calc(var(--bc-g-wb-size, 40px) - 1px);
        height: calc(var(--bc-g-wb-size, 40px) - 1px);

        &+.btn-item {
            margin-left: 0;
        }
    }
}

.floatingLayer {
    height: unset;
    display: inline-block;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
    background: #fff;
    // .bc-set-bar-content,
    // .btn-group {
    // position: absolute;
    // top: calc(var(--bc-g-wb-size, 40px) + 8px);
    // right: 30px;
    // z-index: 201;
    // }

    .bc-set-bar-content {
        height: unset;
        max-height: calc(100% - 50px);
        min-height: 600px;
        border-left: none;
    }

    .btn-config {
        border-radius: 2px 0px 0px 2px;
        // box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
    }

    .btn-close {
        position: absolute;
        top: 0;
        right: 0;
    }
}
</style>
<style lang="less">
.bc-set-bar {
    .el-tabs {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .el-tabs__header {
        margin: 0;
    }

    .el-tabs__nav-wrap {
        padding: 0 8px;
    }

    .el-tabs__item {
        height: 40px;
        line-height: 40px;
    }

    .el-button i:before {
        color: var(--color-special02, rgb(115, 124, 140));
    }
}
</style>
