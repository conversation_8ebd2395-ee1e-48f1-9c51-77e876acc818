/**
 * @desc 格式化值
 */
export default function formatValue(data) {
    let result = {};
    Object.keys(data).forEach((propKey) => {
        const value = data[propKey];
        if(typeof value === 'object') {
            if(value.type) {
                if(value.type === 'i18n') {
                    // result[propKey] = data[propKey][];
                }
            }else {
                
            }
        }else {
            result[propKey] = value;
        }
    });
}