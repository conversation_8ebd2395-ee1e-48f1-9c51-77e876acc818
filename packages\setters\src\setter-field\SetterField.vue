<template>
    <div :class="`bc-setter-field bc-setter-${display}-field ${!setter ? 'bc-setter-group-field' : ''}`">
        <div class="bc-setter-field-head" v-if="label">
            <div class="d-flex align-items-center justify-content-center">
                <div class="bc-setter-field-title">
                    {{ label }}
                </div>
                <fx-tooltip v-if="helpText" class="bc-setter-field-help" effect="dark" :content="helpText" placement="top">
                    <span class="fx-icon-question help-icon"></span>
                </fx-tooltip>
            </div>
            <div class="d-flex align-items-center justify-content-center">
                <fx-tooltip
                    v-if="cAllowLock"
                    class="bc-setter-field-help"
                    effect="dark"
                    :content="!isLock ? $t('paasbiz.site.setters.unlockStyle', '独立设置，与主题解绑') : $t('paasbiz.site.setters.lockStyle', '与主题绑定')"
                    placement="top"
                >
                    <div class="cursor-pointer">
                        <svg v-if="!isLock" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M8.75 8.75H11.6667V9.91667H9.91667V11.6667H8.75V8.75ZM2.91667 2.91667H0V1.75H1.75V0H2.91667V2.91667ZM9.54567 7.89571L8.72066 7.07076L9.54567 6.24581C10.6847 5.10679 10.6847 3.26005 9.54567 2.12102C8.40659 0.981995 6.55988 0.981995 5.42086 2.12102L4.5959 2.94598L3.77094 2.12102L4.5959 1.29607C6.19051 -0.298573 8.77596 -0.298573 10.3706 1.29607C11.9652 2.89071 11.9652 5.47616 10.3706 7.07076L9.54567 7.89571ZM7.89571 9.54567L7.07076 10.3706C5.47616 11.9652 2.89071 11.9652 1.29607 10.3706C-0.298573 8.77596 -0.298573 6.19051 1.29607 4.5959L2.12102 3.77094L2.94598 4.5959L2.12102 5.42086C0.981995 6.55988 0.981995 8.40659 2.12102 9.54567C3.26005 10.6847 5.10679 10.6847 6.24581 9.54567L7.07076 8.72066L7.89571 9.54567ZM7.48323 3.35846L8.30818 4.18342L4.18342 8.30818L3.35846 7.48323L7.48323 3.35846Z" fill="#181C25"/>
                        </svg>
                        <svg v-else xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M9.44557 7.79563L8.62062 6.97068L9.44557 6.14573C10.5846 5.00671 10.5846 3.15997 9.44557 2.02094C8.30656 0.881908 6.45978 0.881908 5.32077 2.02094L4.49581 2.8459L3.67085 2.02094L4.49581 1.19598C6.09047 -0.39866 8.67587 -0.39866 10.2705 1.19598C11.8652 2.79062 11.8652 5.37602 10.2705 6.97068L9.44557 7.79563ZM7.79562 9.44559L6.97067 10.2705C5.37607 11.8651 2.79062 11.8651 1.19598 10.2705C-0.39866 8.67588 -0.39866 6.09043 1.19598 4.49582L2.02094 3.67085L2.8459 4.49582L2.02094 5.32078C0.881908 6.45979 0.881908 8.30651 2.02094 9.44559C3.15996 10.5846 5.0067 10.5846 6.14572 9.44559L6.97067 8.62058L7.79562 9.44559ZM7.38314 3.25838L8.20815 4.08333L4.08333 8.2081L3.25837 7.38315L7.38314 3.25838Z" fill="#181C25"/>
                        </svg>
                    </div>
                </fx-tooltip>
            </div>
        </div>
        <div class="bc-setter-field-body">
            <component
                ref="field"
                v-if="setter"
                :is="setter.resource || setter.component"
                v-bind="{ ...setter.data, value: value, id: id, nodeId: nodeId }"
                @change="handleChange"
                @delete="handleDelete"
                @hook:mounted="lockStyle"
            >
            </component>
            <slot></slot>
        </div>
    </div>
</template>
<script>
import * as SetterComponents from '../setters';
import { getRangesFromTarget, setRangesToTarget } from '../shared/formatRanges';

export default {

    name: 'SetterField',

    props: {
        label: {
            type: String,
            required: false
        },
        display: {
            type: String,
            default: 'inline'
        },
        setter: {
            type: Object
        },
        // TODO 不是当前节点的id
        id: {
            type: String
        },
        helpText: {
            type: String
        },
        canShow: {
            type: Function
        },
        canHide: {
            type: Function
        },
        allowLock: {
            type: [Boolean, Function],
            default: false
        },
    },

    components: SetterComponents,

    inject: ['useInternalEditor', 'useInternalNode'],

    data() {
        return {
            isLock: false,
            nodeId: ''
        }
    },

    computed: {
        value() {
            const { setter, useInternalNode } = this;
            const { nodeToValue } = this.$attrs;

            if (nodeToValue) {
                if (this.useInternalNode) {
                    return nodeToValue((this.useInternalNode(node => { return { node } })).node);
                }
            }

            let target;

            if (this.useInternalNode) {
                target = (this.useInternalNode(node => { return { node } })).node;
            } else {
                target = (this.useInternalEditor(({ options }) => { return { options } }));
            }

            return getRangesFromTarget(target, setter.ranges);
        },
        cAllowLock() {
            if (this.useInternalNode) {
                const { allowLock } = this;
                const { id } = this.useInternalNode();
                const { query, actions } = this.useInternalEditor();
                return typeof this.allowLock === 'function' ? allowLock(query.node(id).get(), { query, actions }) : allowLock;
            }
            return false;
        }
    },

    created() {
        const { options } = this.useInternalEditor(({ options }) => { return { options } });

        if (options.setters) {
            Object.assign(this.$options.components, options.setters);
        }
        this.nodeId = this.useInternalNode?.().id;
    },

    methods: {
        handleChange(value) {
            const { useInternalEditor, setter } = this;
            const { onChange, valueToNode } = this.$attrs;

            if (onChange) {
                onChange(this.id, value, this.useInternalNode?.().id, this.useInternalEditor());
                return;
            }

            if (!setter) {
                return;
            }

            if(valueToNode) {
                value = valueToNode(value, this.useInternalNode?.().id, this.useInternalEditor());
            }

            const [prop, ...dataRanges] = setter.ranges;

            if (this.useInternalNode) {
                const { actions, id } = this.useInternalNode();
                if (prop === 'data') {
                    actions.setCustom((data) => {
                        setRangesToTarget(data, dataRanges, value);
                    });
                } else if (prop === '$$data') {
                    actions.setProp(($$data) => {
                        setRangesToTarget($$data, dataRanges, value);
                    });
                }
            } else {
                const { actions, id } = this.useInternalEditor();
                actions.setOptions((options) => {
                    setRangesToTarget(options, dataRanges, value);
                });
            }
            this.lockStyle();
        },
        lockStyle() {
            this.isLock = this.$refs.field?.isEmpty?.();
        }
    }

}
</script>
<style lang="less">
.bc-setter-group-field {
    &>.bc-setter-field-head {
        height: 24px;
        margin-bottom: 8px;

        &>.bc-setter-field-title {
            font-size: 16px;
            font-weight: 700;
            text-align: left;
        }
    }

    // .bc-setter-group-field {
    //     &>.bc-setter-field-head {
    //         margin: 12px 0;
    //         height: 20px;
    //         &>.bc-setter-field-title {
    //             font-size: 14px;
    //             line-height: 20px;
    //             font-weight: 500;
    //             text-align: left;
    //         }
    //     }
    // }
}

.bc-setter-field {
    margin-bottom: 16px;
}

.bc-setter-field-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 20px;
    margin-bottom: 2px;

    &>.bc-setter-field-title {
        font-size: 14px;
    }
}

.bc-setter-inline-field {
    display: flex;
    align-items: center;
    margin: 12px 0;

    .bc-setter-field-head {
        width: 70px;
        margin: 0;
    }

    .bc-setter-field-body {
        flex: 1;
    }
}
.bc-setter-divider-field {
    border-bottom: 1px solid var(--color-neutrals07);
}
</style>
<style lang="less" scoped>
.help-icon {
    margin-left: 5px;

    &:before {
        font-size: 15px;
    }
}
</style>