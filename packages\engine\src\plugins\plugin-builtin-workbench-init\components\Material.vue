<template>
    <div class="bc-material">
        <div class="bc-material-top">
            <fx-input :placeholder="$t('请输入')" v-model="keyword" size="mini" clearable is-search @change="handleChange"
                @on-search="handleSearch"></fx-input>
        </div>
        <div class="bc-material-content">
            <template v-if="cMaterials?.length">
                <fx-collapse :value="cMaterials.map(group => group.title)" isCard :isArrowRight="false">
                    <fx-collapse-item v-for="(group, index) in cMaterials" :key="index" :name="group.title"
                        :title="group.title">
                        <!-- <div class="bc-material-group-title d-flex justify-content-center align-items-center" slot="title">
                            <span class="fx-icon-unfold-2"></span>
                            {{group.title}}
                        </div> -->
                        <!-- <div slot="arrow"></div> -->
                        <div class="bc-material-list">
                            <template v-for="(item, index) in group.children">
                                <Blueprint :key="item.name" v-if="item.template" class="bc-material-item"
                                    component="div" :template="item.template">
                                    <div class="d-flex align-items-center">
                                        <i v-if="item.icon" :class="item.icon" class="icon mr-2"></i>
                                        <div class="text-truncate flex-1" v-html="item.title"></div>
                                    </div>
                                </Blueprint>
                                <div v-else class="bc-material-item bc-material-item-disabled text-truncate">
                                    <div class="d-flex align-items-center">
                                        <i v-if="item.icon" :class="item.icon" class="icon mr-2"></i>
                                        <div class="text-truncate flex-1" v-html="item.title"></div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </fx-collapse-item>
                </fx-collapse>
            </template>
            <div v-else class="bc-material-nodata">{{$t('beecraft.workbench.noContent', {}, '暂无匹配内容')}}</div>
        </div>
    </div>
</template>
<script>
import { Blueprint } from '@beecraft/core';

export default {

    name: 'Material',

    components: {
        Blueprint
    },

    inject: ['useInternalEditor'],

    props: {
        materials: {
            default: Array
        }
    },

    data() {
        return {
            keyword: ''
        }
    },

    computed: {
        cMaterials() {
            const { keyword, materials = [] } = this;

            return materials.reduce((memo, group) => {
                let children = [];

                if (group.children?.length) {
                    children = group.children.filter(item => {
                        return !keyword || item.title.indexOf(keyword) > -1;
                    });
                    children = children.map(item => {
                        const count = this._getNodeCountByType(item.name);
                        const { template } = this.useInternalEditor((state) => {
                            const { beecraft } = state.options.resolver[item.name] || {};
                            const defaultTemplate = { name: item.name };
                            let template = beecraft?.()?.$$data?.template || defaultTemplate;

                            template = {
                                ...template,
                                data: {
                                    ...(template.data || {}),
                                    ...(item.data || {}),
                                },
                                $$data: {
                                    ...(template.$$data || {}),
                                    ...(item.$$data || {}),
                                },
                                source: item.source
                            }

                            return {
                                template
                            };
                        });

                        return {
                            title: keyword ? item.title.replaceAll(keyword, `<span style="color: var(--color-primary06);">${keyword}</span>`) : item.title,
                            icon: item.icon,
                            template: count < (item.limit || Infinity) ? template : null,
                            name: item.name
                        }
                    })
                }

                if (children.length) {
                    memo.push({
                        title: group.title,
                        children
                    })
                }
                return memo;
            }, []);
        }
    },

    methods: {
        handleSearch() {

        },

        handleChange(val) {
            this.keyword = val;
        },

        _getNodeCountByType(type) {
            const { query } = this.useInternalEditor();
            const nodes = query.getNodes();

            let count = 0;
            Object.keys(nodes).forEach(id => {
                if (nodes[id].name === type) {
                    count++;
                }
            });

            return count;
        }
    }
}
</script>
<style lang="less" scoped>
.bc-material-top {
    padding: 10px 0;
    // border-bottom: 1px solid #ebeef5;
}

.bc-material-group-title {
    font-size: 13px;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 0px;
    text-align: left;
}

.bc-material-item {
    border: 1px solid #DEE1E8;
    padding: 0px 8px 0px 8px;
    border-radius: 4px;
    height: 28px;
    line-height: 28px;
    box-sizing: border-box;
    cursor: move;

    &-disabled {
        background-color: #ebedf2;
        cursor: not-allowed;
    }
}

.bc-material-nodata {
    text-align: center;
    line-height: 160px;
}

.bc-material-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(98px, 1fr));
    grid-gap: 8px;
}

.icon {
    display: inline-block;
}
</style>
<style lang="less">
.bc-material {

    .is-card {
        margin-bottom: 0;

        .el-collapse-item__header {
            height: 20px;
            line-height: 20px;
            border-bottom: none;
            padding: 0;
            font-weight: 600;
            background-color: #fff;
            color: var(--color-neutrals15, #91959e);

            &>span {
                display: inline-block;
                width: 20px;
            }

            .el-collapse-item__arrow:before {
                color: var(--color-neutrals13);
                font-size: 12px;
            }
        }

        .el-collapse-item {
            border: none;
        }

        .el-collapse-item__content {
            padding: 8px 0px;
        }

        .el-collapse-item__wrap {
            border-top: unset;
        }
        
    }
}
</style>