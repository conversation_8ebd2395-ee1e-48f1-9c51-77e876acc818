// 创建节点时用的模板
const template = {
    name: 'Demo'
}

const settings = [{
    name: 'Setter<PERSON>ield',
    data: {
        label: '测试1',
        display: 'block'
    },
    children: [{
        name: 'SetterField',
        data: {
            label: '测试1',
            display: 'block'
        },
        children: [{
            name: 'SetterField',
            data: {
                label: '单行文本',
                display: 'block',
                setter: {
                    component: 'StringSetter',
                    ranges: ['data', 'text'],
                    onChange({actions}) {
                        
                    }
                }
            }
        },{
            name: 'SetterField',
            data: {
                label: '单行文本',
                display: 'block',
                setter: {
                    component: 'ArraySetter',
                    ranges: ['children']
                }
            }
        }]
    },{
        name: 'SetterField',
        data: {
            label: '单行文本',
            display: 'block',
            setter: {
                component: 'StringSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '颜色',
            display: 'block',
            setter: {
                component: 'ColorPickerSetter',
                ranges: ['data', 'color']
            }
        }
    },{
        name: 'Setter<PERSON>ield',
        data: {
            label: '多行文本',
            setter: {
                component: 'TextareaSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '选择器',
            setter: {
                component: 'SelectSetter',
                ranges: ['data', 'text'],
                data: {
                    options: [{
                        value: '选项1',
                        label: '黄金糕'
                    }, {
                        value: '选项2',
                        label: '双皮奶'
                    }]
                }
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '开关',
            setter: {
                component: 'SwitchSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        name: 'SetterField',
        data: {
            label: '自定义',
            setter: {
                component: 'SwitchSetter',
                ranges: ['data', 'text'],
                resource: {
                    render: (h: any) => h('div', 2333)
                }
            }
        }
    }]
}];

export default function(){
    return {
        type: 'Demo',
        name: $t('测试'),
        data: {
            text: ''
        },
        $$data: {
            isCanvas: true,
            template,
            title: $t('测试')
        },
        related: {
            // settings: {
            //     render: h => h('div', 23333)
            // }
            attributeSettings: {
                render: h => h('div', 'attribute')
            },
            styleSettings: settings,
        },
        hooks: {
            // 此处可以进行关联关系绑定
            afterRender({ id, query }) {
                const { mediator } = query.getOptions();
                const node = query.node(id).get();
                
                if (node.name === 'Demo') {
                    const vm = query.instance(id);

                    vm.$on('change', (...args) => {
                        mediator.$emit(`${node.name}.change`, ...args);
                        mediator.$emit(`${node.id}.change`, ...args);
                    });

                    mediator.$on('Demo.change', () => {
                        vm.update();
                    });
                }
            }
        }
    }
}