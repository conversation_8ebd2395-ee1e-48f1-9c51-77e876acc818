<template>
    <div class="bc-layout-col" :style="`width: ${this.width};`">
        <slot></slot>
    </div>
</template>
<script lang="js">
export default {
    beecraft: function () {
        return {
            name: 'LayoutCol',
            $$data: {
                noWrapper: true,
                isCanvas: true
            },
            
        }
    },
    props: ['width']
}
</script>
<style lang="less" scoped>
.bc-layout-col {
    border-right: 1px solid #DEE1E8;
}

.bc-layout-col:last-child {
    border-right: unset;
}
</style>