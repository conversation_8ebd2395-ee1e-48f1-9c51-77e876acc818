<template>
    <div class="bc-layout-container d-flex flex-column">
        <slot></slot>
    </div>
</template>
<script lang="js">
export default {
    beecraft: function () {
        return {
            name: 'LayoutContainer',
            $$data: {
                template: {
                    name: 'LayoutContainer',
                    children: [{
                        name: 'LayoutRow',
                        children: [{
                            name: 'LayoutCol',
                            data: {
                                width: '100%'
                            }
                        }]
                    }, {
                        name: 'LayoutRow',
                        children: [{
                            name: 'LayoutCol',
                            data: {
                                width: '70%'
                            }
                        }, {
                            name: 'LayoutCol',
                            data: {
                                width: '30%'
                            }
                        }]
                    }]
                },
                noWrapper: true,
            },
            
        }
    }
}
</script>
<style lang="less" scoped></style>