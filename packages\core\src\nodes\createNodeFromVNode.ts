

// export function createNodeFromVNode(editor: Editor, vnode: VNode, parent: Nullable<Node>): Nullable<Node> {
    
//     if (!vnode.componentOptions) {
//         return null;
//     }

//     // todo node tag
//     const type = vnode.componentOptions.tag;
//     const $$typeof = CraftorSymbols.for(type!);

//     let data = vnode.componentOptions.data;

//     if ($$typeof === CRAFTOR_CANVAS_TYPE && vnode.data?.attrs) {
//         data = { ...data, ...vnode.data.attrs || {} };
//     }

//     const {rules, addition, defaultProps} = getProvider(type!, data!, editor);
//     const nodeProps = { ...defaultProps, ...data };

//     // todo Node构造函数调整后缺少
//     const node = Node.unserialize(editor, {
//         type: type as string,
//         data: nodeProps
//     });

//     const vnodeChildren = vnode.componentOptions.children;
//     const children = vnodeChildren
//         ? vnodeChildren.map((childVNode) => createNodeFromVNode(editor, childVNode, node))
//         .filter((childNode) => !!childNode)
//         : [];

//         node.children = children as Node[];

//     return node;
// }


export function createNodeFromVNode() {
    
}