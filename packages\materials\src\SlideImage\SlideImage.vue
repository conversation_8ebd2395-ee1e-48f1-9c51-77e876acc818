<template>
    <div class="bc-slide-image" :class="{ 'bc-slide-image--isFullHeight': isFullHeight }">
        <fx-carousel ref="originInstance" :height="isFullHeight ? '' : `${height}px`" :direction="direction" :type="type"
            :autoplay="autoplay" v-if="show">
            <fx-carousel-item v-for="(item, index) in items" :key="index">
                <img v-if="item.img" :src="getImgUrl(item.img)" class="bc-slide-item-img" @click="onClick(item)"></img>
                <div v-else class="placeholder">
                    <img
                        src="https://a9.fspage.com/FSR/frontend/html/paas/appcustomization/images/lunbo.291a831e.png"></img>
                </div>
            </fx-carousel-item>
        </fx-carousel>
    </div>
</template>
<script>
import { getImgUrl } from './utils';

const warn = (content) => {
    console.log(
        `%c beecraft %c ${content} `,
        'padding: 2px 1px; border-radius: 3px 0 0 3px; color: #fff; background: #606060; font-weight: bold;font-size: 10px;',
        'padding: 2px 1px; border-radius: 0 3px 3px 0; color: #fff; background: #ff522a; font-weight: bold;font-size: 10px;',
    )
}
export default {
    props: {
        items: {
            type: Array
        },
        direction: {
            type: String
        },
        type: {
            type: String
        },
        // 是否自动播放
        autoplay: {
            type: Boolean
        },
        // 最大上传限制
        maxCount: {
            type: Number
        },
        // todo 高度
        height: {
            type: Number
        },
        isFullHeight: {
            type: Boolean
        }
    },
    watch: {
        type() {
            this.forceRerender();
        },
        direction() {
            this.forceRerender();
        }
    },
    data() {
        return {
            show: true
        }
    },
    methods: {
        // 直接切换fx-carousel-item组件的属性，存在渲染异常的问题。所以此处强制重新渲染
        forceRerender() {
            this.show = false;
            this.$forceUpdate();
            setTimeout(() => {
                this.show = true;
            });
        },

        onClick(item) {
            if (item.menuType === 3) {
                window.open(item.action)
            }
        },

        getImgUrl,
    },
    created() {
        const methods = Vue.options.components.FxCarousel.options.methods;
        Object.keys(methods).forEach(name => {
            this[name] = (...args) => {
                const originInstance = this.$refs.originInstance;

                if (originInstance) {
                    originInstance?.[name]?.apply(originInstance, args);
                }
            }
        })

        warn('轮播图组件提交前还需转换图片路径TNPath->NPath')
    },
    mounted() {
        console.log(this.$el);
    }
}
</script>
<style lang="less" scoped>
.placeholder {
    background-color: #ffd103;
    display: flex;
    justify-content: center;
    align-items: center;
}

.bc-slide-item-img {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.bc-slide-image--isFullHeight {
    height: 100%;

    .el-carousel {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /deep/.el-carousel__container {
        flex: 1;
    }
}
</style>