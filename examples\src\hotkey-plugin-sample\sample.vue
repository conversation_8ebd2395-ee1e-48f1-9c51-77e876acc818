<template>
    <div class="w-100 h-100">
        <Render v-bind="options"></Render>
        <div class="attention">因为缺乏主站依赖的各种资源，所以此处无法渲染各类组件</div>
    </div>
</template>
<script lang="js">
import { Render, builtinPlugins } from '@beecraft/engine';

export default {
    components: {
        Render
    },
    created() {
        this.options = {
            resolver: {},
            import: [{
                name: 'Test',
                source: 'PaasBiz'
            }],
            plugins: [
                builtinPlugins.FxiaokeComponentLibs
            ]
        }
    }
}
</script>
<style lang="less" scoped>
    .attention{
        
    }
</style>