import Demo from './Demo';
import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'Demo',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }]
            }],
            setbar: {
                related: {
                    globalSettings: [{
                        name: 'Set<PERSON><PERSON><PERSON>',
                        data: {
                            label: '测试1',
                            display: 'block'
                        },
                        children: [{
                            name: 'Set<PERSON><PERSON><PERSON>',
                            data: {
                                label: '测试1',
                                display: 'block'
                            },
                            children: [{
                                id: 'text',
                                name: 'Set<PERSON><PERSON><PERSON>',
                                data: {
                                    label: '单行文本',
                                    display: 'block',
                                    setter: {
                                        component: 'StringSetter',
                                        ranges: ['options', 'text']
                                    }
                                }
                            }]
                        }]
                    }]
                }
            }
        }
    }
)