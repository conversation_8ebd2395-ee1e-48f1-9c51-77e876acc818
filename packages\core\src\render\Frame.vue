<template>
    <component :is="frameRender" class="bc-frame w-100 h-100 overflow-y-auto">
        <template v-for="node in nodes">
            <NodeElement class="bc-root-node" :id="node.id" :key="node.id" :onRender="onRender"></NodeElement>
        </template>
        <RenderEditorIndicator></RenderEditorIndicator>
    </component>
</template>
<script>
import NodeElement from '../nodes/NodeElement.vue';
import RenderEditorIndicator from '../events/RenderEditorIndicator.vue';

export default {
    name: 'Frame',

    props: {
        component: {
            type: [Object, String]
        }
    },

    components: {
        NodeElement,
        RenderEditorIndicator
    },

    inject: ['useInternalEditor'],

    computed: {
        frameRender() {
            return this.component || 'div';
        },
        nodes() {
            const { query } = this.useInternalEditor();

            return query.getRootNodes();
        },
        onRender() {
            const { query } = this.useInternalEditor();

            return query.getOptions().onRender;
        },
    },

    mounted() {
        this.$el.addEventListener('mouseleave', this.onMouseleave);

        const {
            actions,
            query,
            initialData,
            onStateChange,
            normalizeNode
        } = this.useInternalEditor(state => {
            return {
                initialData: state.options.import,
                normalizeNode: state.options.normalizeNode
            }
        });

        if(initialData) {
            // actions.history.ignore().deserialize(initialData, normalizeNode);
            actions.history.ignore().deserialize(initialData);
        }

        actions.history.ignore().setOptions(options => {
            const rect = this.$el.getBoundingClientRect();
            options.frameOffset = {
                top: rect.top,
                left: rect.left
            };
        });

        onStateChange('options.enabled', (status) => {
            if (!status) {
                actions.clearEvents();
            }
        })
    },

    methods: {
        onMouseleave() {
            const { actions } = this.useInternalEditor();

            actions.setNodeEvent('hovered', null);
        }
    },

    beforeDestroy() {
        this.$el.removeEventListener('mouseleave', this.onMouseleave);
    }

}
</script>
<style lang="less" scoped>
.bc-frame {
    position: relative;
    //&::-webkit-scrollbar {
    //    width: 6px;
    //}

    //&::-webkit-scrollbar-thumb {
    //    width: 6px;
    //    background-color: #d2e0e1;
    //    border-radius: 4px;
    //}
}
</style>