const nodeHelper = {
  findFirstObjectByName(objs, name) {
    if (typeof objs !== 'object' || objs === null) {
      return null;
    }
  
    for (const key in objs) {
      if (objs.hasOwnProperty(key)) {
        const value = objs[key];
        if (typeof value === 'object' && value !== null && value?.name?.toLowerCase() === name?.toLowerCase()) {
          return value;
        }
      }
    }
    return null;
  },
  getContainerInstanceByName(query, name) {
    const containerNode = this.findFirstObjectByName(query.getNodes(), name);
    return containerNode ? query.instance(containerNode.id) : null;
  }
}

export default {
  hooks: {
    created(node) {
      console.log('category-tree running hooks created', node);        
    },
    
    beforeRender(node, { query, actions }) {
      console.log('category-tree running hooks beforeRender', node, query, actions);
      // const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      // console.log('containerInstance', containerInstance);
    },
    
    rendered(node, { query, actions }) {
      console.log('category-tree running hooks rendered', node, query, actions);
      
      // 获取容器组件实例
      const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      console.log('containerInstance', containerInstance);
      
      // 获取当前组件实例
      const vm = query.instance(node.id);
      
      debugger
      if (containerInstance && vm) {
        // 设置数据 - 只传递分类数据
        vm.category = containerInstance.dhtPageData.category;
        vm.dhtContainerApi = containerInstance.dhtContainerApi;
        // vm.dhtPageEventTypes = containerInstance.dhtContainerApi.getEventTypes();
        
        // 建立事件监听
        vm.$on('category-change', (data) => {
          // 使用容器组件的事件总线触发事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.CATEGORY_CHANGE, data);
          }
        });
      }
    }
  }    
}
