<template>
    <div
      :class="{
        'split-view': true,
        [orientation]: true,
        'separator-border': !!styles.separatorBorder
      }"
      :style="splitViewStyles"
    >
      <div class="sash-container">
        <template v-if="length > 1">
          <SashItem
            v-for="(item, index) in viewItems.slice(0, -1)"
            :key="item._uid"
            :id="item._uid"
            ref="sashItems"
            v-bind="sashProps"
            @didEnablementChange="onEnablementChange"
            @didStart="onSashStart"
            @didChange="onSashChange"
            @didEnd="onSashEnd(index)"
            @didReset="onSashReset"
          >
          </SashItem>
        </template>
      </div>
      <div class="scrollable-element">
        <div class="split-view-container" ref="viewContainer">
          <slot></slot>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import SashItem from "./SashItem.vue";
  
  import {
    pushToStart,
    pushToEnd,
    clamp,
    LayoutPriority,
    SashState
  } from "./utils";
  
  const DefaultStyles = {
    separatorBorder: "rgba(0, 0, 0, 0.1)",
    sashSize: "4px",
    sashHoverSize: "4px",
    sashHoverBorder: "#ff8000"
  };
  const State = {
    Idle: "Idle",
    Busy: "Busy"
  };
  const Orientation = {
    HORIZONTAL: "horizontal",
    VERTICAL: "vertical"
  };
  function range(arg, to) {
    let from = typeof to === "number" ? arg : 0;
    if (typeof to === "number") {
      from = arg;
    } else {
      from = 0;
      to = arg;
    }
    const result = [];
    if (from <= to) {
      for (let i = from; i < to; i++) {
        result.push(i);
      }
    } else {
      for (let i = from; i > to; i--) {
        result.push(i);
      }
    }
    return result;
  }
  export default {
    name: "SplitView",
    components: {
      SashItem
    },
    props: {
      // 方向
      orientation: {
        type: String,
        default: Orientation.HORIZONTAL // horizontal vertical
      },
      initSize: {
        type: Number,
        default: 0
      },
      // 样式
      styles: {
        type: Object,
        default: () => DefaultStyles
      },
      // 按比例布局
      proportionalLayout: {
        type: Boolean,
        default: true
      },
      getSashOrthogonalSize: {
        type: Function
      },
      inverseAltBehavior: {
        type: Boolean,
        default: false
      },
      scrollbarVisibility: String
    },
    provide() {
      return {
        splitView: this
      };
    },
    data() {
      return {
        contentSize: 0, // 内容尺寸
        size: 0, // 当前尺寸
        cachedVisibleSize: undefined, // 缓存的可见尺寸
  
        viewItems: [], // 视图项
  
        sashItems: [], // 分割线项
        sashDragState: null, // 分割线拖动状态
  
        startSnappingEnabled: true, // 开始捕捉启用
        endSnappingEnabled: true, // 结束捕捉启用
  
        orthogonalStartSash: null, // 正交开始分割线
        orthogonalEndSash: null, // 正交结束分割线
  
        state: State.Idle, // 状态
  
        proportions: undefined, // 比例
  
        resizeObserver: null // 观察者
      };
    },
    computed: {
      splitViewStyles() {
        function toDashCase(str) {
          return (
            "--" +
            str.replace(/[A-Z]/g, function (match) {
              return "-" + match.toLowerCase();
            })
          );
        }
        const styles = Object.assign({}, DefaultStyles, this.styles);
        Object.keys(styles).forEach((key) => {
          if (styles[key]) {
            styles[toDashCase(key)] = styles[key];
            delete styles[key];
          }
        });
        let overflowX = "hidden";
        let overflowY = "hidden";
        if (this.orientation === Orientation.HORIZONTAL) {
          overflowX = this.scrollbarVisibility ?? "auto";
        } else {
          overflowY = this.scrollbarVisibility ?? "auto";
        }
        styles.overflowX = overflowX;
        styles.overflowY = overflowY;
        return styles;
      },
      sashProps() {
        const props = {
          orthogonalStartSash: this.orthogonalStartSash,
          orthogonalEndSash: this.orthogonalEndSash
        };
        if (this.orientation === Orientation.HORIZONTAL) {
          props.orientation = "vertical";
          props.layoutProvider = {
            getVerticalSashLeft: this._getSashPosition,
            getVerticalSashHeight: this.getSashOrthogonalSize
          };
        } else {
          props.orientation = "horizontal";
          props.layoutProvider = {
            getHorizontalSashTop: this._getSashPosition,
            getHorizontalSashWidth: this.getSashOrthogonalSize
          };
        }
        return props;
      },
      length() {
        // 不要使用viewItems.length，要保证在
        return this.$slots.default?.length || 0;
      },
      minimumSize() {
        return this.viewItems.reduce((acc, cur) => {
          return acc + cur.minimumSize;
        }, 0);
      },
      maximumSize() {
        return this.length === 0
          ? Number.POSITIVE_INFINITY
          : this.viewItems.reduce((acc, cur) => {
              return acc + cur.maximumSize;
            }, 0);
      }
    },
    watch: {
      viewItems() {
        this.sashItems = this._getSashItems();
      },
      startSnappingEnabled() {
        this._updateSashEnablement();
      },
      endSnappingEnabled() {
        this._updateSashEnablement();
      }
    },
    mounted() {
      this.resizeObserver = new ResizeObserver(() => {
        this.$nextTick(() => {
          this.layout(this.getSize());
        });
      });
      this.resizeObserver.observe(this.$el);
  
      this.$nextTick(() => {
        this.sashItems = this._getSashItems();
        this.contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);
        this.layout(this.getSize());
      });
    },
    beforeDestroy() {
      this.resizeObserver?.disconnect();
    },
    methods: {
      getSize() {
        let size;
        if (!this.$el) return 0;
        if (this.orientation === Orientation.VERTICAL) {
          size = this.$el.getBoundingClientRect().height;
        } else {
          size = this.$el.getBoundingClientRect().width;
        }
        return size;
      },
      // 添加视图
      addView(view, size, index = this.viewItems.length, skipLayout) {
        this._doAddView(view, size, index, skipLayout);
      },
      // 删除视图
      removeView(index, sizing) {
        if (this.state !== State.Idle) {
          throw new Error("Cant modify splitview");
        }
  
        // this.state = State.Busy;
  
        if (index < 0 || index >= this.viewItems.length) {
          throw new Error("Index out of bounds");
        }
  
        // Remove view
        const viewItem = this.viewItems.splice(index, 1)[0];
  
        // Remove sash
        if (this.viewItems.length >= 1) {
          this.sashItems = this._getSashItems();
        }
  
        this._relayout();
        this.state = State.Idle;
  
        if (sizing?.type === "distribute") {
          this.distributeViewSizes();
        }
  
        return viewItem;
      },
      // 移动视图
      moveView(from, to) {
        if (this.state !== State.Idle) {
          throw new Error("Cant modify splitview");
        }
  
        const cachedVisibleSize = this.getViewCachedVisibleSize(from);
        const sizing =
          typeof cachedVisibleSize === "undefined"
            ? this.getViewSize(from)
            : Sizing.Invisible(cachedVisibleSize);
        const view = this.removeView(from);
        this.addView(view, sizing, to);
      },
      // 交换视图
      swapViews(from, to) {
        if (this.state !== State.Idle) {
          throw new Error("Cant modify splitview");
        }
  
        if (from > to) {
          return this.swapViews(to, from);
        }
  
        const fromSize = this.getViewSize(from);
        const toSize = this.getViewSize(to);
        const toView = this.removeView(to);
        const fromView = this.removeView(from);
  
        this.addView(toView, fromSize, from);
        this.addView(fromView, toSize, to);
      },
      isViewVisible(index) {
        if (index < 0 || index >= this.viewItems.length) {
          throw new Error("Index out of bounds");
        }
  
        const viewItem = this.viewItems[index];
        return viewItem.visible;
      },
      setViewVisible(index, visible) {
        if (index < 0 || index >= this.viewItems.length) {
          throw new Error("Index out of bounds");
        }
  
        const viewItem = this.viewItems[index];
        viewItem.setVisible(visible);
  
        this._distributeEmptySpace(index);
        this._layoutViews();
        this._saveProportions();
      },
      /**
       * Returns the {@link IView view}'s size previously to being hidden.
       */
      getViewCachedVisibleSize(index) {
        if (index < 0 || index >= this.viewItems.length) {
          throw new Error("Index out of bounds");
        }
  
        const viewItem = this.viewItems[index];
        return viewItem.cachedVisibleSize;
      },
      layout(size) {
        const previousSize = Math.max(this.size, this.contentSize);
        this.size = size;
  
        if (!this.proportions) {
          const indexes = range(this.viewItems.length);
          const lowPriorityIndexes = indexes.filter(
            (i) => this.viewItems[i].priority === LayoutPriority.Low
          );
          const highPriorityIndexes = indexes.filter(
            (i) => this.viewItems[i].priority === LayoutPriority.High
          );
  
          this._resize(
            this.viewItems.length - 1,
            size - previousSize,
            undefined,
            lowPriorityIndexes,
            highPriorityIndexes
          );
        } else {
          let total = 0;
  
          for (let i = 0; i < this.viewItems.length; i++) {
            const item = this.viewItems[i];
            const proportion = this.proportions[i];
  
            if (typeof proportion === "number") {
              total += proportion;
            } else {
              size -= item.size;
            }
          }
  
          for (let i = 0; i < this.viewItems.length; i++) {
            const item = this.viewItems[i];
            const proportion = this.proportions[i];
  
            if (typeof proportion === "number") {
              item.size = clamp(
                Math.round((proportion * size) / total),
                item.minimumSize,
                item.maximumSize
              );
            }
          }
        }
  
        this._distributeEmptySpace();
        this._layoutViews();
      },
  
      _saveProportions() {
        if (this.proportionalLayout && this.contentSize > 0) {
          this.proportions = this.viewItems.map((i) =>
            i.proportionalLayout ? i.size / this.contentSize : undefined
          );
        }
      },
      onEnablementChange() {},
      // 开始移动布局
      onSashStart(event) {
        const { sash, start, alt } = this.sashEventMapper(event);
        for (const item of this.viewItems) {
          item.enabled = false;
        }
        const index = this.$refs.sashItems.findIndex((item) => item === sash);
  
        const resetSashDragState = () => {
          const sizes = this.viewItems.map((i) => i.size);
          let minDelta = Number.NEGATIVE_INFINITY;
          let maxDelta = Number.POSITIVE_INFINITY;
          if (this.inverseAltBehavior) {
            alt = !alt;
          }
          let snapBefore, snapAfter;
          if (!alt) {
            const upIndexes = range(index, -1);
            const downIndexes = range(index + 1, this.viewItems.length);
            const minDeltaUp = upIndexes.reduce(
              (r, i) => r + (this.viewItems[i].minimumSize - sizes[i]),
              0
            );
            const maxDeltaUp = upIndexes.reduce(
              (r, i) => r + (this.viewItems[i].viewMaximumSize - sizes[i]),
              0
            );
            const maxDeltaDown =
              downIndexes.length === 0
                ? Number.POSITIVE_INFINITY
                : downIndexes.reduce(
                    (r, i) => r + (sizes[i] - this.viewItems[i].minimumSize),
                    0
                  );
            const minDeltaDown =
              downIndexes.length === 0
                ? Number.NEGATIVE_INFINITY
                : downIndexes.reduce(
                    (r, i) => r + (sizes[i] - this.viewItems[i].viewMaximumSize),
                    0
                  );
            const minDelta = Math.max(minDeltaUp, minDeltaDown);
            const maxDelta = Math.min(maxDeltaDown, maxDeltaUp);
            const snapBeforeIndex = this._findFirstSnapIndex(upIndexes);
            const snapAfterIndex = this._findFirstSnapIndex(downIndexes);
            if (typeof snapBeforeIndex === "number") {
              const viewItem = this.viewItems[snapBeforeIndex];
              const halfSize = Math.floor(viewItem.viewMinimumSize / 2);
  
              snapBefore = {
                index: snapBeforeIndex,
                limitDelta: viewItem.visible
                  ? minDelta - halfSize
                  : minDelta + halfSize,
                size: viewItem.size
              };
            }
  
            if (typeof snapAfterIndex === "number") {
              const viewItem = this.viewItems[snapAfterIndex];
              const halfSize = Math.floor(viewItem.viewMinimumSize / 2);
  
              snapAfter = {
                index: snapAfterIndex,
                limitDelta: viewItem.visible
                  ? maxDelta + halfSize
                  : maxDelta - halfSize,
                size: viewItem.size
              };
            }
          }
          this.sashDragState = {
            start,
            current: start,
            index,
            sizes,
            minDelta,
            maxDelta,
            alt,
            snapBefore,
            snapAfter
          };
        };
        resetSashDragState();
      },
      // 移动布局
      onSashChange(event) {
        const { current } = this.sashEventMapper(event);
        const {
          index,
          start,
          sizes,
          alt,
          minDelta,
          maxDelta,
          snapBefore,
          snapAfter
        } = this.sashDragState;
        this.sashDragState.current = current;
        const delta = current - start;
        const newDelta = this._resize(
          index,
          delta,
          sizes,
          undefined,
          undefined,
          minDelta,
          maxDelta,
          snapBefore,
          snapAfter
        );
  
        this._distributeEmptySpace();
        this._layoutViews();
      },
      // 结束移动布局
      onSashEnd(index) {
        this.$emit("didSashEnd", index);
        this._saveProportions();
  
        for (const item of this.viewItems) {
          item.enabled = true;
        }
      },
      onSashReset({ sash }) {
        const index = this.sashItems.findIndex((item) => item.sash === sash);
        const upIndexes = range(index, -1);
        const downIndexes = range(index + 1, this.viewItems.length);
        const snapBeforeIndex = this._findFirstSnapIndex(upIndexes);
        const snapAfterIndex = this._findFirstSnapIndex(downIndexes);
  
        if (
          typeof snapBeforeIndex === "number" &&
          !this.viewItems[snapBeforeIndex].visible
        ) {
          return;
        }
  
        if (
          typeof snapAfterIndex === "number" &&
          !this.viewItems[snapAfterIndex].visible
        ) {
          return;
        }
  
        this.$emit("didSashReset", index);
      },
      _getSashPosition(sash) {
        let position = 0;
  
        for (let i = 0; i < this.sashItems.length; i++) {
          position += this.viewItems[i].size;
  
          if (this.sashItems[i].sash === sash) {
            return position;
          }
        }
  
        return 0;
      },
      sashEventMapper(event) {
        const { sash, altKey } = event;
        let start, current;
        if (this.orientation === Orientation.VERTICAL) {
          start = event.startY;
          current = event.currentY;
        } else {
          start = event.startX;
          current = event.currentX;
        }
        return { sash, start, current, alt: altKey };
      },
  
      getViewSize(index) {
        if (index < 0 || index >= this.viewItems.length) {
          return -1;
        }
  
        return this.viewItems[index].size;
      },
  
      _doAddView(view, size, index = this.viewItems.length, skipLayout) {
        if (this.state !== State.Idle) {
          throw new Error("Cant modify splitview");
        }
  
        // this.state = State.Busy;
  
        this.viewItems.splice(index, 0, view);
  
        // Add sash
        if (this.viewItems.length > 1) {
          this.sashItems = this._getSashItems();
        }
  
        let highPriorityIndexes;
  
        if (typeof size !== "number" && size.type === "split") {
          highPriorityIndexes = [size.index];
        }
  
        if (!skipLayout) {
          this._relayout([index], highPriorityIndexes);
        }
  
        this.state = State.Idle;
  
        if (
          !skipLayout &&
          typeof size !== "number" &&
          size.type === "distribute"
        ) {
          this.distributeViewSizes();
        }
      },
  
      resizeView(index, size) {
        if (this.state !== State.Idle) {
          throw new Error("Cant modify splitview");
        }
  
        this.state = State.Busy;
  
        if (index < 0 || index >= this.viewItems.length) {
          return;
        }
  
        const indexes = range(this.viewItems.length).filter((i) => i !== index);
        const lowPriorityIndexes = [
          ...indexes.filter(
            (i) => this.viewItems[i].priority === LayoutPriority.Low
          ),
          index
        ];
        const highPriorityIndexes = indexes.filter(
          (i) => this.viewItems[i].priority === LayoutPriority.High
        );
  
        const item = this.viewItems[index];
        size = Math.round(size);
        size = clamp(
          size,
          item.minimumSize,
          Math.min(item.maximumSize, this.size)
        );
  
        item.size = size;
        this._relayout(lowPriorityIndexes, highPriorityIndexes);
        this.state = State.Idle;
      },
  
      /**
       * Returns whether all other are at their minimum size.
       */
      isViewSizeMaximized(index) {
        if (index < 0 || index >= this.viewItems.length) {
          return false;
        }
  
        for (const item of this.viewItems) {
          if (item !== this.viewItems[index] && item.size > item.minimumSize) {
            return false;
          }
        }
  
        return true;
      },
  
      _relayout(lowPriorityIndexes, highPriorityIndexes) {
        const contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);
  
        this._resize(
          this.viewItems.length - 1,
          this.size - contentSize,
          undefined,
          lowPriorityIndexes,
          highPriorityIndexes
        );
        this._distributeEmptySpace();
        this._layoutViews();
        this._saveProportions();
      },
  
      _resize(
        index,
        delta,
        sizes = this.viewItems.map((i) => i.size),
        lowPriorityIndexes,
        highPriorityIndexes,
        overloadMinDelta = Number.NEGATIVE_INFINITY,
        overloadMaxDelta = Number.POSITIVE_INFINITY,
        snapBefore,
        snapAfter
      ) {
        if (index < 0 || index >= this.viewItems.length) {
          return 0;
        }
  
        const upIndexes = range(index, -1);
        const downIndexes = range(index + 1, this.viewItems.length);
  
        if (highPriorityIndexes) {
          for (const index of highPriorityIndexes) {
            pushToStart(upIndexes, index);
            pushToStart(downIndexes, index);
          }
        }
  
        if (lowPriorityIndexes) {
          for (const index of lowPriorityIndexes) {
            pushToEnd(upIndexes, index);
            pushToEnd(downIndexes, index);
          }
        }
  
        const upItems = upIndexes.map((i) => this.viewItems[i]);
        const upSizes = upIndexes.map((i) => sizes[i]);
  
        const downItems = downIndexes.map((i) => this.viewItems[i]);
        const downSizes = downIndexes.map((i) => sizes[i]);
  
        const minDeltaUp = upIndexes.reduce(
          (r, i) => r + (this.viewItems[i].minimumSize - sizes[i]),
          0
        );
        const maxDeltaUp = upIndexes.reduce(
          (r, i) => r + (this.viewItems[i].maximumSize - sizes[i]),
          0
        );
        const maxDeltaDown =
          downIndexes.length === 0
            ? Number.POSITIVE_INFINITY
            : downIndexes.reduce(
                (r, i) => r + (sizes[i] - this.viewItems[i].minimumSize),
                0
              );
        const minDeltaDown =
          downIndexes.length === 0
            ? Number.NEGATIVE_INFINITY
            : downIndexes.reduce(
                (r, i) => r + (sizes[i] - this.viewItems[i].maximumSize),
                0
              );
        const minDelta = Math.max(minDeltaUp, minDeltaDown, overloadMinDelta);
        const maxDelta = Math.min(maxDeltaDown, maxDeltaUp, overloadMaxDelta);
  
        let snapped = false;
  
        if (snapBefore) {
          const snapView = this.viewItems[snapBefore.index];
          const visible = delta >= snapBefore.limitDelta;
          snapped = visible !== snapView.visible;
          snapView.setVisible(visible, snapBefore.size);
        }
  
        if (!snapped && snapAfter) {
          const snapView = this.viewItems[snapAfter.index];
          const visible = delta < snapAfter.limitDelta;
          snapped = visible !== snapView.visible;
          snapView.setVisible(visible, snapAfter.size);
        }
  
        if (snapped) {
          return this._resize(
            index,
            delta,
            sizes,
            lowPriorityIndexes,
            highPriorityIndexes,
            overloadMinDelta,
            overloadMaxDelta
          );
        }
  
        delta = clamp(delta, minDelta, maxDelta);
  
        for (let i = 0, deltaUp = delta; i < upItems.length; i++) {
          const item = upItems[i];
          const size = clamp(
            upSizes[i] + deltaUp,
            item.minimumSize,
            item.maximumSize
          );
          const viewDelta = size - upSizes[i];
  
          deltaUp -= viewDelta;
          item.size = size;
        }
  
        for (let i = 0, deltaDown = delta; i < downItems.length; i++) {
          const item = downItems[i];
          const size = clamp(
            downSizes[i] - deltaDown,
            item.minimumSize,
            item.maximumSize
          );
          const viewDelta = size - downSizes[i];
  
          deltaDown += viewDelta;
          item.size = size;
        }
  
        return delta;
      },
  
      /**
       * Distribute the entire size among all
       */
      _distributeEmptySpace(lowPriorityIndex) {
        const contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);
        let emptyDelta = this.size - contentSize;
  
        const indexes = range(this.viewItems.length - 1, -1);
        const lowPriorityIndexes = indexes.filter(
          (i) => this.viewItems[i].priority === LayoutPriority.Low
        );
        const highPriorityIndexes = indexes.filter(
          (i) => this.viewItems[i].priority === LayoutPriority.High
        );
  
        for (const index of highPriorityIndexes) {
          pushToStart(indexes, index);
        }
  
        for (const index of lowPriorityIndexes) {
          pushToEnd(indexes, index);
        }
  
        if (typeof lowPriorityIndex === "number") {
          pushToEnd(indexes, lowPriorityIndex);
        }
  
        for (let i = 0; emptyDelta !== 0 && i < indexes.length; i++) {
          const item = this.viewItems[indexes[i]];
          const size = clamp(
            item.size + emptyDelta,
            item.minimumSize,
            item.maximumSize
          );
          const viewDelta = size - item.size;
  
          emptyDelta -= viewDelta;
          item.size = size;
        }
      },
      /**
       * Returns the size of a {@link IView view}.
       */
      getViewSize(index) {
        if (index < 0 || index >= this.viewItems.length) {
          return -1;
        }
  
        return this.viewItems[index].size;
      },
  
      _layoutViews() {
        requestAnimationFrame(() => {
          // Save new content size
          this.contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);
  
          // Layout views
          let offset = 0;
  
          for (const viewItem of this.viewItems) {
            viewItem.layout(offset);
            offset += viewItem.size;
          }
  
          // Layout sashes
          this.sashItems.forEach((item) => item.sash?.layout());
          this._updateSashEnablement();
          this._updateScrollableElement();
        });
      },
  
      _updateScrollableElement() {
        // if (this.orientation === Orientation.VERTICAL) {
        //   this.scrollableElement.setScrollDimensions({
        //     height: this.size,
        //     scrollHeight: this.contentSize,
        //   });
        // } else {
        //   this.scrollableElement.setScrollDimensions({
        //     width: this.size,
        //     scrollWidth: this.contentSize,
        //   });
        // }
      },
  
      _updateSashEnablement() {
        // debugger;
        let previous = false;
        const collapsesDown = this.viewItems.map(
          (i) => (previous = i.size - i.minimumSize > 0 || previous)
        );
  
        previous = false;
        const expandsDown = this.viewItems.map(
          (i) => (previous = i.maximumSize - i.size > 0 || previous)
        );
  
        const reverseViews = [...this.viewItems].reverse();
        previous = false;
        const collapsesUp = reverseViews
          .map((i) => (previous = i.size - i.minimumSize > 0 || previous))
          .reverse();
  
        previous = false;
        const expandsUp = reverseViews
          .map((i) => (previous = i.maximumSize - i.size > 0 || previous))
          .reverse();
  
        let position = 0;
        for (let index = 0; index < this.sashItems.length; index++) {
          const { sash } = this.sashItems[index];
          const viewItem = this.viewItems[index];
          position += viewItem.size;
  
          const min = !(collapsesDown[index] && expandsUp[index + 1]);
          const max = !(expandsDown[index] && collapsesUp[index + 1]);
  
          if (min && max) {
            const upIndexes = range(index, -1);
            const downIndexes = range(index + 1, this.viewItems.length);
            const snapBeforeIndex = this._findFirstSnapIndex(upIndexes);
            const snapAfterIndex = this._findFirstSnapIndex(downIndexes);
  
            const snappedBefore =
              typeof snapBeforeIndex === "number" &&
              !this.viewItems[snapBeforeIndex].visible;
            const snappedAfter =
              typeof snapAfterIndex === "number" &&
              !this.viewItems[snapAfterIndex].visible;
  
            if (
              snappedBefore &&
              collapsesUp[index] &&
              (position > 0 || this.startSnappingEnabled)
            ) {
              sash.state = SashState.AtMinimum;
            } else if (
              snappedAfter &&
              collapsesDown[index] &&
              (position < this.contentSize || this.endSnappingEnabled)
            ) {
              sash.state = SashState.AtMaximum;
            } else {
              sash.state = SashState.Disabled;
            }
          } else if (min && !max) {
            sash.state = SashState.AtMinimum;
          } else if (!min && max) {
            sash.state = SashState.AtMaximum;
          } else {
            sash.state = SashState.Enabled;
          }
        }
      },
  
      _getSashPosition(sash) {
        let position = 0;
  
        for (let i = 0; i < this.sashItems.length; i++) {
          position += this.viewItems[i].size;
  
          if (this.sashItems[i].sash === sash) {
            return position;
          }
        }
  
        return 0;
      },
  
      _findFirstSnapIndex(indexes) {
        // visible views first
        for (const index of indexes) {
          const viewItem = this.viewItems[index];
  
          if (!viewItem.visible) {
            continue;
          }
  
          if (viewItem.snap) {
            return index;
          }
        }
  
        // then, hidden views
        for (const index of indexes) {
          const viewItem = this.viewItems[index];
  
          if (
            viewItem.visible &&
            viewItem.maximumSize - viewItem.minimumSize > 0
          ) {
            return undefined;
          }
  
          if (!viewItem.visible && viewItem.snap) {
            return index;
          }
        }
  
        return undefined;
      },
  
      _getSashItems() {
        if (this.viewItems.length <= 1) {
          return [];
        } else {
          const sashItems = [];
          for (let i = 0; i < this.viewItems.length - 1; i++) {
            sashItems.push({
              sash: this.$refs?.sashItems?.[i] || null,
              id: this.viewItems[i]._uid
            });
          }
          return sashItems;
        }
      }
    }
  };
  </script>
  
  <style lang="less" scoped>
  .split-view {
    position: relative;
    width: 100%;
    height: 100%;
    .sash-container {
      position: absolute;
      width: 100%;
      height: 100%;
      pointer-events: none;
      .sash-item {
        pointer-events: initial;
      }
    }
    .scrollable-element {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 100%;
      .split-view-container {
        width: 100%;
        height: 100%;
        white-space: nowrap;
        position: relative;
        .split-view-item {
          white-space: initial;
          position: absolute;
          &:not(.visible) {
            display: none;
          }
        }
      }
    }
    &.separator-border .split-view-item:not(:first-child)::before {
      content: " ";
      position: absolute;
      top: 0;
      left: 0;
      z-index: 20;
      pointer-events: none;
      background-color: var(--separator-border);
    }
  }
  .split-view.vertical {
    .split-view-container .split-view-item {
      width: 100%;
    }
    &.separator-border .split-view-item:not(:first-child)::before {
      height: 1px;
      width: 100%;
    }
  }
  .split-view.horizontal {
    .split-view-container .split-view-item {
      height: 100%;
    }
    &.separator-border .split-view-item:not(:first-child)::before {
      height: 100%;
      width: 1px;
    }
  }
  </style>
  