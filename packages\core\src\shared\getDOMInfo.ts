export const getDOMInfo = (el: HTMLElement) => {
    if (!el) {
        return {} as any;
    }

    const {
        x,
        y,
        top,
        left,
        bottom,
        right,
        width,
        height,
    } = el.getBoundingClientRect() as DOMRect;

    const style = window.getComputedStyle(el);

    const margin = {
        left: parseInt(style.marginLeft),
        right: parseInt(style.marginRight),
        bottom: parseInt(style.marginBottom),
        top: parseInt(style.marginTop),
    };

    const padding = {
        left: parseInt(style.paddingLeft),
        right: parseInt(style.paddingRight),
        bottom: parseInt(style.paddingBottom),
        top: parseInt(style.paddingTop),
    };

    const styleInFlow = (parent: HTMLElement) => {
        const parentStyle: any = getComputedStyle(parent);

        // todo 如果放开，意味着组件支持滚动条就会导致横向占位
        // if (style.overflow && style.overflow !== 'visible') {
        //     return;
        // }

        if (parentStyle.float !== 'none') {
            return;
        }

        if (parentStyle.display === 'grid') {
            return;
        }

        if (
            parentStyle.display === 'flex' &&
            parentStyle['flex-direction'] !== 'column'
        ) {
            return;
        }

        switch (style.position) {
            case 'static':
            case 'relative':
                break;
            default:
                return;
        }

        switch (el.tagName) {
            case 'TR':
            case 'TBODY':
            case 'THEAD':
            case 'TFOOT':
                return true;
        }

        switch (style.display) {
            case 'block':
            case 'list-item':
            case 'table':
            case 'flex':
            case 'grid':
                return true;
        }

        return;
    };

    return {
        x,
        y,
        top,
        left,
        bottom,
        right,
        width,
        height,
        outerWidth: Math.round(width + margin.left + margin.right),
        outerHeight: Math.round(height + margin.top + margin.bottom),
        margin,
        padding,
        // inFlow: el.parentElement && !!styleInFlow(el.parentElement),
        inFlow: true,
        isPlaceHolder: (el?.querySelector('.container-placeholder') as HTMLElement)?.dataset.placeholder
    };
};

let scrollbarWidth;

// 获取滚动条的宽度
export function getScrollbarWidth() {
    if(scrollbarWidth !== undefined){
        return scrollbarWidth;
    }
    // 创建一个用于检测滚动条宽度的元素
    var outerDiv = document.createElement("div");
    outerDiv.style.visibility = "hidden";
    outerDiv.style.overflow = "scroll";
    document.body.appendChild(outerDiv);

    // 在检测元素内部创建一个较长的子元素
    var innerDiv = document.createElement("div");
    outerDiv.appendChild(innerDiv);
    innerDiv.style.width = "100%";
    innerDiv.style.height = "200px";

    // 计算滚动条宽度
    scrollbarWidth = outerDiv.offsetWidth - innerDiv.offsetWidth;

    // 清除创建的元素
    document.body.removeChild(outerDiv);

    return scrollbarWidth;
}

export function hasVerticalScrollbar(elem){
    if(elem.scrollHeight > elem.clientHeight) {
        return true;
    };

    const style = window.getComputedStyle(elem);
    const overflowValues = (style.overflow || '').split(' ');
    if (overflowValues[overflowValues.length-1] === 'scroll') {
        return true;
    }
}
