export const propertyConfig = {
    margin: {
        positions: ['Top', 'Right', 'Bottom', 'Left'],
        icons: ['fx-icon-tuliweizhi2', 'fx-icon-tuliweizhi3', 'fx-icon-tuliweizhi1', 'fx-icon-tuliweizhi4'],
        label: $t('beecraft.setters.margin', {}, '外边距值相等')
    },
    padding: {
        positions: ['Top', 'Right', 'Bottom', 'Left'],
        icons: ['fx-icon-neirongweizhi2', 'fx-icon-neirongweizhi3', 'fx-icon-neirongweizhi1', 'fx-icon-neirongweizhi4'],
        label: $t('beecraft.setters.padding', {}, '内边距值相等')
    },
    borderWidth: {
        positions: ['Top', 'Right', 'Bottom', 'Left'],
        icons: ['fx-icon-biankuangweizhi2', 'fx-icon-biankuangweizhi3', 'fx-icon-biankuangweizhi1', 'fx-icon-biankuangweizhi4'],
        label: $t('beecraft.setters.borderWidth', {}, '边框大小相等')
    },
    borderRadius: {
        positions: ['TopLeft', 'TopRight', 'BottomRight', 'BottomLeft'],
        icons: ['fx-icon-biankuangweizhi2', 'fx-icon-biankuangweizhi3', 'fx-icon-biankuangweizhi1', 'fx-icon-biankuangweizhi4'],
        label: $t('beecraft.setters.borderRadius', {}, '边框圆角相等')
    }
};

/**
 * 统一处理CSS样式属性
 * @param {Object} style - 原始样式对象
 * @param {String} property - 属性名称 ('margin'|'padding'|'borderWidth'|'borderRadius')
 * @returns {Object} 处理后的样式对象
 */
export function normalizeStyleProperty(style, property, isSimplify = true) {
    const config = propertyConfig[property];
    if (!config) {
        throw new Error(`Unsupported property: ${property}`);
    }

    const result = {};
    const styleCopy = { ...style }; // 创建样式对象的副本

    // 处理简写形式
    const shorthandValue = styleCopy[property] ?? styleCopy[kebabCase(property)];
    if (shorthandValue) {
        const values = shorthandValue.split(' ').map(v => v.trim());

        switch (values.length) {
            case 1: // 所有方向相同
                config.positions.forEach(pos => {
                    result[getCssPropertyWithDirection(property, pos)] = values[0];
                });
                break;

            case 2: // 上下 左右
                config.positions.forEach((pos, index) => {
                    result[getCssPropertyWithDirection(property, pos)] = values[index % 2 === 0 ? 0 : 1];
                });
                break;

            case 3: // 上 左右 下
                config.positions.forEach((pos, index) => {
                    const cssProperty = getCssPropertyWithDirection(property, pos);
                    if (index === 0) result[cssProperty] = values[0]; // top
                    else if (index === 2) result[cssProperty] = values[2]; // bottom
                    else result[cssProperty] = values[1]; // left/right
                });
                break;

            case 4: // 上 右 下 左
                config.positions.forEach((pos, index) => {
                    result[getCssPropertyWithDirection(property, pos)] = values[index];
                });
                break;
        }

        // 清除原有的简写属性
        if (styleCopy[property]) {
            result[property] = undefined;
        }else if (styleCopy[kebabCase(property)]) {
            result[kebabCase(property)] = undefined;
        }
    }

    // 处理分开写的属性
    config.positions.forEach(pos => {
        const camelKey = getCssPropertyWithDirection(property, pos);
        const cssKey = kebabCase(camelKey);

        if (styleCopy[cssKey] !== undefined) {
            result[camelKey] = styleCopy[cssKey];
            result[cssKey] = undefined;
        }
        if (styleCopy[camelKey] !== undefined) {
            result[camelKey] = styleCopy[camelKey];
        }
    });

    // 若值相等则合并成简写的属性
    return isSimplify ? simplifyResult(result, property) ?? result : result;
}

// 若值相等则合并成简写的属性
function simplifyResult(style, property) {
    let count = 0;
    let result = {};
    
    Object.keys(style).forEach(key => {
        if(key === property) {
            return;
        }
        if(style[key] !== undefined) {
            if(!result[property]){
                result[property] = style[key];
                count++;
            }else {
                if(result[property] === style[key]) {
                    count++;
                }
            }
            result[key] = undefined;
        }
        // if(key !== property) {
        //     result[key] = undefined;
        // }
    });

    if (count === 4) {
        return result;
    }
}

/**
 * 获取CSS属性
 * @param {String} property - 属性名称 ('margin'|'padding'|'borderWidth'|'borderRadius')
 * @param {String} direction - 方向 ('Top'|'Right'|'Bottom'|'Left')
 * @returns {String} 对应的CSS属性
 */
export function getCssPropertyWithDirection(property, direction) {
    if (property === 'borderWidth' || property === 'borderRadius') {
        return property.replace(/([A-Z])/g, `${direction}$1`);
    } else {
        return `${property}${direction}`;
    }
}

// 辅助函数：转换为kebab-case
export function kebabCase(str) {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

// 辅助函数：相反转换为kebab-case
export function unKebabCase(str) {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

// 填充其余值
export function fillAndUnitValue(style, property) {
    const config = propertyConfig[property];
    if (!config) {
        throw new Error(`Unsupported property: ${property}`);
    }

    config.positions.forEach(pos => {
        const camelKey = getCssPropertyWithDirection(property, pos);
        
        if(style[camelKey] === undefined) {
            style[camelKey] = '';
        }
    });
    
    return style;
}
