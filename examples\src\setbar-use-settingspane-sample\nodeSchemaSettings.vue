<template>
    <div class="test" v-if="selectedNode">
        <h1>schema方式</h1>
        <div>
            <SettingsPane :onChange="handleChange" :related="selectedNode.related?.settings" :selectedId="selectedNode.id" type="node"></SettingsPane>
        </div>
    </div>
</template>
<script lang="js">
    import { SettingsPane } from '@beecraft/workbench';
    
    export default {
        components: {
            SettingsPane
        },

        computed: {
            // 当前处于选中的节点
            selectedNode() {
                const { query } = this.useInternalEditor();
                const selectedNodeId = query.getEvent('selected').first();

                return query.node(selectedNodeId).get();
            },
        },

        inject: ['useInternalEditor'],

        methods: {
            handleChange(id, value) {
                const { actions } = this.useInternalEditor();
                
                actions.setCustom(this.selectedNode.id, (data) => {
                    data[id] = value;
                });
            }
        }
    }
</script>
<style lang="less" scoped>
    h1 {
        line-height: 40px;
        font-size: 14px;
        color: #8492a6;
    }
    .test {
        padding: 0 10px;
    }
</style>