<template>
    <div class="bc-setter-align">
        <fx-radio-group v-model="localValue" size="micro" isStroke @change="handleChange">
            <fx-radio-button
                v-for="option in alignmentOptions"
                :key="option.label"
                :label="option.label"
            >
                <fx-tooltip :content="option.desc" effect="light">
                    <span class="bc-icon-item" :class="option.icon"></span>
                </fx-tooltip>
            </fx-radio-button>
        </fx-radio-group>
    </div>
</template>

<script lang="ts">
const ALIGNMENT_TYPES = {
    VERTICAL: 'vertical',
    HORIZONTAL: 'horizontal',
    TEXT: 'text',
};

const OPTION_KEYS = {
    [ALIGNMENT_TYPES.VERTICAL]: ['alignItems', 'align-items'],
    [ALIGNMENT_TYPES.HORIZONTAL]: ['justifyContent', 'justify-content'],
    [ALIGNMENT_TYPES.TEXT]: ['textAlign', 'text-align'],
};

export default {
    props: {
        type: String,
        value: {
            type: Object,
            default: () => ({}),
        },
    },
    computed: {
        alignmentOptions() {
            return this.optionSets[this.type] || [];
        },
    },
    data() {
        return {
            optionSets: {
                [ALIGNMENT_TYPES.VERTICAL]: [
                    {
                        desc: $t('beecraft.setters.align.top', {}, '顶对齐'),
                        label: 'flex-start',
                        icon: 'fx-icon-dingduiqi',
                    },
                    {
                        desc: $t('beecraft.setters.align.verticalCenter', {}, '垂直居中'),
                        label: 'center',
                        icon: 'fx-icon-chuizhijuzhongduiqi',
                    },
                    {
                        desc: $t('beecraft.setters.align.bottom', {}, '底对齐'),
                        label: 'flex-end',
                        icon: 'fx-icon-diduiqi',
                    },
                ],
                [ALIGNMENT_TYPES.HORIZONTAL]: [
                    {
                        desc: $t('beecraft.setters.align.left', {}, '左对齐'),
                        label: 'flex-start',
                        icon: 'fx-icon-zuoduiqi',
                    },
                    {
                        desc: $t('beecraft.setters.align.horizontalCenter', {}, '水平居中'),
                        label: 'center',
                        icon: 'fx-icon-zhongduiqi',
                    },
                    {
                        desc: $t('beecraft.setters.align.right', {}, '右对齐'),
                        label: 'flex-end',
                        icon: 'fx-icon-youduiqi',
                    },
                ],
                [ALIGNMENT_TYPES.TEXT]: [
                    {
                        desc: $t('文本左对齐', {}, '文本左对齐'),
                        label: 'left',
                        icon: 'fx-icon-wenbenzuoduiqi',
                    },
                    {
                        desc: $t('文本中对齐', {}, '文本中对齐'),
                        label: 'center',
                        icon: 'fx-icon-wenbenzhongduiqi',
                    },
                    {
                        desc: $t('文本右对齐', {}, '文本右对齐'),
                        label: 'right',
                        icon: 'fx-icon-wenbenyouduiqi',
                    },
                ],
            },
            localValue: this.initializeLocalValue(),
        };
    },
    methods: {
        initializeLocalValue() {
            const keys = OPTION_KEYS[this.type] || [];
            for (const key of keys) {
                if (this.value[key]) {
                    return this.value[key];
                }
            }
            return this.defaultLabel();
        },
        defaultLabel() {
            if (this.type === ALIGNMENT_TYPES.VERTICAL) return 'flex-start';
            if (this.type === ALIGNMENT_TYPES.HORIZONTAL) return 'flex-end';
            if (this.type === ALIGNMENT_TYPES.TEXT) return 'left';
        },
        handleChange(value) {
            const prop = OPTION_KEYS[this.type] ? OPTION_KEYS[this.type][0] : '';
            if (prop) this.$emit('change', { [prop]: value });
        },
    },
};
</script>

<style lang="less" scoped>
.bc-setter-align {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    .bc-icon-item {
        font-size: 16px;
    }

    .el-radio-button.is-active {
        .icon-item {
            &::before {
                color: var(--color-primary06, #ff8000);
            }
        }
    }
}
</style>
