<template>
    <div v-if="enabled && indicator && !indicator.error">
        <div class="bc-indicator bc-indicator-block" v-if="getIndicatorStyle.placeholderStyle"
            :style="getIndicatorStyle.placeholderStyle">
            <div class="bc-indicator-line" :style="getIndicatorStyle.indicatorstyle"></div>
            <div class="bc-indicator-placeholder">{{ getIndicatorStyle.placeholder }}</div>
        </div>
        <div v-else class="bc-indicator bc-indicator-line" :style="getIndicatorStyle.indicatorstyle"></div>
    </div>
</template>
<script>
import movePlaceholder from './movePlaceholder';
import { getDOMInfo } from '../shared/getDOMInfo';

export default {
    name: 'RenderEditorIndicator',

    inject: ['useEventHandler', 'useInternalEditor'],

    computed: {
        indicator() {
            const { indicator } = this.useInternalEditor((state) => {
                return {
                    indicator: state.indicator
                }
            });
            return indicator;
        },
        indicatorOptions() {
            const { indicatorOptions } = this.useInternalEditor((state) => {
                return {
                    indicatorOptions: state.options.indicator
                }
            });

            return indicatorOptions;
        },
        getIndicatorStyle() {
            const { indicator, indicatorOptions } = this;
            const { placement, error } = this.indicator;
            let indicatorstyle, // 指针位置
                placeholderStyle; // 所放置容器提示遮罩位置

            // 特殊接口-占位DOM
            const phElem = placement.parent.dom?.querySelector(`[data-placeholder="${placement.parent.id}"]`);

            if (phElem) {
                const domInfo = getDOMInfo(phElem);

                indicatorstyle = {
                    top: `${domInfo.top}px`,
                    left: `${domInfo.left}px`,
                    width: `${domInfo.width}px`,
                    height: `${domInfo.height}px`,
                    backgroundColor: error ? indicatorOptions.error : indicatorOptions.success,
                    opacity: 0.5
                }
            } else {
                const parentDomInfo = getDOMInfo(placement.parent.dom);

                indicatorstyle = {
                    ...movePlaceholder(
                        placement,
                        parentDomInfo,
                        placement.currentNode && getDOMInfo(placement.currentNode.dom || placeholderElem),
                    ),
                    backgroundColor: error ? indicatorOptions.error : indicatorOptions.success
                };

                if (placement.parent.$$data.isAlwaysShowPlaceholder) {
                    placeholderStyle = {
                        width: `${parentDomInfo.width}px`,
                        height: `${parentDomInfo.height}px`,
                        top: `${parentDomInfo.top}px`,
                        left: `${parentDomInfo.left}px`,
                    }
                    indicatorstyle.top = (indicatorstyle.top.slice(0, -2) - parentDomInfo.top) + 'px';
                    indicatorstyle.left = (indicatorstyle.left.slice(0, -2) - parentDomInfo.left) + 'px';
                }
            }

            return {
                indicatorstyle,
                placeholderStyle,
                placeholder: placement.parent.$$data.placeholder || placement.parent.displayName
            };
        },
        enabled() {
            const handler = this.useEventHandler();
            const { enabled } = this.useInternalEditor((state) => ({ enabled: state.options.enabled }));

            if (enabled) {
                handler.enable();
            } else {
                handler.disable();
            }

            return enabled;
        }
    }
}
</script>
<style lang="less" scoped>
.bc-indicator {
    position: fixed;
    background-color: var(--color-primary06);
    will-change: left, top, height, width;
    z-index: 150;

    &.forbidden {
        background-color: #ff522a;
    }

    .bc-indicator-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-weight: bold;
        position: absolute;
        left: 0;
        margin: 0 auto;
        right: 0;
        width: 90px;
        background-color: var(--color-primary06);
    }

    .bc-indicator-line {
        position: absolute;
    }
}

.bc-indicator-block {
    .bc-indicator-line {
        position: absolute;
    }

    background-color: rgba(33, 43, 54, 0.30);
    pointer-events: none;
    border: 1px dashed var(--color-primary06);
    box-sizing: border-box;
}
</style>