import {
    NodeMap,
    Node,
    NodeId,
    NodeSelectorWrapper,
    NodeSelector
} from '../interfaces';
import invariant from 'tiny-invariant';

import { ERROR_INVALID_NODEID } from '@beecraft/shared';


type config = { existOnly: boolean; idOnly: boolean };

export const getNodesFromSelector = (
    nodes: NodeMap,
    selector: NodeSelector,
    config?: Partial<config>
): NodeSelectorWrapper[] => {
    const items = Array.isArray(selector) ? selector : [selector];

    const mergedConfig = {
        existOnly: false,
        idOnly: false,
        ...(config || {}),
    };

    const nodeSelectors = (items as any)
        .filter((item: any) => !!item)
        .map((item: any) => {
            if (typeof item === 'string') {
                return {
                    node: nodes[item],
                    exists: !!nodes[item],
                };
            }

            if (typeof item === 'object' && !mergedConfig.idOnly) {
                const node = item as Node;
                return {
                  node,
                  exists: !!nodes[node.id],
                };
            }

            return {
                node: null,
                exists: false,
            };
        });

    if (mergedConfig.existOnly) {
        invariant(
            nodeSelectors.filter((selector: NodeSelectorWrapper) => !selector.exists).length === 0,
            ERROR_INVALID_NODEID
        );
    }

    return nodeSelectors;
}