export default function () {
    return {
        data: {
            text: 'hello world',
            filters: {
                
            }
        },
        $$data: {
            layer: {
                w: 6,
                h: 10,
            },
            noWrapper: true
        },
        related: {
            attributeSettings: [{
                name: 'SetterField',
                data: {
                    label: '单行文本',
                    display: 'block',
                    setter: {
                        component: 'ColorPickerSetter',
                        ranges: ['data', 'text']
                    }
                }
            }]
        }
    }
}