import { SetterField } from '@beecraft/setters';

/**
 * @module engine/plugins/PaasSetterLibs
 * @desc 初始化设置器
 */

export default class PaasSetterLibs {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }   

    initBefore(service, { config, options }) {
        config.set('resolver', {
            SetterField
        });
    }
}