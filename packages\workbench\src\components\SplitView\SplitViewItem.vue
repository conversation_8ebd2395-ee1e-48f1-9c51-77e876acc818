<template>
    <div :class="{ 'split-view-item': true, visible: visible }">
      <slot></slot>
    </div>
  </template>
  
  <script>
  import { clamp, LayoutPriority } from "./utils";
  export default {
    inheritAttrs: false,
    name: "SplitViewItem",
    inject: ["splitView"],
    props: {
      initSize: {
        type: [Number, Object],
        default: () => ({
          cachedVisibleSize: 200
        })
        // default: 0,
      },
      // 最小尺寸
      miniSize: {
        type: Number,
        default: 0
      },
      // 最大尺寸
      maxSize: {
        type: Number,
        default: Number.POSITIVE_INFINITY
      },
      // 具有较高优先级的视图将首先调整大小。
      priority: {
        type: String,
        default: LayoutPriority.Normal,
        validator: function (value) {
          return ["low", "normal", "high"].includes(value);
        }
      },
      // 此属性允许更精细的控制比例布局算法，每个视图。
      proportionalLayout: {
        type: Boolean,
        default: true
      },
      // 视图是否会在用户达到最小尺寸或尝试将其扩展到超过最小大小。
      // 如果为true，则视图将在达到最小尺寸时隐藏。
      snap: {
        type: Boolean,
        default: false
      }
    },
    data() {
      let size, cachedVisibleSize;
      if (typeof this.initSize === "number") {
        size = this.initSize || this.miniSize;
        cachedVisibleSize = undefined;
      } else {
        size = 0;
        cachedVisibleSize = this.initSize.cachedVisibleSize;
      }
      return {
        size, // 当前尺寸
        cachedVisibleSize, // 缓存的可见尺寸
  
        enabled: true // 是否启用
      };
    },
    computed: {
      orientation() {
        return this.$parent.orientation;
      },
      visible() {
        return typeof this.cachedVisibleSize === "undefined";
      },
      // 限制最小尺寸
      minimumSize() {
        return this.visible ? this.miniSize : 0;
      },
      viewMinimumSize() {
        return this.miniSize;
      },
      // 限制最大尺寸
      maximumSize() {
        return this.visible ? this.maxSize : 0;
      },
      viewMaximumSize() {
        return this.maxSize;
      }
    },
    watch: {
      enabled(newValue) {
        this.$el.style.pointerEvents = newValue ? "" : "none";
      }
    },
    mounted() {
      const index = this.splitView.viewItems.findIndex((item) => item === this);
  
      this.splitView.addView(
        this,
        this.initSize,
        this.splitView.viewItems.length,
        true
      );
  
      this.layout();
    },
    beforeDestroy() {
      const index = this.splitView.viewItems.findIndex((item) => item === this);
      index > -1 && this.splitView.removeView(index);
    },
    methods: {
      /**
       *
       * @param {Boolean} visible
       * @param {Number=} size
       */
      setVisible(visible, size) {
        if (visible === this.visible) {
          return;
        }
  
        if (visible) {
          this.size = clamp(
            this.cachedVisibleSize,
            this.viewMinimumSize,
            this.viewMaximumSize
          );
          this.cachedVisibleSize = undefined;
        } else {
          this.cachedVisibleSize = typeof size === "number" ? size : this.size;
          this.size = 0;
        }
      },
      layout(offset, layoutContext) {
        requestAnimationFrame(() => {
          this.layoutContainer(offset);
          // try {
          //   this.$slots.default.forEach((item) => {
          //     item.componentInstance?.layout?.(this.size, offset, layoutContext);
          //   });
          // } catch (error) {
          //     console.log(error);
          // }
        });
      },
      layoutContainer(offset) {
        if (this.orientation === "vertical") {
          this.$el.style.top = `${offset}px`;
          this.$el.style.height = `${this.size}px`;
        } else {
          this.$el.style.left = `${offset}px`;
          this.$el.style.width = `${this.size}px`;
        }
      }
    }
  };
  </script>
  
  <style lang="less" scoped>
  .split-view-item {
    box-sizing: border-box;
  }
  </style>
  