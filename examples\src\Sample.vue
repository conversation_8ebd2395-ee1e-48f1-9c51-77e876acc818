<template>
    <div class="sample-wrapper">
        <div class="sample-wrapper-content">
            <component :is="sampleComponent"></component>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            sampleComponent: ''
        }
    },
    created() {
        this.sampleComponent = () => import(`./${this.$route.params.example}-sample/sample.vue`)
    },
}
</script>
<style lang="less" scoped>
.sample-wrapper {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    overflow-y: auto;
    position: relative;
    display: flex;
}

.sample-wrapper-content {
    background: #fff;
    margin: 20px;
    padding: 20px;
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    box-sizing: border-box;
    border-radius: 8px;
    overflow: hidden;
    overflow: auto;
}
</style>