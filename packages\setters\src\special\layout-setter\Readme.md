## 使用

```
    {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        data: {
            label: $t('边距', {}, '边距'),              ==> 传入自定义label,注意多语
            display: 'block',
            setter: {
                component: 'LayoutSetter',
                ranges: ['$$data', 'style'],
                data: {
                    type: 'margin'                     ==> 可以传入 'margin' || 'padding' || 'border'
                }
            }
        }
    },

```

## 传出的值

```
{
  margin: '33px'  // 复合属性
}

```
