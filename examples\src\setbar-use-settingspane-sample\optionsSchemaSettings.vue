<template>
    <div class="test">
        <h1>schema方式</h1>
        <div>
            <SettingsPane :onChange="handleChange" :related="globalSettings"></SettingsPane>
        </div>
    </div>
</template>
<script lang="js">
    import { SettingsPane } from '@beecraft/workbench';
    
    export default {
        components: {
            SettingsPane
        },

        inject: ['useInternalEditor'],

        created() {
            this.globalSettings = [{
                name: 'Setter<PERSON><PERSON>',
                data: {
                    label: '测试1',
                    display: 'block'
                },
                children: [{
                    name: 'SetterField',
                    data: {
                        label: '测试1',
                        display: 'block'
                    },
                    children: [{
                        id: 'text',
                        name: 'Setter<PERSON>ield',
                        data: {
                            label: '单行文本',
                            display: 'block',
                            setter: {
                                component: 'StringSetter',
                                ranges: ['options', 'text']
                            }
                        }
                    }]
                }]
            }];
        },

        methods: {
            handleChange(id, value) {
                console.log(arguments);
            }
        }
    }
</script>
<style lang="less" scoped>
    h1 {
        line-height: 40px;
        font-size: 14px;
        color: #8492a6;
    }
    .test {
        padding: 0 10px;
    }
</style>