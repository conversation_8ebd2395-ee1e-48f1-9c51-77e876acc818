<template>
    <div class="global-setting-wrapper">
        <fx-radio-group class="global-setting-group" v-model="activeTab" size="mini" @change="handleActiveTabChange">
            <fx-radio-button v-for="tab in _globalThemeSchema" :key="tab.name" :label="tab.name">
                {{ tab.label }}
            </fx-radio-button>
        </fx-radio-group>
        <div :class="['global-setting', activeTab]">
            <!-- <div v-show="activeTab === setting.name"> -->
                <SettingsPane
                    v-show="activeTab === setting.name"
                    v-for="setting in _globalThemeSchema"
                    :related="[{name: 'SetterField',data: {label: setting.name,display: 'block'}}]"
                    :key="setting.name"
                ></SettingsPane>
            <!-- </div> -->
            <div v-show="activeTab === 'style'" class="reset">
                <span class="text" @click="handleResetDefault">{{$t('恢复默认')}}</span>
            </div>
        </div>
    </div>
</template>

<script>
import { SettingsPane } from '@beecraft/workbench';
const GRID_NAME = 'GridLayout';
const GlobalThemeSchema = [{name: 'aaa', label: 'aaa'}, {name: 'bbb', label: 'bbb'}];
export default {
    components: {
        SettingsPane
    },
    inject: ['useInternalEditor'],
    data() {
        return {
            text: 0,
            activeTab: 'style',
        };
    },
    computed: {
    },
    methods: {
        handleResetDefault(){

        },
        handleSetterChange(id, value) {
            console.log(id,value);
            this[`handle${id}`](value);
            // const { actions } = this.useInternalEditor();
            // actions.setOptions(options => {
            //     options[id] = value;
            // });
        },
        handleGlobalBackgroundColor(value){
            const { actions, getAttribute } = this.useInternalEditor((state)=>({
                getAttribute: state.options.getAttribute
            }));
            actions.setOptions(options=>{
                options.globalThemeConfig.backgroundConfig.background.color = value;
            })
            const {updateBackgroundColor} = getAttribute(state=>({updateBackgroundColor: state.updateBackgroundColor.bind(state)}));
            updateBackgroundColor(value);
        },
        handleGlobalName(value){
            const { actions, getAttribute } = this.useInternalEditor((state)=>({
                getAttribute: state.options.getAttribute
            }));
            actions.setOptions(options=>{
                options.globalAttributeConfig.name = value;
            })
            getAttribute(state=>state.$data).dashName = value;
        },
        handleCompMargin(value){
            const { query,actions } = this.useInternalEditor();
            actions.setOptions(options=>{
                options.globalThemeConfig.margin = value;
            })
            const nodes = query.getNodes();
            for(let id in nodes){
                const node = nodes[id];
                if(node.name === GRID_NAME){  //预置的栅格容器的Name
                    actions.setCustom(id,(data)=>{
                        data.margin = new Array(2).fill(value);
                    })
                }
            }
        },
        handleActiveTabChange() {
            console.log(this.activeTab);
        },
        initTabsSchema() {
            this._globalThemeSchema = GlobalThemeSchema;
        },
    },
    created() {
        this.initTabsSchema();
    },
};
</script>

<style lang="less">
.global-setting-wrapper {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .global-setting-group {
        margin-top: 12px;
    }
    .global-setting {
        width: 100%;
        flex: 1;
        overflow: auto;
        .bc-setter-field-body {
            width: 100%;
        }
        .reset{
            position: relative;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 12px;
            color: var(--color-info06, #0c6cff);
            font-size: 12px;
            line-height: 18px;
            padding: 0 12px;
            text-align: end;
            cursor: pointer;
        }
    }
}
</style>