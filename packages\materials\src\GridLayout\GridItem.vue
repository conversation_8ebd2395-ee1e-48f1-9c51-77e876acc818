<template>
    <vue-draggable-resizable ref="instance" class="grid-item"
        :class="{ cssTransforms: useCSSTransforms, dropping: isDroppingItem }" @dragstop="onDragStop"
        :draggable="draggable" :resizable="resizable" :onDragStart="onDragStart" :onDrag="onDrag"
        :onResizeStart="onResizeStart" :onResize="onResize" @resizestop="onResizeStop" @deactivated="onDeactivated"
        :parent="false" class-name-handle="custom-handle" v-bind="style" :z="3">
        <!-- <div class="grid-item-inner"> -->
        <slot></slot>
        <!-- </div> -->
        <div class="drag-box" slot="tl"></div>
        <!-- <div slot="tm"></div> -->
        <div class="drag-box" slot="tr"></div>
        <!-- <div slot="mr"></div> -->
        <div class="drag-box" slot="br"></div>
        <!-- <div slot="bm"></div> -->
        <div class="drag-box" slot="bl"></div>
        <!-- <div slot="ml"></div> -->
    </vue-draggable-resizable>
</template>
<script>
import VueDraggableResizable from 'vue-draggable-resizable';
import {
    perc,
    setTopLeft,
    setTransform,
    resizeItemInDirection,
} from './utils';
import {
    calcXY,
    calcWH,
    clamp,
    calcGridItemPosition
} from './calculateUtils';

export default {
    props: {
        // These are all in grid units
        x: {
            type: Number
        },
        y: {
            type: Number
        },
        w: {
            type: Number
        },
        h: {
            type: Number
        },
        // All optional
        minW: {
            type: Number,
            default: 1
        },
        minH: {
            type: Number,
            default: 1
        },
        maxW: {
            type: Number,
            default: Infinity
        },
        maxH: {
            type: Number,
            default: Infinity
        },
        i: {
            type: String
        },
        containerWidth: {
            type: Number
        },
        isDroppingItem: {
            type: Boolean,
            default: false
        },
        draggable: {
            type: Boolean,
            default: true
        },
        resizable: {
            type: Boolean,
            default: true
        }
    },
    inject: ['useInternalEditor'],
    components: {
        VueDraggableResizable
    },
    data: function () {
        return {
            hydrationTimestamp: +new Date(),
            droppingPosition: null
        }
    },
    computed: {
        style() {
            const pos = calcGridItemPosition(
                this.getPositionParams(),
                this.x,
                this.y,
                this.w,
                this.h,
                this
            );
            const style = this.createStyle(pos);
            const instance = this.$refs.instance;

            if (!this.resizing) {
                if (instance) {
                    instance.moveHorizontally(style.x);
                    instance.moveVertically(style.y);
                    instance.changeWidth(style.w);
                    instance.changeHeight(style.h);
                }
            } else if (!this.dragging) {
                if (instance) {
                    instance.moveHorizontally(style.x);
                    instance.moveVertically(style.y);
                }
            }

            return {
                ...style,
                hydrationTimestamp: this.hydrationTimestamp
            }
        },
        useCSSTransforms() {
            return this.layout.useCSSTransforms;
        }
    },
    inject: ["layout"],
    created() {
        this.dragging = null;
        this.resizing = null;
    },
    // mounted() {
    //     this.$el.addEventListener('transitionend', function (event) {
    //         document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_DRAG_STOP', {
    //             bubbles: true,
    //             cancelable: true,
    //             detail: {
    //                 id: this._uid
    //             }
    //         }));
    //     }, false);
    // },
    methods: {
        moveDroppingItem(droppingPosition) {
            if (!droppingPosition) {
                return;
            };
            const prevDroppingPosition = this.droppingPosition || {
                left: 0,
                top: 0
            };
            const { dragging } = this;
            const shouldDrag =
                (dragging && droppingPosition.left !== prevDroppingPosition.left) ||
                droppingPosition.top !== prevDroppingPosition.top;

            this.droppingPosition = droppingPosition;

            if (!dragging) {
                this.onDragStart(droppingPosition.e, {
                    // node,
                    // deltaX: droppingPosition.left,
                    // deltaY: droppingPosition.top
                });
            } else if (shouldDrag) {
                // const deltaX = droppingPosition.left - dragging.left;
                // const deltaY = droppingPosition.top - dragging.top;

                // this.onDrag(droppingPosition.e, {
                //     node,
                //     deltaX,
                //     deltaY
                // });
                // this.onDrag(deltaY, deltaX);
                this.onDrag(droppingPosition.left, droppingPosition.top);
            }
        },

        // onResize(x, y, width, height) {
        //     this.x = x
        //     this.y = y
        //     this.width = width
        //     this.height = height
        // },

        onDragStart(e) {
            console.log('GridItem,onDragStart');
            const { currentTarget: node } = e;
            const { onDragStart, transformScale = 1 } = this.layout;
            if (!onDragStart) return;

            const newPosition = { top: 0, left: 0 };

            // TODO: this wont work on nested parents
            const { offsetParent } = node;
            if (!offsetParent) return;
            const parentRect = offsetParent.getBoundingClientRect();
            const clientRect = node.getBoundingClientRect();

            const cLeft = clientRect.left / transformScale;
            const pLeft = parentRect.left / transformScale;
            const cTop = clientRect.top / transformScale;
            const pTop = parentRect.top / transformScale;

            newPosition.left = cLeft - pLeft + offsetParent.scrollLeft;
            newPosition.top = cTop - pTop + offsetParent.scrollTop;

            // TODO: 源码使用setState，可能存在响应式
            this.dragging = newPosition;

            const { x, y } = calcXY(
                this.getPositionParams(),
                newPosition.top,
                newPosition.left,
                this.w,
                this.h
            )

            return onDragStart?.call(this, this.i, x, y, {
                // e,
                // node,
                newPosition
            })
        },

        onDrag(left, top) {
            const { onDrag } = this.layout;
            if (!onDrag) return;

            if (!this.dragging) {
                throw new Error("onDrag called before onDragStart.");
            }

            const { w, h, i } = this;
            const newPosition = { top, left };
            this.dragging = newPosition;

            const positionParams = this.getPositionParams();
            const { containerPadding } = positionParams;

            const { x, y } = calcXY(
                positionParams,
                top - containerPadding[1],
                left - containerPadding[0],
                w,
                h
            );

            return onDrag.call(this, i, x, y, {
                // e,
                // node,
                newPosition
            });
        },

        onDragStop() {
            const { onDragStop } = this.layout;
            if (!onDragStop) return;

            if (!this.dragging) {
                throw new Error("onDragEnd called before onDragStart.");
            }

            const positionParams = this.getPositionParams();
            const { containerPadding } = positionParams;
            const { w, h, i } = this;
            const { left, top } = this.dragging;
            const newPosition = { top, left };
            console.log('onDragStop');
            this.dragging = null;

            const { x, y } = calcXY(
                positionParams,
                top - containerPadding[1],
                left - containerPadding[0],
                w,
                h
            );

            this.hydrationTimestamp = +new Date();
            this.droppingPosition = null;

            return onDragStop.call(this, i, x, y, {
                // e,
                // node,
                newPosition
            });
        },

        onDeactivated() {
            this.dragging = null;
        },

        onResizeStart(handle, e) {
            const pos = this.style;

            this.onResizeHandler(e, {
                handle,
                node: {},
                size: {
                    width: pos.width,
                    height: pos.height
                }
            }, pos, "onResizeStart");
        },

        onResize(handle, left, top, width, height) {
            this.onResizeHandler(null, {
                handle,
                node: {},
                size: {
                    width,
                    height
                }
            }, {
                left,
                top,
                width,
                height
            }, "onResize")
        },

        onResizeStop(left, top, width, height) {
            this.onResizeHandler(null, {
                handle: this.handle,
                node: {},
                size: {
                    width,
                    height
                }
            }, {
                left,
                top,
                width,
                height
            }, "onResizeStop");
        },

        onResizeHandler(e, { node, size, handle }, position, handlerName) {
            const handler = this.layout[handlerName];

            if (!handler) return;

            const { x, y, i, maxH, minH, minW, maxW } = this;
            const containerWidth = this.layout.containerWidth;

            let updatedSize = size;
            if (node) {
                updatedSize = resizeItemInDirection(
                    handle,
                    position,
                    size,
                    containerWidth
                );
                this.resizing = handlerName === "onResizeStop" ? null : updatedSize;
                this.handle = handlerName === "onResizeStop" ? null : handle;
                this.dragging = null;
            }

            let { w, h } = calcWH(
                this.getPositionParams(),
                updatedSize.width,
                updatedSize.height,
                x,
                y,
                handle
            );

            w = clamp(w, Math.max(minW, 1), maxW);
            h = clamp(h, minH, maxH);

            if (handlerName === 'onResizeStop') {
                this.hydrationTimestamp = +new Date();
            }

            handler.call(this, i, w, h, {
                // e,
                // node,
                size: updatedSize,
                handle
            });
        },

        getPositionParams() {
            const { layout } = this;
            return {
                cols: layout.colNum,
                containerWidth: layout.containerWidth,
                containerPadding: layout.containerPadding || layout.margin,
                margin: layout.margin,
                maxRows: layout.maxRows,
                rowHeight: layout.cRowHeight
            }
        },

        createStyle(pos) {
            // const { useCSSTransforms, mounted, containerWidth } = this.layout;
            // let style = {};

            // if (useCSSTransforms) {
            //     style = setTransform(pos);
            // } else {
            //     style = setTopLeft(pos);
            //     if (!mounted) {
            //         style.left = perc(pos.left / containerWidth);
            //         style.width = perc(pos.width / containerWidth);
            //     }
            // }

            // return {
            //     x: pos.x,
            //     y: pos.y,
            //     w: pos.w,
            //     h: pos.h
            // }
            return {
                x: pos.left,
                y: pos.top,
                w: pos.width,
                h: pos.height
            }

            return style;
        }
    }
}
</script>
<style lang="less" scoped>
.vdr {
    touch-action: none;
    position: absolute;
    box-sizing: border-box;
}

.cssTransforms {
    transition-property: transform, width, height;
}

.grid-item {
    transition: all 100ms ease;
    // background-color: #fff;
}

.grid-item-inner {
    width: 100%;
    height: 100%;
    overflow-y: auto;

    // &::-webkit-scrollbar {
    //     width: 6px;
    // }

    // &::-webkit-scrollbar-thumb {
    //     width: 6px;
    //     background-color: #d2e0e1;
    //     border-radius: 4px;
    // }
}

.grid-item.dragging,
.grid-item.resizing {
    transition: unset;
    z-index: 999 !important;
}

.grid-item.dragging {
    /deep/.custom-handle {
        display: none !important;
    }
}

.grid-item.dropping {
    visibility: hidden;
}

.drag-box {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid var(--color-primary06);
    background: var(--color-primary02);
}

/deep/.custom-handle {
    box-sizing: border-box;
    position: absolute;
    width: 10px;
    height: 10px;
    z-index: 999;
    background: #fff;
}

/deep/.custom-handle-tl {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
}

/deep/.custom-handle-tm {
    top: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: n-resize;
    display: none !important;
}

/deep/.custom-handle-tr {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
}

/deep/.custom-handle-mr {
    top: 50%;
    margin-top: -4px;
    right: -4px;
    cursor: e-resize;
    display: none !important;
}

/deep/.custom-handle-br {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
}

/deep/.custom-handle-bm {
    bottom: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: s-resize;
    display: none !important;
}

/deep/.custom-handle-bl {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
}

/deep/.custom-handle-ml {
    top: 50%;
    margin-top: -4px;
    left: -4px;
    cursor: w-resize;
    display: none !important;
}
</style>