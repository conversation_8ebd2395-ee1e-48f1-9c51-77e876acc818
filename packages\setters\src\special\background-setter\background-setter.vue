<template>
    <div class="bc-setter-background">
        <fx-radio-group
            class="bc-setter-background-radio-group"
            size="mini"
            v-model="backgroundType"
            @change="handleChangeType"
        >
            <fx-radio label="color">{{ $t('颜色') }}</fx-radio>
            <fx-radio label="img">{{ $t('图片') }}</fx-radio>
        </fx-radio-group>
        <div class="bc-setter-background-color-body" v-if="backgroundType === 'color'">
            <fx-color-picker
                class="color-picker"
                size="mini"
                show-alpha
                show-hue
                show-preview0
                show-preview
                v-model="globalValue.backgroundColor"
                @change="handleColorPickerChange"
            >
                <!-- color-format="hsl" -->
            </fx-color-picker>
        </div>
        <div class="bc-setter-background-img-body" v-if="backgroundType === 'img'">
            <BackgroundImg
                ref="backgroundImg"
                :global-value="globalValue"
                @change="handleImgPickerChange"
            ></BackgroundImg>
        </div>
    </div>
</template>
<script lang="ts">
import BackgroundImg from './background-img/index.vue';
export default {
    components: {
        BackgroundImg,
    },
    props: {
        value: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            globalValue: this.initGlobalValue(),
            backgroundType: 'color',
            //切换type时临时存储对应type的value
            tempGlobalValue: this.initGlobalValue(),
        };
    },
    created() {
        this.initializeBackgroundType();
    },
    methods: {
        initializeBackgroundType() {
            this.backgroundType =
                this.globalValue.backgroundImage && this.globalValue.backgroundImage !== 'unset'
                    ? 'img'
                    : 'color';
        },
        initGlobalValue() {
            const globalValue = {
                backgroundColor: '',
                backgroundSize: 'cover',
                backgroundPosition: '',
                backgroundImage: '',
            };

            Object.entries(this.value).forEach(([key, value]) => {
                const camelKey = this.toCamelCase(key);
                if (camelKey === 'background') {
                    Object.assign(globalValue, this.parseBackground(value));
                } else if (camelKey in globalValue) {
                    globalValue[camelKey] = value;
                }
            });

            return globalValue;
        },
        // 转大写驼峰
        toCamelCase(str) {
            return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        },
        // 解析background多值
        parseBackground(background) {
            const element = document.createElement('div');
            element.style.background = background;

            return {
                backgroundColor: element.style.backgroundColor || '',
                backgroundImage: element.style.backgroundImage || '',
                backgroundSize: element.style.backgroundSize || 'cover',
                backgroundPosition: element.style.backgroundPosition || '',
            };
        },
        // TODO: 其他多值css属性是否需要解析
        isDefaultSize(value) {
            return ['unset', 'auto', 'initial', 'inherit', ''].includes(value);
        },

        handleChangeType(type) {
            // 切换前需要先把值存储到临时变量
            this.storeCurrentValues(type);
            // 切换时切换类型数据
            this.restorePreviousValues(type);
            // 确保图片的初始化设置
            this.ensureValidImageSettings(type);
            // 确保颜色的初始化设置
            this.ensureValidColorSettings(type);
            if (type === 'img') {
                this.$refs.backgroundImg.dealImgUrl();
            }
            this.$emit('change', this.globalValue);
        },
        storeCurrentValues(type) {
            if (type === 'img') {
                this.tempGlobalValue.backgroundColor = this.globalValue.backgroundColor;
            } else if (type === 'color') {
                this.tempGlobalValue.backgroundImage = this.globalValue.backgroundImage;
                this.tempGlobalValue.backgroundPosition = this.globalValue.backgroundPosition;
                this.tempGlobalValue.backgroundSize = this.globalValue.backgroundSize;
            }
        },
        restorePreviousValues(type) {
            if (type === 'img') {
                this.globalValue.backgroundColor = 'unset';
                this.globalValue.backgroundSize = this.tempGlobalValue.backgroundSize;
                this.globalValue.backgroundPosition = this.tempGlobalValue.backgroundPosition;
                this.globalValue.backgroundImage = this.tempGlobalValue.backgroundImage;
            } else if (type === 'color') {
                this.globalValue.backgroundImage = 'unset';
                this.globalValue.backgroundPosition = 'unset';
                this.globalValue.backgroundSize = 'unset';
                this.globalValue.backgroundColor = this.tempGlobalValue.backgroundColor;
            }
        },
        // 确保图片的初始化设置
        ensureValidImageSettings(type) {
            if (type === 'img' && this.isDefaultSize(this.globalValue.backgroundSize)) {
                this.globalValue.backgroundSize = 'cover';
                this.globalValue.backgroundPosition = 'left top';
            }
        },
        // 确保颜色的初始化设置
        ensureValidColorSettings(type) {
            if (
                type === 'img' &&
                (!this.globalValue.backgroundColor || this.globalValue.backgroundColor === 'unset')
            ) {
                this.globalValue.backgroundColor = 'rgba(0, 0, 0, 0)';
            }
        },
        handleColorPickerChange() {
            // 把图片相关资源存储到默认值
            this.globalValue.backgroundImage = 'unset';
            this.globalValue.backgroundPosition = 'unset';
            this.globalValue.backgroundSize = 'unset';
            this.$emit('change', this.globalValue);
        },
        handleImgPickerChange() {
            // 把颜色相关资源存储到默认值
            this.globalValue.backgroundColor = 'unset';
            this.$emit('change', this.globalValue);
        },
    },
};
</script>
<style lang="less" scoped>
.bc-setter-background {
    .bc-setter-background-radio-group {
        margin-bottom: 6px;
    }
}
</style>
<style lang="less">
.bc-setter-background {
    .bc-setter-background-color-body {
        .color-picker {
            .el-color-picker__color {
                width: 100%;
            }
        }
    }
}
</style>