/**
 * https://github.com/kaorun343/vue-property-decorator#readme
 */

// import {
//   Vue,
//   Mixins,
//   Component,
//   Emit,
//   Inject,
//   InjectReactive,
//   Model,
//   ModelSync,
//   Prop,
//   PropSync,
//   Provide,
//   ProvideReactive,
//   Ref,
//   VModel,
//   Watch,
// } from 'vue-property-decorator';

// Component.registerHooks(['$el']);
import Vue from 'vue';

export {
  Vue,
  // Mixins,
  // Component,
  // Emit,
  // Inject,
  // InjectReactive,
  // Model,
  // ModelSync,
  // Prop,
  // PropSync,
  // Provide,
  // ProvideReactive,
  // Ref,
  // VModel,
  // Watch,
};
