import Material from './components/Material.vue';
import Outline from './components/Outline.vue';

/**
 * @module engine/plugins/PaasWorkbenchInit
 * @desc 内置初始化工作台：
 * - 初始化物料库
 * - 初始化页面大纲
 * - 初始化前进、后退按钮
 */

export default class PaasWorkbenchInit {
    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        // 物料库
        config.add('workbench.activityBar.activityArea', {
            name: 'material',
            data: {
                align: "top",
                icon: {
                    content: '<path d="M736 32l256 256-256 256-256-256 256-256z m-165.504 256L736 453.504 901.504 288 736 122.496 570.496 288zM480 288v-192h-384v384h384v-192z m-320-128h256v256h-256v-256zM736 544h192v384h-384v-384h192z m-128 320h256v-256h-256v256zM96 544h384v384h-384v-384z m64 64v256h256v-256h-256z"></path>'
                },
                description: $t("组件"),
                type: 'panel',
            },
            paneldata: {
                title: $t('组件'),
                data: {
                    materials: options.workbench?.materials
                },
                component: Material
            }
        });

        if (options.workbench?.outline !== false) {
            config.add('workbench.activityBar.activityArea', {
                name: 'outline',
                data: {
                    align: "top",
                    icon: {
                        content: '<path d="M256 64v192H192v224h256v-64h448v192H448v-64H192V832h256v-64h448v192H448v-64H128V256H64V64h192zM192 128H128v64h64V128z m320 352v64h320v-64H512z m320 352H512v64h320v-64z"></path><path d="M896 64v192H384V64h512z m-64 64H448v64h384V128z"></path>'
                    },
                    description: $t('beecraft.workbench.outline', {}, "大纲树"),
                    type: 'panel',
                },
                paneldata: {
                    title: $t('beecraft.workbench.outline', {}, "大纲树"),
                    data: {
                        // materials: this.materials
                    },
                    component: Outline
                }
            });
        }

        // 设置面板
        let items: any[] = [];

        if (options.workbench?.setbar?.globalSettings) {
            items.push({
                label: $t('全局'),
                name: 'global',
                component: options.workbench?.setbar?.globalSettings
            });
        }
        items.push({
            label: $t('属性'),
            name: 'attribute',
            type: 'node'
        });

        config.set('workbench.setbar.items', items);

        // 工具栏
        config.add('workbench.tools', {
            name: 'undo',
            data: {
                align: "right",
                icon: {
                    content: '<path d="M351.808 484.352l-44.544 41.984L96 327.168 307.264 128l44.544 41.984-135.232 127.488h422.208c159.552 0 289.216 119.488 289.216 267.264 0 143.552-122.368 260.416-275.584 266.944L638.72 832H381.056v-59.392h257.728c125.12 0 226.24-93.184 226.24-207.872 0-110.848-94.528-201.6-213.76-207.552l-12.48-0.32H216.576l135.232 127.488z"></path>'
                },
                description: $t('beecraft.workbench.undo', {}, "撤销")
            },
            onClick: (item, editor) => {
                editor.actions.history.undo();
            }
        });
        config.add('workbench.tools', {
            name: 'redo',
            data: {
                align: "right",
                icon: {
                    content: '<path d="M672.192 484.352l44.544 41.984L928 327.168 716.736 128l-44.544 41.984 135.232 127.488H385.216C225.664 297.472 96 416.96 96 564.736c0 143.552 122.368 260.416 275.584 266.944L385.28 832h257.728v-59.392H385.216c-125.12 0-226.24-93.184-226.24-207.872 0-110.848 94.528-201.6 213.76-207.552l12.48-0.32h422.208l-135.232 127.488z"></path>'
                },
                description: $t('beecraft.workbench.redo', {}, "恢复")
            },
            onClick: (item, editor) => {
                editor.actions.history.redo();
            }
        });
    }
}