import {
    EditorState,
    Options,
    NodeId,
    NodeEventTypes,
    SerializedNode,
    Node,
    NodeData,
    Resolver
} from '../interfaces';
import { NodeHelpers } from './NodeHelpers';
import { EventHelpers } from './EventHelpers';
import { createNode } from '../nodes/createNode';
import {
    deserializeNode
} from '../nodes/deserializeNode';

export function QueryMethods(state: EditorState) {
    const options = state && state.options;

    const _ = () => QueryMethods(state);

    return {
        getDropPlaceholder: () => { },

        /**
         * 获取node字典
         */
        getNodes() {
            return state.nodes;
        },

        /**
         * 获取组件实例
         */
        getInstances() {
            return state.instances;
        },

        getRootNodes() {
            const { nodes } = state;
            return Object.keys(nodes).reduce((accum, id) => {
                if (!nodes[id].parent) {
                    accum.push(nodes[id]);
                }
                return accum;
            }, [] as any);
        },

        /**
         * 获取设计器参数
         */
        getOptions(): Options {
            return options;
        },

        /**
         * 获取节点
         */
        node(id: NodeId) {
            return NodeHelpers(state, id, this);
        },

        nodeByName(type: String) {
            let arr: any = [];

            arr.get = function () {
                return this.map(item => item.get())
            }

            return Object.keys(state.nodes).reduce((memo, id) => {
                if (state.nodes[id].name === type) {
                    memo.push(this.node(id));
                }
                return memo;
            }, arr);
        },

        instance(id: NodeId) {
            return state.instances[id];
        },

        getEvent(eventType: NodeEventTypes) {
            return EventHelpers(state, eventType);
        },

        getSerializedNodes: () => { },

        serialize: () => { },

        parseVNode: () => { },

        // 解析序列化节点
        // 并非是完整的页面，有可能只是某个区域
        parseSerializedNode: (serializedNode: SerializedNode, resolver?: Resolver) => ({
            toNode(normalize?: (node: Node) => void): NodeData {
                let node = deserializeNode(serializedNode, resolver || state.options.resolver);

                return _()
                    .parseFreshNode(node)
                    .toNode(normalize);
            },
        }),

        /**
         * 解析空节点，即创建完整的节点示例
         * @param node
         * @returns 
         */
        parseFreshNode: (node: NodeData) => ({
            toNode(normalize?: (node: Node) => void): Node {
                return createNode(node, state.options, normalize);
            }
        }),

        getState(): EditorState {
            return state;
        },

        toJSON() {
            const nodes = this.getNodes();
            const nodesJSON = {};

            function toJSON(node: Node) {
                const data = {
                    id: node.id,
                    name: node.name,
                    data: node.data,
                    $$data: node.$$data,
                    children: node.children,
                };

                if (node.toJSON) {
                    return node.toJSON(data, node);
                } else {
                    return Promise.resolve(data);
                }
            }

            let requires: any[] = Object.keys(nodes).reduce((acc: any[], id) => {
                acc.push(toJSON(nodes[id]));
                return acc;
            }, []);

            return Promise.all(requires).then((nodes) => {
                return nodes.reduce((memo, nodeData) => {
                    memo[nodeData.id] = nodeData;
                    return memo;
                }, {});
            });
        }
    }
}