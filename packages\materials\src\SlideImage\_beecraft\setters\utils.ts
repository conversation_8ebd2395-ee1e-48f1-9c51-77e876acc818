const isTApath = path => {
    return /^TA/.test(path);
};
const isTNpath = path => {
    return /^TN/.test(path);
};
const isTCpath = path => {
    return /^TC/.test(path);
};

const isNpath = path => {
    return /^N_/.test(path);
};
const isApath = path => {
    return /^A_/.test(path);
};

const isLocalImg = path => {
    return /^package:\/\//.test(path);
};

const isServeImg = path => {
    return /^http(s)?:\/\//.test(path);
};

export function getImgUrl(path, isMini) {
    const { util, PAAS_APPCUSTOMIZATION_MODULE } = (<any>window).FS;
    const _ = (<any>window)._;
    let size;
    
    if (_.isNumber(isMini) && isMini !== 0) {
        size = isMini;
    } else {
        size = isMini ? 3 : 1;
    }
    if (isTNpath(path)) {
        return util.getFscTempFileViewUrl(path);
    } else if (isTApath(path)) {
        return util.getFscTempFileViewUrl(path);
    } else if (isTCpath(path)) {
        return util.getFscTempFileViewUrl(path);
    } else if (isLocalImg(path)) {
        let iconName = path.replace('package://', '');
        if (iconName.indexOf('.') == -1) {
            iconName += '.png';
        }
        return PAAS_APPCUSTOMIZATION_MODULE.ASSETS_PATH + `/images/tabs/${iconName}`;
    } else if (isServeImg(path)) {
        return path;
    } else if (isApath(path)) {
        return util.getFscLinkByOpt({
            id: path,
            webp: false
        });
    } else if (isNpath(path)) {
        return util.getFscTempFileViewUrl(path);
    } else {
        return util.getFscLinkByOpt({
            id: `${path}${size}.jpg`,
            webp: true
        });
    }
}