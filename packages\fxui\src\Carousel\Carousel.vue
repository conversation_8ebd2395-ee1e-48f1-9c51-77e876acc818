<script>
    import { Vue, getChildVInstanceByType } from '@beecraft/shared';

    const FxCarousel = {
        extends: Vue.options.components.FxCarousel,
        
        methods: {
            updateItems() {
                this.items = this.$children.reduce((memo, child) => {
                    const vm = getChildVInstanceByType(child, 'FxCarouselItem');
                    if(vm) {
                        memo.push(vm);
                    }
                    return memo;
                }, []);
            },
        },

        beecraft: function() {
            return {
                name: 'FxCarousel',
                displayName: $t('uipaas.widget.slideimage', {}, '轮播容器'),
                $$data: {
                    template: {
                        name: 'FxCarousel',
                        data: {
                            autoplay: false
                        },
                        children: [{
                            name: 'FxCarouselItem'
                        },{
                            name: 'FxCarouselItem'
                        }]
                    },
                    noWrapper: true // 不需要包装器
                },
                rules: {
                    canMoveIn: (nodes) => (!nodes.find(node => node.name !== 'FxCarouselItem'))
                }
            }
        }
    }

    export default FxCarousel;
</script>
<style lang="less" scoped>
    .el-carousel {
        overflow: hidden;
    }
</style>