## 使用

```
    {
        name: 'Set<PERSON><PERSON><PERSON>',
        data: {
            label: $t('背景颜色'),              ==> 传入自定义label,注意多语
            display: 'block',
            setter: {
                component: 'ColorPickerSetter',
                ranges: ['$$data', 'style', 'backgroundImage']
            }
        }
    },

```

## 传出的值

### 选择颜色抛出的值：

```
单色：
'rgba(255, 255, 255, 1)'

渐变色：
'linear-gradient(to bottom, #ff7e5f, #feb47b)'

```
