<template>
    <fx-input v-if="!supportI18n" v-bind="{...$attrs, ...$props}" size="mini" v-on="$listeners"></fx-input>
    <fx-input-translate
        ref="ipt"
        v-else
        v-bind="{...$attrs, ...$props}"
        size="mini"
        :translateDataDef="i18nMap"
        :value="cValue"
        @change="onChange"
        @hide-translate="onHideTranslate"
    ></fx-input-translate>
</template>
<script lang="ts">
    import { generateUuid } from '@beecraft/shared';
    export default {
        name: 'StringSetter',
        props: {
            supportI18n: {
                type: Boolean,
                default: false
            },
            nodeId: {
                type: String,
            }
        },
        data() {
            return {
                i18nMap: this.$attrs.value?.i18nMap ?? {}
            }
        },
        computed: {
            cValue() {
                const value = this.$attrs.value || {};
                if (this.supportI18n) {
                    return value.i18nMap?.defaultValue ?? value ?? '';
                }else {
                    return value;
                }
            }
        },
        watch: {
            '$attrs.value.i18nMap': {
                handler(value) {
                    if (value) {
                        this.i18nMap = value;
                        this.$refs.ipt?.setTranslateData(value);
                    }
                },
                deep: true
            }
        },
        methods: {
            onChange(value) {
                if (this.supportI18n) {
                    const i18nKey = this.$attrs.value?.value ?? `i18n.${this.nodeId}.${generateUuid()}`;
                    const i18nValue = this.$refs.ipt.getTranslateData();
                    this.$emit('change', {
                        type: 'i18n',
                        value: i18nKey,
                        defaultValue: value,
                        i18nMap: {
                            ...i18nValue,
                            defaultValue: value,
                        }
                    });
                }else {
                    this.$emit('change', value);
                }
            },
            onHideTranslate() {
                this.onChange(this.cValue);
            },

        }
    }
</script>
<style lang="less" scoped>
    
</style>