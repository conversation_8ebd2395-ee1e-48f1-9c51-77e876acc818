<template>
    <div class="test">
        <h1>全局设置</h1>
        <div>
            <SettingsPane :onChange="handleChange" :related="related"></SettingsPane>
        </div>
    </div>
</template>
<script>
    import { SettingsPane } from '@beecraft/workbench';
    
    export default {
        components: {
            SettingsPane
        },
        
        data() {
            return {
                related: [{
                    name: 'Setter<PERSON><PERSON>',
                    data: {
                        label: '测试1',
                        display: 'block'
                    },
                    children: [{
                        name: 'Set<PERSON><PERSON><PERSON>',
                        data: {
                            label: '测试1',
                            display: 'block'
                        },
                        children: [{
                            id: 'text',
                            name: 'Setter<PERSON>ield',
                            data: {
                                label: '单行文本',
                                display: 'block',
                                setter: {
                                    component: 'StringSetter',
                                    ranges: ['options', 'text']
                                }
                            }
                        }]
                    }]
                }]
            }
        },

        inject: ['useInternalEditor'],

        methods: {
            handleChange(id, value) {
                const { actions } = this.useInternalEditor();

                actions.setOptions((options) => {
                    options[id] = value;
                })
            }
        }
    }
</script>
<style lang="less" scoped>
    h1 {
        line-height: 40px;
        font-size: 14px;
        color: #8492a6;
    }
    .test {
        padding: 0 10px;
    }
</style>