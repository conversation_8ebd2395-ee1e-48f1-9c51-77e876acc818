import { init, builtinPlugins } from '@beecraft/engine';

const layers = [
    { "w": 10, "h": 4, "x": 0, "y": 10, "i": "demo1", "moved": false, "static": false }, 
    { "w": 9, "h": 7, "x": 0, "y": 0, "i": "lz8942d1", "moved": false, "static": false },
    // { "w": 6, "h": 6, "x": 0, "y": 11, "i": "lz8942d2", "moved": false, "static": false },
    // { "w": 6, "h": 11, "x": 0, "y": 11, "i": "lz8942d3", "moved": false, "static": false }
];

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        import: [{
            id: 'slidegridlayout',
            name: 'SlideGridLayout',
            data: {
                rowHeight({ viewportHeight, margin }) {
                    return (viewportHeight - margin[1]) / 10 - margin[1]; // 每页5行
                },
                margin: [12, 12],
                layout: [],
                maxRowsPerPage: 10,
                mode: 'carousel',
                // mode: '',
                autoplay: true,
                pageFooterText: '您可以调整图表的大小和位置，使图表占满页面，轮播效果会更美观。',
                pageFooterBackgroundColor: '#303030',
                pageFooterFontColor: '#fff',
                controlBackgroundColor: 'rgba(0, 0, 0, 0.48)',
                controlFontColor: '#fff',
                // controlTheme: 'dark'
                // direction: 'vertical',
            },
            children: layers.map(item => {
                // if(item.i === 'lz8942dn') {
                //     item.static = true;
                // }
                return {
                    id: item.i,
                    name: 'Demo',
                    $$data: {
                        layer: item
                    }
                }
            })
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }, {
                    "title": "轮播图",
                    "name": "SlideImage"
                }]
            }]
        },
        isUseDefaultStyle: true,
        enabled: true,
        plugins: [
            builtinPlugins.FxiaokeComponentLibs,
            class Plugin {
                apply() {
                    return [{
                        event: 'beecraft.node.render.after',
                        functional: this.renderAfter.bind(this)
                    }]
                }
                renderAfter(service, { id }) {
                    console.log(id, '渲染完成');
                }
            }
        ]
    }
).then((editor: any) => {

})

// editor.actions.setCustom('slidegridlayout', function aaa(data){
//     data.mode = 'carousel';
// });

// editor.actions.setCustom('slidegridlayout', function aaa(data){
//     data.mode = '33333';
// });

// editor.actions.setOptions(function (options){
//     options.enabled = false;
// });