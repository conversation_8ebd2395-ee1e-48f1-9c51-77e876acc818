import Demo from './Demo';
import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'De<PERSON>',
            children: [{
                id: 'test',
                name: 'De<PERSON>',
            }]
        }]
    }
).then((editor) => {
    // @ts-ignore
    // 记录历史
    // editor.actions.setCustom('test', (data) => {
    //     data.text = 1
    // })

    // @ts-ignore
    // 不记录历史
    // editor.actions.history.ignore().setCustom('test', (data) => {
    //     data.text = 123
    // })

    // @ts-ignore
    // 合并历史
    // editor.actions.history.merge().setCustom('test', (data) => {
    //     data.text = 123
    // })
});