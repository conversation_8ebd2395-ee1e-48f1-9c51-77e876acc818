<template>
    <div class="bc-listitem d-flex w-100">
        <div class="bc-listitem-body flex-item-1">
            <fx-input-translate v-if="enabledI18N" ref="ipt" size="small" v-model="dValue" :translateDataDef="i18nMap"
                disabled0 :placeholder="$t('请输入内容')" @change="handleChange" @input="handleInput"
                @hide-translate="onHideTranslate">
            </fx-input-translate>
            <fx-input v-else size="mini" :placeholder="$t('请输入内容')" v-model="dValue" @change="handleChange"
                @input="handleInput"></fx-input>
        </div>
        <div class="bc-listitem-actions d-flex">
            <div class="bc-listitem-action d-flex justify-content-center align-items-center cursor-pointer"
                @click="handleDelete">
                <span class="fx-icon-process-delete"></span>
            </div>
            <div class="bc-listitem-action bc-listitem-action-drag d-flex justify-content-center align-items-center">
                <span class="fx-icon-drag"></span>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    props: {
        value: {
            type: String
        },
        // todo
        i18nMap: Object,
        // todo
        enabledI18N: {
            type: Boolean
        }
    },

    data() {
        return {
            dValue: this.value,
        }
    },

    created() {
        if (this.value) {
            this.minVal = this.value[0];
        }
    },

    methods: {
        handleDelete() {
            this.$emit('delete');
        },
        handleChange(val, i18nVal) {
            this.dValue = val || this.minVal;
            this.$emit('change', this.dValue, i18nVal);
        },
        // todo
        onHideTranslate() {
            const data = this.$refs.ipt.getTranslateData();
            this.handleChange(this.dValue, data);
        },
        handleInput(val) {
            if (val && val[0] !== this.minVal) {
                this.minVal = val[0];
            }
        }
    }

}
</script>
<style lang="less" scoped>
.bc-listitem {
    margin: 8px 0;
}

.bc-listitem-actions {
    margin: 0 4px;
}

.bc-listitem-action {
    width: 18px;
    height: 28px;
    font-size: 16px;

    &:hover {
        span:before {
            color: var(--color-primary06, #ff8000);
        }
    }

    &-drag {
        cursor: move;
    }
}
</style>