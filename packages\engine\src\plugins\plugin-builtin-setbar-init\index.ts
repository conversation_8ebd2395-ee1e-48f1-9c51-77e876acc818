import { SetterField } from '@beecraft/setters';

/**
 * @module engine/plugins/BuiltinInitSetters
 * @desc 初始化设置器
 */

export default class BuiltinInitSetbar {

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config, options }) {
        config.set('resolver', {
            SetterField
        });

        const items = [
            {
                label: $t('全局'),
                name: 'global',
                component: options.workbench?.setbar?.globalSettings
            },
            {
                label: $t('属性'),
                name: 'attribute',
                type: 'node'
            }
        ];

        if (!options.workbench?.setbar?.noStyle) {
            items.push({
                label: $t('样式'),
                name: 'style',
                type: 'node'
            });
        }

        config.set('workbench.setbar.items', items);

        config.set('workbench.setbar.activeTab', 'global');
    }
}