export const LayoutPriority = {
    Low: "low",
    Normal: "normal",
    High: "high"
};

export function clamp(value, min, max) {
    return Math.min(Math.max(value, min), max);
}
export function pushToStart(arr, value) {
    const index = arr.indexOf(value);

    if (index > -1) {
        arr.splice(index, 1);
        arr.unshift(value);
    }
}
export function pushToEnd(arr, value) {
    const index = arr.indexOf(value);

    if (index > -1) {
        arr.splice(index, 1);
        arr.push(value);
    }
}
export function createStyleSheet(
    container = document.getElementsByTagName("head")[0]
) {
    const style = document.createElement("style");
    style.type = "text/css";
    style.media = "screen";
    container.appendChild(style);
    return style;
}

export const isMacintosh = window.navigator.userAgent.indexOf("Macintosh") >= 0;

export const SashState = {
    Disabled: 0,
    AtMinimum: 1,
    AtMaximum: 2,
    Enabled: 3
};
