import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
import TestModel from './components/TestModel.vue';
import TestModel1 from './components/TestModel1.vue';
import TestModel2 from './components/TestModel2.vue';
import TestRepeater from './components/TestRepeater.vue';
import TestRepeaterItem from './components/TestRepeaterItem.vue';
import TestModelChild from './components/TestModelChild.vue';
import Page from '../components/Page.vue';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        import: [{
            name: 'Page',
            // children: [{
            //     name: 'TestRepeater',
            //     children: [{
            //         name: 'TestRepeaterItem',
            //         children: [{
            //             name: 'TestModelChild',
            //         }]
            //     }]
            // }]
            children: [{
                name: 'TestModel',
                children: [{
                    name: 'TestModel1',
                    children: [{
                        name: 'TestModel2',
                        children: [{
                            name: 'TestModelChild'
                        }]
                    }]
                }]
            }, {
                name: '<PERSON>Repeater',
                children: [{
                    name: 'TestRepeaterItem',
                    children: [{
                        name: 'TestModelChild'
                    }]
                }]
            }]
        }],
        workbench: {
            materials: [{
                "title": "测试",
                "children": [{
                    "title": "栅格容器",
                    "name": "grid_row",
                    "source": "paas"
                }, {
                    "title": "对象详情",
                    "name": "object_detail",
                    "source": "paas"
                }]
            }, {
                "title": "容器组件",
                "children": [{
                    "title": "栅格容器",
                    "name": "grid_row",
                    "source": "paas"
                }]
            },{
                "title": "数据容器",
                "children": [{
                    "title": "测试数据容器",
                    "name": "TestModel",
                },{
                    "title": "测试数据容器",
                    "name": "TestRepeater",
                }]
            }]
        },
        // todo 可能损耗性能
        // onStateChange() {
        //     console.log('state', arguments);
        // },

        resolver: {
            Page,
            TestModel,
            TestModel1,
            TestModel2,
            TestModelChild,
            TestRepeater,
            TestRepeaterItem
        },

        disableDefaultWrapper: true,

        plugins: [
            builtinPlugins.PaasWorkbenchInit,
            // builtinPlugins.PaasComponentLibs,
            builtinPlugins.PaasSetterLibs,
        ],

        useDefaultPluginPackages: false
    }
)