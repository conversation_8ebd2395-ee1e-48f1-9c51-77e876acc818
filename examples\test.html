<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <title>Document</title>
    <style>
        html,body,iframe {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
        }
    </style>
    <script src="https://ceshi112.fspage.com/fsh5/uipaas/jsapi/2.3.5/fsapi.min.js"></script>
</head>
<body>
    <button id="button">点击开始</button>
    <button id="button1">点击结束</button>
    <div class="content">

    </div>
    <script>
        FSOpen.init({
            appId: 'FSAID_98979c'
        });
        document.getElementById('button').addEventListener('click', function() {
            FSOpen.media.audio.startRecord({
                frameRecorder: true,
                frameSize: 10,
                onFrameRecorded(res) {
                    var div = document.createElement('div');
                    div.innerHTML = JSON.stringify(res);
                    document.querySelector('.content').appendChild(div);
                },
                onStop() {
                    alert('录制结束');
                }
            })

            // setTimeout(() => {
            //     alert('准备停止！');
            //     FSOpen.media.audio.stopRecord({
            //         frameRecorder: true
            //     });
            // }, 5000)
        })
        document.getElementById('button1').addEventListener('click', function() {
            FSOpen.media.audio.stopRecord();
        })
    </script>
</body>
</html>