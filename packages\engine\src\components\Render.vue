<template>
    <Editor v-bind="{...options}">
        <Frame></Frame>
    </Editor>
</template>
<script lang="js">
    import { Editor, Frame } from '@beecraft/core';
    import builtinPlugins from '../plugins';

    export default {
        components: {
            Editor,
            Frame
        },
        created() {
            const options = {
                ...this.$attrs,
                ...this.$props,
            };

            this.options = {
                ...options,
                enabled: false,
                plugins: [
                    ...builtinPlugins,
                    (options.plugins || [])
                ]
            }
        }
    }
</script>
<style lang="less" scoped>
</style>