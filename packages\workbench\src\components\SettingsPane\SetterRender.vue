<template>
    <component
        v-bind="node.data"
        :id="node.id"
        :data-id="node.id"
        :is="node.type"
        ref="instance"
    >
        <NodeElement
            v-for="id in node.children"
            :id="id"
            :key="id"
            :nodes="nodes"
        />
    </component>
</template>
<script lang="ts">
    import NodeElement from './NodeElement.vue';
    export default {
        props: ['nodeTrees'],
        
        components: {
            NodeElement
        },
    }
</script>
<style lang="less" scoped>
    
</style>