// 所有页面
export const pageLayouts = [{
    api_name: 'page1',
    themeLayout: 'themeLayout1',
    components: [
        {
            api_name: '$$page',
            children: ['Demo1'],
            name: '<PERSON>'
        },
        {
            api_name: 'Demo1',
            name: 'Demo'
        }
    ]
}, {
    api_name: 'page2',
    themeLayout: 'themeLayout1',
    components: [
        {
            api_name: '$$page',
            children: ['Demo1', 'Demo2'],
            name: '<PERSON>'
        },
        {
            api_name: 'Demo1',
            name: 'Demo'
        },
        {
            api_name: 'Demo2',
            name: 'Demo'
        }
    ]
}, {
    api_name: 'page3',
    themeLayout: 'themeLayout2',
    components: [
        {
            api_name: '$$page',
            children: ['Demo1', 'Demo2'],
            name: '<PERSON>'
        },
        {
            api_name: 'Demo1',
            name: 'Demo'
        },
        {
            api_name: 'Demo2',
            name: 'Demo'
        }
    ]
}]

// 所有主题布局
export const themeLayouts = [{
    api_name: 'themeLayout1',
    components: [{
        api_name: '$root',
        children: ['header', 'main', 'footer'],
        name: '<PERSON>'
    },{
        api_name: 'header',
        name: 'Header',
        children: ['menu']
    },{
        api_name: 'main',
        name: 'Main',
        children: ['$$page']
    },{
        api_name: 'footer',
        name: 'Footer',
    },{
        api_name: 'menu',
        name: 'menu',
        source: 'paas'
    }]
},{
    api_name: 'themeLayout2',
    components: [{
        api_name: '$root',
        children: ['$$page'],
        name: 'Page'
    }]
}]

export const generatePageLayout = function(pageApiName) {
    const pageLayout = JSON.parse(
        JSON.stringify(
            pageLayouts.find(item => item.api_name === pageApiName)
        )
    );
    const themeLayout = JSON.parse(
        JSON.stringify(
            themeLayouts.find(item => item.api_name === pageLayout.themeLayout)
        )
    )

    const components= [ ...themeLayout.components, ...pageLayout.components ];

    const dynamicContents = components.find(item => item.children.indexOf('$$page') > -1);
    dynamicContents.children = ['$$page'];

    const nodes = components.reduce((memo, item) => {
        memo[item.api_name] = {
            ...item,
            id: item.api_name
        }
        return memo;
    }, {});

    Object.keys(nodes).forEach(id => {
        const item = nodes[id];
        if(item.children?.length) {
            item.children = item.children.map(id => nodes[id]);
        }
    })

    return [nodes['$root']]
}