<template>
    <div></div>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import { init } from '@beecraft/engine';
export default {
    mounted() {
        init(
            this.$el,
            {
                resolver: {
                    Demo
                },
                import: [{
                    id: 'gridlayout',
                    name: 'SlideGridLayout',
                    data: {
                        rowHeight({ viewportHeight, margin }) {
                            return (viewportHeight - margin[1]) / 20 - margin[1];
                        },
                        margin: [12, 12],
                        layout: [],
                        mode: 'normal'
                    },
                    children: []
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "示例",
                            "name": "Demo"
                        }]
                    }]
                },
            }
        )
    }
}
</script>