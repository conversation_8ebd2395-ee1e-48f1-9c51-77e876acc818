<template>
    <div class="bc-page" :class="`page-${deviceType}`">
        <slot></slot>
    </div>
</template>
<script>
export default {
    props: {
        deviceType: {
            default: 'PC',
            type: String
        }
    },
    beecraft: function () {
        return {
            name: 'Page',
            displayName: $t('beecraft.widget.page', {}, '页面'),
            data: {
                filters: {
                    name: 'test'
                }
            },
            $$data: {
                isCanvas: true,
                // isAlwaysShowPlaceholder: true,
                noWrapper: true,
                style: {
                    'background-color': '#FAFAFA',
                    'height': '600px',
                    'box-sizing': 'border-box',
                    'border': '0.5px solid transparent',
                    'overflow-y': 'scroll'
                }
            },
            rules: {
                // canSelect: () => false,
                // canHover: () => false
            }
        }
    }
}
</script>
<style lang="less" scoped>
</style>