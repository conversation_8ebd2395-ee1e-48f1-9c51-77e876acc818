<script>
    import { Vue } from '@beecraft/shared';

    const FxCollapseItem = {
        extends: Vue.options.components.FxCollapseItem
    }

    let count = 2; // todo 修改为随机数

    FxCollapseItem.beecraft = function() {
        return {
            name: 'FxCollapseItem',
            displayName: $t('beecraft.widget.CollapseItem', {}, '折叠面板'),
            data: {
                title: $t('beecraft.widget.CollapseItem', {}, '折叠面板'),
                name: `item${count++}`
            },
            $$data: {
                isCanvas: true,
                noWrapper: true,
                style: {
                    'padding': '0'
                }
            },
            rules: {
                canSelect: () => false,
                canDrag: () => false,
            },
            hooks: {
                beforeSelect(e, node, { query, actions }) {
                    const parentNode = query.node(node.parent).get();

                    if(parentNode.name === 'FxCollapse') {
                        actions.setNodeEvent('selected', [parentNode.id])
                    }
                }
            }
        }
    }

    export default FxCollapseItem;
</script>