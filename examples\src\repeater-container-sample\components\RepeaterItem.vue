<script>
    export default {
        render(createElement) {
            return createElement('div', this.$slots.default);
        },
        beecraft() {
            return {
                name: 'RepeaterItem',
                displayName: 'RepeaterItem',
                $$data: {
                    isCanvas: true, //是否是容器
                    isAlwaysShowPlaceholder: true,
                },
                rules: {
                    canDrag: () => false,
                    canHover: () => false,
                    canSelect: () => false,
                }
            }
        }
    }
</script>