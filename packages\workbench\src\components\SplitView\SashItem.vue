<template>
    <div
      :class="{
        'sash-item': true,
        [orientation]: true,
        hover: hover,
        active: active,
        disabled: disabled,
        mac: isMacintosh,
        minimum: minimum,
        maximum: maximum,
        ['orthogonal-edge-' + orthogonalEdge]: !!orthogonalEdge
      }"
      @mousedown.stop="onPointerStart"
      @dblclick="onPointerDoublePress"
      @mouseenter="onMouseEnter(null, $event)"
      @mouseleave="onMouseLeave(null, $event)"
    >
      <div
        v-if="orthogonalStartSash && !disabled"
        class="orthogonal-drag-handle start"
        @mouseenter="onMouseEnter(orthogonalStartSash, $event)"
        @mouseleave="onMouseLeave(orthogonalStartSash, $event)"
      ></div>
      <div
        v-if="orthogonalEndSash && !disabled"
        class="orthogonal-drag-handle end"
        @mouseenter="onMouseEnter(orthogonalStartSash, $event)"
        @mouseleave="onMouseLeave(orthogonalStartSash, $event)"
      ></div>
    </div>
  </template>
  
  <script>
  import { createStyleSheet, isMacintosh, SashState } from "./utils";
  
  let globalHoverDelay = 300;
  
  const PointerEventsDisabledCssClass = "pointer-events-disabled";
  
  const Orientation = {
    HORIZONTAL: "horizontal",
    VERTICAL: "vertical"
  };
  const OrthogonalEdge = {
    North: "north",
    South: "south",
    East: "east",
    West: "west"
  };
  
  export default {
    name: "SashItem",
    props: {
      id: [String, Number],
      orientation: {
        type: String,
        required: true
        //   default: "horizontal", // horizontal vertical
      },
      // 分割线的宽度
      initSize: {
        type: Number,
        default: 4
      },
      layoutProvider: {
        type: Object,
        required: true
      },
      orthogonalStartSash: Object,
      orthogonalEndSash: Object,
      orthogonalEdge: String
    },
    data() {
      return {
        size: this.initSize,
        hoverDelayer: null,
        hover: false,
        active: false,
        disabled: false,
        minimum: false,
        maximum: false,
  
        state: SashState.Enabled,
  
        isMacintosh
      };
    },
    watch: {
      state: {
        handler(newValue, oldValue) {
          if (newValue === oldValue) {
            return;
          }
          this.disabled = newValue === SashState.Disabled;
          this.minimum = newValue === SashState.AtMinimum;
          this.maximum = newValue === SashState.AtMaximum;
          this.$emit("didEnablementChange", newValue);
        },
        immediate: true
      }
    },
    mounted() {
      this.layout();
    },
    methods: {
      // 鼠标按下
      onPointerStart(event) {
        let isMultisashResize = false;
  
        if (!event.__orthogonalSashEvent) {
          const orthogonalSash = this.getOrthogonalSash(event);
        //   debugger;
          if (orthogonalSash) {
            isMultisashResize = true;
            event.__orthogonalSashEvent = true;
            orthogonalSash.onPointerStart(event);
          }
        }
        if (this.linkedSash && !event.__linkedSashEvent) {
          event.__linkedSashEvent = true;
        }
        if (!this.state) {
          return;
        }
  
        const iframes = document.getElementsByTagName("iframe");
        for (const iframe of iframes) {
          iframe.classList.add(PointerEventsDisabledCssClass); // disable mouse events on iframes as long as we drag the sash
        }
  
        const startX = event.pageX;
        const startY = event.pageY;
        const altKey = event.altKey;
        const startEvent = {
          sash: this,
          startX,
          currentX: startX,
          startY,
          currentY: startY,
          altKey
        };
        this.active = true;
        this.$emit("didStart", startEvent);
  
        const style = createStyleSheet(this.$el);
        const updateStyle = () => {
          let cursor = "";
  
          if (isMultisashResize) {
            cursor = "all-scroll";
          } else if (this.orientation === Orientation.HORIZONTAL) {
            if (this.state === SashState.AtMinimum) {
              cursor = "s-resize";
            } else if (this.state === SashState.AtMaximum) {
              cursor = "n-resize";
            } else {
              cursor = isMacintosh ? "row-resize" : "ns-resize";
            }
          } else {
            if (this.state === SashState.AtMinimum) {
              cursor = "e-resize";
            } else if (this.state === SashState.AtMaximum) {
              cursor = "w-resize";
            } else {
              cursor = isMacintosh ? "col-resize" : "ew-resize";
            }
          }
  
          style.textContent = `* { cursor: ${cursor} !important;user-select: none !important; }`;
        };
        updateStyle();
        this.$on("didEnablementChange", updateStyle);
        const onPointerMove = (e) => {
          e.stopPropagation();
          const event = {
            sash: this,
            startX,
            currentX: e.pageX,
            startY,
            currentY: e.pageY,
            altKey
          };
          this.$emit("didChange", event);
        };
  
        const onPointerUp = (e) => {
          e.stopPropagation();
  
          this.$el.removeChild(style);
  
          this.active = false;
  
          this.$emit("didEnd");
  
          window.removeEventListener("mousemove", onPointerMove, true);
          window.removeEventListener("mouseup", onPointerUp, true);
  
          for (const iframe of iframes) {
            iframe.classList.remove(PointerEventsDisabledCssClass);
          }
        };
        window.addEventListener("mousemove", onPointerMove, true);
        window.addEventListener("mouseup", onPointerUp, true);
      },
      // 双击
      onPointerDoublePress(e) {
        const orthogonalSash = this.getOrthogonalSash(event);
        if (orthogonalSash) {
          orthogonalSash.$emit("didReset");
        }
  
        this.$emit("didReset", { sash: this });
      },
      // 鼠标进入
      onMouseEnter(sash, event) {
        sash = sash || this;
        if (sash.active) {
          sash.hoverDelayer && clearTimeout(sash.hoverDelayer);
          sash.hoverDelayer = null;
          sash.hover = true;
        } else {
          sash.hoverDelayer = setTimeout(() => {
            sash.hover = true;
            sash.hoverDelayer = null;
          }, 300);
        }
        //   if (!fromLinkedSash && sash.linkedSash) {
        //     Sash.onMouseEnter(sash.linkedSash, true);
        //   }
      },
      // 鼠标离开
      onMouseLeave(sash, event) {
        sash = sash || this;
        sash.hoverDelayer && clearTimeout(sash.hoverDelayer);
        sash.hoverDelayer = null;
        sash.hover = false;
      },
      layout() {
        requestAnimationFrame(() => {
          if (this.orientation === Orientation.VERTICAL) {
            const verticalProvider = this.layoutProvider;
            this.$el.style.left =
              verticalProvider.getVerticalSashLeft(this) - this.size / 2 + "px";
  
            if (verticalProvider.getVerticalSashTop) {
              this.$el.style.top =
                verticalProvider.getVerticalSashTop(this) + "px";
            }
  
            if (verticalProvider.getVerticalSashHeight) {
              this.$el.style.height =
                verticalProvider.getVerticalSashHeight(this) + "px";
            }
          } else {
            const horizontalProvider = this.layoutProvider;
            this.$el.style.top =
              horizontalProvider.getHorizontalSashTop(this) -
              this.size / 2 +
              "px";
  
            if (horizontalProvider.getHorizontalSashLeft) {
              this.$el.style.left =
                horizontalProvider.getHorizontalSashLeft(this) + "px";
            }
  
            if (horizontalProvider.getHorizontalSashWidth) {
              this.$el.style.width =
                horizontalProvider.getHorizontalSashWidth(this) + "px";
            }
          }
        });
      },
      getOrthogonalSash(e) {
        const target = e.initialTarget ?? e.target;
  
        if (!target || !(target instanceof HTMLElement)) {
          return undefined;
        }
  
        if (target.classList.contains("orthogonal-drag-handle")) {
          return target.classList.contains("start")
            ? this.orthogonalStartSash
            : this.orthogonalEndSash;
        }
  
        return undefined;
      },
      // --------------------------------- //
      clearSashHoverState() {
        this.onMouseLeave();
      }
    }
  };
  </script>
  
  <style lang="less" scoped>
  .sash-item {
    position: absolute;
    pointer-events: auto;
    z-index: 35;
    touch-action: none;
    &.disabled {
      cursor: default !important;
      pointer-events: none !important;
    }
    &:not(.disabled) > .orthogonal-drag-handle {
      content: " ";
      height: calc(var(--sash-size) * 2);
      width: calc(var(--sash-size) * 2);
      z-index: 100;
      display: block;
      cursor: all-scroll;
      position: absolute;
    }
    &:before {
      content: "";
      pointer-events: none;
      position: absolute;
      width: 100%;
      height: 100%;
      transition: background-color 0.1s ease-out;
      background: transparent;
    }
    &.hover:before,
    &.active:before {
      background: var(--sash-hover-border);
    }
  }
  .sash-item.vertical {
    cursor: ew-resize;
    top: 0;
    width: var(--sash-size);
    height: 100%;
    &.mac {
      cursor: col-resize;
    }
    &.minimum {
      cursor: e-resize;
    }
    &.maximum {
      cursor: w-resize;
    }
    & > .orthogonal-drag-handle.start {
      left: calc(var(--sash-size) * -0.5);
      top: calc(var(--sash-size) * -1);
    }
    & > .orthogonal-drag-handle.end {
      left: calc(var(--sash-size) * -0.5);
      bottom: calc(var(--sash-size) * -1);
    }
    &:before {
      width: var(--sash-hover-size);
      left: calc(50% - (var(--sash-hover-size) / 2));
    }
  }
  
  .sash-item.horizontal {
    cursor: ns-resize;
    left: 0;
    width: 100%;
    height: var(--sash-size);
    &.mac {
      cursor: row-resize;
    }
    &.minimum {
      cursor: s-resize;
    }
    &.maximum {
      cursor: n-resize;
    }
    &.orthogonal-edge-north:not(.disabled) > .orthogonal-drag-handle.start,
    &.orthogonal-edge-south:not(.disabled) > .orthogonal-drag-handle.end {
      cursor: nwse-resize;
    }
    &.orthogonal-edge-north:not(.disabled) > .orthogonal-drag-handle.end,
    &.orthogonal-edge-south:not(.disabled) > .orthogonal-drag-handle.start {
      cursor: nesw-resize;
    }
    & > .orthogonal-drag-handle.start {
      top: calc(var(--sash-size) * -0.5);
      left: calc(var(--sash-size) * -1);
    }
    & > .orthogonal-drag-handle.end {
      left: calc(var(--sash-size) * -0.5);
      right: calc(var(--sash-size) * -1);
    }
    &:before {
      height: var(--sash-hover-size);
      top: calc(50% - (var(--sash-hover-size) / 2));
    }
  }
  .pointer-events-disabled {
    pointer-events: none !important;
  }
  </style>
  