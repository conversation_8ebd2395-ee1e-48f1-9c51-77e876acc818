// export function isObject(value: any): boolean {
// 	return value && typeof value === 'object' && value.constructor === Object;
// }

// export function isPlainObject(value: any): boolean {
// 	return Object.prototype.toString.call(value) === '[object Object]' && value.constructor === Object;
// }

// export function isArray(value: any): boolean {
// 	return Array.isArray(value);
// }

function getTag(value) {
	return Object.prototype.toString.call(value);
}

export function isObject(value) {
	const type = typeof value;
	return value != null && (type === 'object' || type === 'function');
}

export function isObjectLike(value) {
	return typeof value === 'object' && value !== null;
}

export function isPlainObject(value) {
	if (!isObjectLike(value) || getTag(value) !== '[object Object]') {
		return false;
	}
	if (Object.getPrototypeOf(value) === null) {
		return true;
	}
	let proto = value;
	while (Object.getPrototypeOf(proto) !== null) {
		proto = Object.getPrototypeOf(proto);
	}
	return Object.getPrototypeOf(value) === proto;
}

export function isArray(value) {
	return Array.isArray(value);
}

export function isSimpleType(value) {
	// 仅仅是最普通的对象
	function _isObject(obj) {
		return obj !== null && typeof obj === 'object' && obj.constructor === Object;
	}
	return (typeof value !== 'object' && typeof value !== 'function') || (_isObject(value) && !value.render) || Array.isArray(value);
}

export function getChildVInstanceByType(instance, type) {
	let pause = false;

	while(!pause && instance) {
		if(instance.$options.name === type) {
			pause = true;
			break;
		}

		instance = instance.$children[0];
	}

	return instance;
}

export function getParentVInstanceByType(instance, type) {
	let pause = false;

	while(!pause && instance) {
		if(instance.$options.name === type) {
			pause = true;
			break;
		}

		instance = instance.$parent;
	}

	return instance;
}