import { rawApplyPatches as applyPatches } from "@beecraft/shared"

type Timeline = Array<{
    patches: [];
    inversePatches: [];
    timestamp: number;
}>;

export const HISTORY_ACTIONS = {
    UNDO: 'HISTORY_UNDO',
    REDO: 'HISTORY_REDO',
    THROTTLE: 'HISTORY_THROTTLE',
    IGNORE: 'HISTORY_IGNORE',
    MERGE: 'HISTORY_MERGE',
    CLEAR: 'HISTORY_CLEAR',
};


export class History {

    private timeline: Timeline = [];

    private pointer = -1;

    /**
     * 向时间轴中添加补丁和逆向补丁，记录操作历史
     */
    public add(patches: [], inversePatches: []): void {
        if (patches.length === 0 && inversePatches.length === 0) {
            return;
        }

        this.pointer = this.pointer + 1;
        this.timeline.length = this.pointer;
        this.timeline[this.pointer] = {
            patches,
            inversePatches,
            timestamp: Date.now(),
        };
    }

    /**
     * 带有节流功能的添加补丁和逆向补丁的方法
     */
    public throttleAdd(
        patches: [],
        inversePatches: [],
        throttleRate: number = 500
    ): void {
        if (patches.length === 0 && inversePatches.length === 0) {
            return;
        }

        if (this.timeline.length && this.pointer >= 0) {
            const {
                patches: currPatches,
                inversePatches: currInversePatches,
                timestamp,
            } = this.timeline[this.pointer];

            const now = new Date();
            const diff = now.getTime() - timestamp;

            if (diff < throttleRate) {
                this.timeline[this.pointer] = {
                    timestamp,
                    patches: [...currPatches, ...patches],
                    inversePatches: [...inversePatches, ...currInversePatches],
                };
                return;
            }
        }

        this.add(patches, inversePatches);
    }

    /**
     * 合并补丁和逆向补丁到时间轴中的当前位置
     */
    public merge(patches: [], inversePatches: []) {
        if (patches.length === 0 && inversePatches.length === 0) {
            return;
        }

        if (this.timeline.length && this.pointer >= 0) {
            const {
                patches: currPatches,
                inversePatches: currInversePatches,
                timestamp,
            } = this.timeline[this.pointer];

            this.timeline[this.pointer] = {
                timestamp,
                patches: [...currPatches, ...patches],
                inversePatches: [...inversePatches, ...currInversePatches],
            };
            return;
        }

        this.add(patches, inversePatches);
    }

    /**
     * 清空历史
     */
    public clear() {
        this.timeline = [];
        this.pointer = -1;
    }

    /**
     * 是否可以后退
     */
    public canUndo() {
        return this.pointer >= 0;
    }

    /**
     * 是否可以前进
     */
    public canRedo() {
        return this.pointer < this.timeline.length - 1;
    }

    // todo
    public undo(state: any) {
        if (!this.canUndo()) {
            return;
        }

        const { inversePatches } = this.timeline[this.pointer];
        
        this.pointer = this.pointer - 1;
        return applyPatches(state, inversePatches);
    }

    // todo
    public redo(state: any){
        if (!this.canRedo()) {
            return;
        }

        this.pointer = this.pointer + 1;
        const { patches } = this.timeline[this.pointer];
        return applyPatches(state, patches);
    }

}



