const SvgIcon = {
    functional: true,
    render: function(createElement, { props, slots }) {
        const {
            size,
            viewBox,
            style = {},
            content,
            ...otherProps
        } = props;
        return createElement('svg', {
            attrs: {
                fill: 'currentColor',
                preserveAspectRatio: 'xMidYMid meet',
                width: size,
                height: size,
                viewBox: viewBox,
                ...otherProps
            },
            style: {
                color: 'fill',
                ...style
            },
            domProps: {
                innerHTML: content
            }
        }, slots().default);
    }
}

export {
    SvgIcon
}