import Demo from './Demo';
import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: '<PERSON><PERSON>',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo",
                    "limit": 3
                }]
            }],
            setbar: {
                globalSettings: () => import('./globalSettings.vue')
            }
        },
        mediator: new window.Vue()
    }
)