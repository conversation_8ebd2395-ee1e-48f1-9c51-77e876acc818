import Vue from 'vue';
import {
    SerializedNode,
    Resolver,
    NodeData
} from '../interfaces';

const HTMLElementNames = ['div']

const restoreType = (name: string, resolver: Resolver) => {
    return typeIsHTMLElement(name) ? name : resolver[name];
};

const typeIsHTMLElement = (name: string) => {
    return HTMLElementNames.indexOf(name) > -1;
}

export const deserializeNode = (
    node: SerializedNode,
    resolver: Resolver
): NodeData => {
    const {
        name,
        data,
        $$data = {},
        children = [],
        id,
        rules,
        hooks,
        source = ''
    } = node;
    const actualType = restoreType(name, resolver) as any;

    // if(typeIsHTMLElement(name)) {
    //     $$data.isCanvas = true;
    // }

    return {
        type: actualType,
        name,
        data,
        $$data,
        id,
        children: children.map(item => deserializeNode(item, resolver)),
        source,
        rules,
        hooks
    } as unknown as NodeData;
}