## 使用

```json
{
    name: 'Set<PERSON><PERSON><PERSON>',
    data: {
        label: $t('背景', {}, '背景'),
        display: 'block',
        setter: {
            component: 'BackgroundSetter',
            ranges: ['$$data', 'style']
        }
    }
},

```

## 传出的值
```json
{
    backgroundColor: "rgba(255,255,255,0.57)"
    backgroundImage: "url('xxx')"
    backgroundPosition: "center"
    backgroundSize: "cover"
}

```





## 其他

```js
background

background: <background-color> <background-image> <background-position> <background-size> <background-repeat> <background-attachment>;

backgroundColor: red / #ff0000 / rgb(255, 0, 0) / rgba(255, 0, 0, 0.5) / hsl(0, 100%, 50%) / hsla(0, 100%, 50%, 0.5)

backgroundSize: auto / 100px / 50% / contain / cover

backgroundPosition: center / left top / center center / 50% 20% / 10px 20px   //最常见的用法是传入两个值，第一个值设定水平位置，第二个值设定垂直位置。

backgroundImage: url("")
```



