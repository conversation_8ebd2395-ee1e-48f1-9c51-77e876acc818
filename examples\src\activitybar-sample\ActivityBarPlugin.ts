import BarDemo from './BarDemo.vue';

class ActivityBarPlugin{

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore({ preData }, { actions, config }) {
        
        config.add('workbench.activityBar.activityArea', {
            name: 'test-link',
            data: {
                align: 'top',
                icon: {
                    content:  `
                        <path d="M96 223.776h32-32z m704 704v-32 32z m-576 0v32-32z m192-800a32 32 0 1 0 0-64v64z m-192-32v-32 32z m736 512a32 32 0 1 0-64 0h64z m-32 192h32-32z m-502.624-246.624a32 32 0 0 0 45.248 45.248l-45.248-45.248zM950.624 118.4a32 32 0 0 0-45.248-45.248L950.624 118.4z m-278.56-54.624a32 32 0 1 0-0.128 64l0.128-64zM928 96.224h32a32 32 0 0 0-31.936-32l-0.064 32z m-32 255.552a32 32 0 1 0 64 0h-64z m-832-128v576h64v-576H64z m736 672H224v64h576v-64z m-384-832H224v64h192v-64z m480 544v192h64v-192h-64z m-768-384a96 96 0 0 1 96-96v-64a160 160 0 0 0-160 160h64z m-64 576a160 160 0 0 0 160 160v-64a96 96 0 0 1-96-96H64z m736 160a160 160 0 0 0 160-160h-64a96 96 0 0 1-96 96v64zM470.624 598.4l480-480-45.248-45.248-480 480 45.248 45.248z m201.312-470.624l256 0.448 0.128-64-256-0.448-0.128 64zM896 96.224v255.552h64V96.224h-64z"></path>
                    `
                },
                description: "新开页签示例",
            },
            type: 'link',
            linkdata: {
                url: 'https://www.fxiaoke.com'
            }
        });

        config.add('workbench.activityBar.activityArea', {
            name: 'test-sync',
            data: {
                align: "top",
                icon: {
                    content:  `
                        <path d="M752 928H272c-52.8 0-96-43.2-96-96V192c0-52.8 43.2-96 96-96h480c52.8 0 96 43.2 96 96v640c0 52.8-43.2 96-96 96zM272 160c-17.6 0-32 14.4-32 32v640c0 17.6 14.4 32 32 32h480c17.6 0 32-14.4 32-32V192c0-17.6-14.4-32-32-32H272z"></path>
                        <path d="M352 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M512 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M672 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M691.2 712H332.8c-12.8 0-24-11.2-24-24s11.2-24 24-24h360c12.8 0 24 11.2 24 24s-11.2 24-25.6 24zM611.2 552H332.8c-12.8 0-24-11.2-24-24s11.2-24 24-24h280c12.8 0 24 11.2 24 24s-11.2 24-25.6 24z"></path>
                    `
                },
                description: "同步组件示例",
            },
            paneldata: {
                title: '同步组件',
                component: BarDemo
            }
        });

        config.add('workbench.activityBar.activityArea', {
            name: 'test-async',
            data: {
                align: "top",
                icon: {
                    content:  `
                        <path d="M752 928H272c-52.8 0-96-43.2-96-96V192c0-52.8 43.2-96 96-96h480c52.8 0 96 43.2 96 96v640c0 52.8-43.2 96-96 96zM272 160c-17.6 0-32 14.4-32 32v640c0 17.6 14.4 32 32 32h480c17.6 0 32-14.4 32-32V192c0-17.6-14.4-32-32-32H272z"></path>
                        <path d="M352 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M512 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M672 336m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"></path>
                        <path d="M691.2 712H332.8c-12.8 0-24-11.2-24-24s11.2-24 24-24h360c12.8 0 24 11.2 24 24s-11.2 24-25.6 24zM611.2 552H332.8c-12.8 0-24-11.2-24-24s11.2-24 24-24h280c12.8 0 24 11.2 24 24s-11.2 24-25.6 24z"></path>
                    `
                },
                description: "异步组件示例",
            },
            paneldata: {
                title: '异步组件',
                component: () => import('./BarDemoAsync.vue'),
                width: 400
            }
        });

        // 设置默认打开的活动栏，如果不打开，则设置为空
        config.set('workbench.activityBar.currentDock', 'test-sync');

        config.add('workbench.activityBar.activityArea', {
            name: 'test-link-bottom',
            data: {
                align: 'bottom',
                icon: {
                    content:  `
                        <path d="M96 223.776h32-32z m704 704v-32 32z m-576 0v32-32z m192-800a32 32 0 1 0 0-64v64z m-192-32v-32 32z m736 512a32 32 0 1 0-64 0h64z m-32 192h32-32z m-502.624-246.624a32 32 0 0 0 45.248 45.248l-45.248-45.248zM950.624 118.4a32 32 0 0 0-45.248-45.248L950.624 118.4z m-278.56-54.624a32 32 0 1 0-0.128 64l0.128-64zM928 96.224h32a32 32 0 0 0-31.936-32l-0.064 32z m-32 255.552a32 32 0 1 0 64 0h-64z m-832-128v576h64v-576H64z m736 672H224v64h576v-64z m-384-832H224v64h192v-64z m480 544v192h64v-192h-64z m-768-384a96 96 0 0 1 96-96v-64a160 160 0 0 0-160 160h64z m-64 576a160 160 0 0 0 160 160v-64a96 96 0 0 1-96-96H64z m736 160a160 160 0 0 0 160-160h-64a96 96 0 0 1-96 96v64zM470.624 598.4l480-480-45.248-45.248-480 480 45.248 45.248z m201.312-470.624l256 0.448 0.128-64-256-0.448-0.128 64zM896 96.224v255.552h64V96.224h-64z"></path>
                    `
                },
                description: "底部活动栏示例",
            },
            type: 'link',
            linkdata: {
                url: 'https://www.fxiaoke.com'
            }
        });
    }
}

export default ActivityBarPlugin;