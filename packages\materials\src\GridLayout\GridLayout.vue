<script>
import GridItem from './GridItem.vue';
import <PERSON><PERSON><PERSON> from './Lattice.vue';
import {
    bottom,
    getLayoutItem,
    moveElement,
    compact,
    compactType,
    cloneLayoutItem,
    withLayoutItem,
    getAllCollisions,
    findBestPos
} from './utils';
import { calcXY, calcGridItemWHPx, calcGridColWidth } from './calculateUtils';
import Vue from 'vue';

const deepEqual = _.isEqual;
const layoutClassName = "beecraft-grid-layout";
const defaultDragPos = { "w": 6, "h": 6, "i": null };
let isFirefox = false;
try {
    isFirefox = /firefox/i.test(navigator.userAgent);
} catch (e) {
    /* Ignore */
}

// 判断数组是否相同
function arraysAreEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
        return false;
    }

    const sortedArr1 = arr1.slice().sort();
    const sortedArr2 = arr2.slice().sort();

    for (let i = 0; i < sortedArr1.length; i++) {
        if (sortedArr1[i] !== sortedArr2[i]) {
            return false;
        }
    }

    return true;
}

export default {
    props: {
        className: {
            type: String,
            default: ''
        },
        colNum: {
            type: Number,
            default: 12
        },
        rowNum: {
            type: Number
        },
        rowHeight: {
            type: [Number, Function],
            default: 20
        },
        margin: {
            type: Array,
            default: [10, 10]
        },
        maxRows: {
            type: Number,
            default: Infinity
        },
        containerPadding: {
            type: Array
        },
        isDraggable: {
            type: Boolean,
            default: true
        },
        isDroppable: {
            type: Boolean,
            default: false
        },
        layout: {
            type: Array,
            default: null
        },
        compactType: {
            type: String,
            default: 'vertical'
        },
        verticalCompact: {
            type: Boolean,
            default: true
        },
        autoSize: {
            type: Boolean,
            default: true
        },

        droppingItem: {
            type: Object,
            default: {
                i: "__dropping-elem__",
                h: 1,
                w: 1
            }
        },

        // ???
        allowOverlap: {
            type: Boolean,
            default: false
        },

        transformScale: {
            type: Number,
            default: 1
        },

        handleResizeStart: {
            type: Function
        },

        handleDragStart: {
            type: Function
        },

        handleDrag: {
            type: Function
        },

        handleDragStop: {
            type: Function
        },

        handleLayoutChange: {
            type: Function
        },

        handleDropDragOver: {
            type: Function
        },

        handleDrop: {
            type: Function
        },

        handleResizeStart: {
            type: Function
        },

        handleResize: {
            type: Function
        },

        handleResizeStop: {
            type: Function
        },

        useCSSTransforms: {
            type: Boolean,
            default: true
        },

        isBounded: {
            type: Boolean,
            default: false
        },

        isResizable: {
            type: Boolean,
            default: true
        },

        // 是否开启网格
        isEnaleLattice: {
            type: Boolean,
            default: true
        },

        preventCollision: {
            type: Boolean,
            default: false
        },

        width: {
            type: Number
        },

        gridRectStroke: {
            type: String
        },

        children: {
            type: Array
        }
    },
    components: {
        GridItem,
        Lattice
    },
    provide() {
        return {
            layout: this
        }
    },
    computed: {
        cRowHeight() {
            const { rowHeight, margin, colNum, containerPadding, containerWidth } = this;

            if (typeof rowHeight === 'function') {
                return rowHeight({
                    margin,
                    containerWidth: containerWidth,
                    viewportHeight: this.viewportHeight,
                    colWidth: calcGridColWidth({
                        margin,
                        containerPadding: containerPadding || margin,
                        containerWidth,
                        cols: colNum
                    })
                });
            }

            return rowHeight;
        },
        enabled() {
            const { enabled } = this.useInternalEditor((state) => ({ enabled: state.options.enabled }));
            return enabled;
        }
    },
    inject: ['useInternalNode', 'useInternalEditor', 'useEventHandler'],
    render(createElement) {
        const { isDroppable, noop, activeDrag, layoutData, processGridItem, droppingDOMNode, droppingItem } = this;
        let layout = layoutData;
        if (droppingDOMNode) {
            layout = layout.filter(item => item.i !== droppingItem.i);
        }
        const items = layout.map(item => {
            return processGridItem(createElement, item, this.$slots.default?.find(slot => slot.key === item.i || slot.asyncMeta?.data.key === item.i))
        });

        return this.containerWidth ? createElement('div', {
            class: 'grid-layout',
            style: {
                height: this.containerHeight(),
                ...this.style,
                draggable: this.enabled,
                resizable: this.enabled,
            },
            on: {
                dragenter: isDroppable ? this.onDragEnter : noop,
                dragleave: isDroppable ? this.onDragLeave : noop,
                dragover: isDroppable ? this.onDragOver : noop,
                drop: isDroppable ? this.onDrop : noop
            }
        }, [
            ...items,
            activeDrag && this.showActiveDrag ? createElement('GridItem', {
                class: {
                    'grid-placeholder': true,
                    'placeholder-resizing': this.resizing
                },
                props: {
                    i: activeDrag.i,
                    w: activeDrag.w,
                    h: activeDrag.h,
                    x: activeDrag.x,
                    y: activeDrag.y,
                    draggable: false,
                    resizable: false,
                    isBounded: false
                }
            }, [createElement('div')]) : '',
            this.isDroppable && droppingDOMNode ? processGridItem(createElement, layoutData.find(item => item.i === droppingItem.i), '', true) : '',
            this.enabled && this.isEnaleLattice ? createElement('Lattice', {
                props: {
                    containerWidth: this.containerWidth,
                    colNum: this.colNum,
                    rowHeight: this.cRowHeight,
                    margin: this.margin,
                    gridRectStroke: this.gridRectStroke
                }
            }) : ''
        ]) : ''
    },
    data() {
        let layout = this.layout;

        return {
            resizing: false,
            activeDrag: null,
            showActiveDrag: false,
            layoutData: compact(layout || [], compactType(this), this.colNum),
            droppingDOMNode: null,
            containerWidth: this.width, // Grid自身的宽度
            viewportHeight: undefined // Grid父元素的高度
        }
    },
    created() {
        this.oldDragItem = null;
        this.oldLayoutData = null;
        this.dragEnterCounter = 0; // 父子元素穿梭会频繁触发dragenter\dragleave,所以通过计数来监控
        this.isFirstUpdateContainerSize = false;
        this.noop = function () { };

        this.oldResizeItem = null;
        this.children = [];

        this.resizeObserver = null;

        // 为什么要保留备份？为了监听GridLayout子节点数量变化（新增/删除），此时需要用layoutData和children的数量作比较，但是layoutData有响应式处理，任何变化都会触发此次比较的逻辑，所以需要一个没有响应式处理的备份。
        this.copyLayoutData = JSON.parse(JSON.stringify(this.layoutData));
        this.copyDropItem = null;
        this.observeChildren();
    },
    watch: {
        layout(val = []) {
            this.layoutData = compact(val, compactType(this), this.colNum);
            this.copyLayoutData = JSON.parse(JSON.stringify(this.layoutData));
            setTimeout(() => {
                document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_WIDTH_UPDATE', {
                    bubbles: true,
                    cancelable: true,
                    detail: {
                        id: this._uid,
                    }
                }));
            }, 250)
        }
    },
    mounted() {
        // todo 没有计算父容器的margin、padding
        this.onLayoutMaybeChanged(this.layoutData, this.layout);
        this.observe(this.$el.parentElement);
        this.isMounted = true;
    },
    methods: {
        processGridItem(createElement, item, child, isDroppingItem) {
            if (!item.i) {
                return;
            }
            return createElement('GridItem', {
                props: {
                    ...item,
                    isDroppingItem,
                    draggable: this.enabled,
                    resizable: this.enabled
                    // // 设置此项避免GridItem中的其他孙节点拖拽被GridLayout拦截
                    // dragIgnoreFrom: '.vue-grid-layout .bc-node .bc-node'
                },
                ref: isDroppingItem ? 'droppingItem' : 'griditem',
                key: item.i
            }, [
                child
            ])
        },

        // 监听子元素新增或删除 @TODO代码逻辑需要优化
        observeChildren() {
            new Vue({
                render: () => {
                    const { colNum } = this;
                    // console.log('GridLayout:children变化');
                    if (this.useInternalEditor) {
                        let { query, actions } = this.useInternalEditor?.() || {};
                        const { id, children } = this.useInternalNode?.(node => {
                            return {
                                children: node.children // 访问一下，触发依赖
                            }
                        }) || [];
                        let layout = JSON.parse(JSON.stringify(this.copyLayoutData));
                        actions = this.isMounted ? actions.history.merge() : actions.history.ignore();

                        if (!arraysAreEqual(layout.map(item => item.i), children)) {
                            let newLayout = [];
                            for (var i = 0; i < layout.length; i++) {
                                const item = layout[i];
                                if (children.indexOf(item.i) > -1) {
                                    newLayout.push(item);
                                }
                            }

                            const copyDropItem = this.copyDropItem;
                            for (var j = 0; j < children.length; j++) {
                                const id = children[j];
                                if (!newLayout.find(item => item.i === id)) {
                                    const node = query.node(id).get();
                                    const componentDefaultLayer = node.$$data?.layer || {};
                                    const l = {
                                        x: copyDropItem ? copyDropItem.x : componentDefaultLayer.x ?? defaultDragPos.x,
                                        y: copyDropItem ? copyDropItem.y : componentDefaultLayer.y ?? defaultDragPos.y,
                                        h: copyDropItem ? copyDropItem.h : componentDefaultLayer.h ?? defaultDragPos.h,
                                        w: copyDropItem ? copyDropItem.w : componentDefaultLayer.w ?? defaultDragPos.w,
                                        i: id
                                    }
                                    const oldL = {
                                        x: -1,
                                        y: -1,
                                        w: l.w,
                                        h: l.h,
                                        i: l.i
                                    };
                                    if (l.x === undefined || l.y === undefined) {
                                        const bestPos = findBestPos(colNum, newLayout, l.w, l.h);
                                        l.x = bestPos.x;
                                        l.y = bestPos.y;
                                    }
                                    // 纠正x,y是否超出边界
                                    if (l.x + l.w > colNum) {
                                        l.x = colNum - l.w;
                                    }
                                    newLayout.push(oldL)
                                    newLayout = moveElement(
                                        newLayout,
                                        oldL,
                                        l.x,
                                        l.y,
                                        this.isUserAction,
                                        this.preventCollision,
                                        compactType(this),
                                        colNum,
                                        this.allowOverlap
                                    );

                                    actions.setCustom(id, data => {
                                        if (data.isFullHeight === undefined) {
                                            data.isFullHeight = true;
                                        }
                                    });
                                }

                                this.copyDropItem = null;
                            }

                            layout = newLayout;

                            layout = compact(layout, compactType(this), colNum);
                            // 同步model
                            actions.setCustom(id, data => {
                                data.layout = layout;
                            });
                            layout.forEach(item => {
                                actions.setProp(item.i, ($$data) => {
                                    $$data.layer = Object.assign($$data.layer || {}, {
                                        w: item.w,
                                        h: item.h,
                                        x: item.x,
                                        y: item.y,
                                    });
                                });
                            });
                        }
                    }
                }
            }).$mount();
        },

        // observe(elem) {
        //     const resizeObserver = this.resizeObserver = new ResizeObserver(entries => {
        //         for (const entry of entries) {
        //             const width = entry.contentRect.width;
        //             const height = entry.contentRect.height;
        //             if (width !== this.containerWidth || height !== this.viewportHeight) {
        //                 this.containerWidth = width;
        //                 this.viewportHeight = height;
        //                 setTimeout(() => {
        //                     document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_WIDTH_UPDATE', {
        //                         bubbles: true,
        //                         cancelable: true,
        //                         detail: {
        //                             id: this._uid,
        //                             width,
        //                             height
        //                         }
        //                     }));
        //                 }, 250)
        //             }
        //         }
        //     });
        //     resizeObserver.observe(elem);
        // },

        observe(elem) {
            this.$nextTick(() => {
                const resizeObserver = this.resizeObserver = new ResizeObserver(entries => {
                    for (const entry of entries) {
                        const width = entry.contentRect.width;
                        const height = entry.contentRect.height;
                        if (width !== this.containerWidth || height !== this.viewportHeight) {
                            // 初始的宽度可能会有变动，所以需要等待稳定后再进行赋值
                            if (!this.isFirstUpdateContainerSize) {
                                if (this.delay) {
                                    clearTimeout(this.delay);
                                    this.delay = null;
                                }
                                this.delay = setTimeout(() => {
                                    this.containerWidth = width;
                                    this.viewportHeight = height;
                                    this.isFirstUpdateContainerSize = true;
                                }, 200);
                            } else {
                                this.containerWidth = width;
                                this.viewportHeight = height;
                            }
                            setTimeout(() => {
                                document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_WIDTH_UPDATE', {
                                    bubbles: true,
                                    cancelable: true,
                                    detail: {
                                        id: this._uid,
                                        width,
                                        height
                                    }
                                }));
                            }, 250)
                        }
                    }
                });
                resizeObserver.observe(elem);
            })
        },

        onDragStart(i, x, y, options) {
            const { layoutData: layout, handleDragStart } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            // Create placeholder (display only)
            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };

            this.oldDragItem = cloneLayoutItem(l);
            this.oldLayoutData = layout;
            this.activeDrag = placeholder;

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_DRAG_START', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));

            return handleDragStart?.(layout, l, l);
        },

        onDrag(i, x, y, options) {
            const { oldDragItem, allowOverlap, preventCollision, colNum, handleDrag } = this;
            let { layoutData: layout } = this;

            const l = getLayoutItem(layout, i);
            if (!l) return;

            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };

            // Move the element to the dragged location.
            const isUserAction = true;
            layout = moveElement(
                layout,
                l,
                x,
                y,
                isUserAction,
                preventCollision,
                compactType(this),
                colNum,
                allowOverlap
            );

            handleDrag?.(layout, oldDragItem, l, placeholder); // TODO 和原来不一样

            this.layoutData = allowOverlap ? layout : compact(layout, compactType(this), colNum);
            this.activeDrag = placeholder;
            this.showActiveDrag = true;

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_DRAG', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        },

        onDragStop(i, x, y, options) {
            if (!this.activeDrag) return;
            let { layoutData: layout } = this;
            const { oldDragItem, colNum, preventCollision, allowOverlap, handleDragStop } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            // Move the element here
            const isUserAction = true;
            layout = moveElement(
                layout,
                l,
                x,
                y,
                isUserAction,
                preventCollision,
                compactType(this),
                colNum,
                allowOverlap
            );

            const newLayout = allowOverlap ? layout : compact(layout, compactType(this), colNum);

            handleDragStop?.(newLayout, oldDragItem, l, null);

            const { oldLayoutData: oldLayout } = this;
            this.activeDrag = null;
            this.oldDragItem = null;
            this.oldLayoutData = null;
            this.showActiveDrag = false;

            if (this.useInternalEditor) {
                const { id, actions } = this.useInternalNode();
                const { actions: editorActions } = this.useInternalEditor();
                actions.setCustom(data => {
                    data.layout = newLayout;
                });
                newLayout.forEach(item => {
                    editorActions.history.merge().setProp(item.i, ($$data) => {
                        $$data.layer = Object.assign($$data.layer || {}, {
                            w: item.w,
                            h: item.h,
                            x: item.x,
                            y: item.y,
                        });
                    })
                });
                this.useEventHandler().dropElement();
            } else {
                this.layoutData = newLayout;
            }

            this.onLayoutMaybeChanged(newLayout, oldLayout);

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_DRAG_STOP', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        },

        onLayoutMaybeChanged(newLayout, oldLayout) {
            if (!oldLayout) oldLayout = this.layoutData;

            if (!deepEqual(oldLayout, newLayout)) {
                this.handleLayoutChange?.(newLayout);
            }
        },

        onDragEnter(e) {
            e.preventDefault();
            e.stopPropagation();
            this.dragEnterCounter++;
            if (e.toElement) { }
            // console.log('onDragEnter', e);
        },

        onDrop(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            const { droppingItem, droppingDOMNode } = this;
            const { layoutData: layout } = this;
            const item = layout.find(l => l.i === droppingItem.i);

            this.handleDrop?.(layout, item, e);

            // reset dragEnter counter on drop
            this.dragEnterCounter = 0;

            this.removeDroppingPlaceholder();

            // 外界管理layout
            if (this.useInternalEditor) {
                const { id } = this.useInternalNode();
                const { actions } = this.useInternalEditor();
                const dragTarget = this.useEventHandler().positioner.dragTarget;

                this.copyDropItem = item;
                actions.addNodeTree(dragTarget.tree, id, 1);
                this.useEventHandler().dropElement(); // 拖拽会触发drag事件，所以需要手动做一次清空
            } else {
                // 自己管理layout
                this.layoutData.push({
                    i: dragTarget.tree.rootNodeId,
                    w: item.w,
                    h: item.h,
                    x: item.x,
                    y: item.y
                });
            }
            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_DRAG_STOP', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        },

        onDragLeave(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            this.dragEnterCounter--;
            // console.log('onDragLeave', e);

            if (this.dragEnterCounter === 0) {
                this.removeDroppingPlaceholder();
                // if (this.useInternalEditor) {
                //     this.useEventHandler().dropElement(); // 拖拽会触发drag事件，所以需要手动做一次清空
                // }
            }
        },

        onDragOver(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();

            if (
                isFirefox &&
                // $FlowIgnore can't figure this out
                !e.nativeEvent.target?.classList.contains(layoutClassName)
            ) {
                return false;
            }

            const {
                droppingItem,
                handleDropDragOver,
                margin,
                colNum,
                cRowHeight: rowHeight,
                maxRows,
                containerWidth: width,
                containerPadding,
                transformScale
            } = this;



            let { layoutData: layout } = this;
            const gridRect = e.currentTarget.getBoundingClientRect();
            const layerX = e.clientX - gridRect.left;
            const layerY = e.clientY - gridRect.top;
            const droppingPosition = {
                left: layerX / transformScale,
                top: layerY / transformScale,
                e
            };

            if (!this.droppingDOMNode) {
                let componentDefaultLayer = {};
                let finalDroppingItem = {
                    ...droppingItem
                }

                if (this.useInternalEditor) {
                    const { query } = this.useInternalEditor();
                    const dragTarget = this.useEventHandler().positioner.dragTarget;
                    componentDefaultLayer = dragTarget.tree.nodes[dragTarget.tree.rootNodeId].$$data?.layer || {};
                    finalDroppingItem = Object.assign(finalDroppingItem, componentDefaultLayer)
                }

                const onDragOverResult = handleDropDragOver?.(e);
                if (onDragOverResult === false) {
                    if (this.droppingDOMNode) {
                        this.removeDroppingPlaceholder();
                    }
                    return false;
                }

                finalDroppingItem = Object.assign(finalDroppingItem, onDragOverResult)

                const positionParams = {
                    cols: colNum,
                    margin,
                    maxRows,
                    rowHeight,
                    containerWidth: width,
                    containerPadding: containerPadding || margin
                };
                const calculatedPosition = calcXY(
                    positionParams,
                    layerY,
                    layerX,
                    finalDroppingItem.w,
                    finalDroppingItem.h
                );

                this.droppingDOMNode = finalDroppingItem.i;
                this.droppingPosition = droppingPosition;

                layout = [
                    ...layout,
                    {
                        ...finalDroppingItem,
                        x: -1,
                        y: -1,
                        static: false,
                        isDraggable: true
                    }
                ];

                this.layoutData = moveElement(
                    layout,
                    layout[layout.length - 1],
                    calculatedPosition.x,
                    calculatedPosition.y,
                    this.isUserAction,
                    this.preventCollision,
                    compactType(this),
                    this.colNum,
                    this.allowOverlap
                );

                this.$forceUpdate();
                this.$nextTick(() => {
                    this.$refs.droppingItem?.moveDroppingItem(droppingPosition);
                })
            } else if (this.droppingPosition) {
                const { left, top } = this.droppingPosition;
                const shouldUpdatePosition = left != layerX || top != layerY;

                if (shouldUpdatePosition) {
                    this.droppingPosition = droppingPosition;
                    this.$forceUpdate();
                    this.$nextTick(() => {
                        this.$refs.droppingItem?.moveDroppingItem(droppingPosition);
                    })
                }
            }
        },

        removeDroppingPlaceholder() {
            const { droppingItem, colNum: cols, allowOverlap } = this;
            const { layoutData: layout } = this;

            const newLayout = compact(
                layout.filter(l => l.i !== droppingItem.i),
                compactType(this),
                cols,
                this.allowOverlap
            );

            this.layoutData = newLayout;
            this.droppingDOMNode = null;
            this.activeDrag = null;
            this.showActiveDrag = false;
            this.droppingPosition = null;
            this.$refs.droppingItem?.moveDroppingItem();
        },

        /**
         * Calculates a pixel value for the container.
         * @return {String} Container height in pixels.
         */
        containerHeight() {
            if (!this.autoSize) return;
            const { margin, cRowHeight: rowHeight, containerPadding, layoutData } = this;
            const nbRow = bottom(layoutData);
            const containerPaddingY = containerPadding
                ? containerPadding[1]
                : margin[1];

            return (
                nbRow * rowHeight +
                (nbRow - 1) * margin[1] +
                containerPaddingY * 2 +
                'px'
            )
        },

        onResizeStart(i, w, h, options) {
            const { layoutData: layout } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            this.oldResizeItem = cloneLayoutItem(l);
            this.oldLayoutData = this.layoutData;
            this.resizing = true;

            this.handleResizeStart?.(layout, l, l);
        },

        onResize(i, w, h, { e, node, size, handle }) {
            const { oldResizeItem } = this;
            const { layoutData: layout } = this;
            const { colNum: cols, preventCollision, allowOverlap } = this;

            let shouldMoveItem = false;
            let finalLayout;
            let x;
            let y;

            const [newLayout, l] = withLayoutItem(layout, i, l => {
                let hasCollisions;
                x = l.x;
                y = l.y;
                if (["bl", "ml", "tl", "tm", "tr"].indexOf(handle) !== -1) {
                    if (["bl", "tl", "ml"].indexOf(handle) !== -1) {
                        x = l.x + (l.w - w);
                        w = l.x !== x && x < 0 ? l.w : w;
                        x = x < 0 ? 0 : x;
                    }

                    if (["tr", "tm", "tl"].indexOf(handle) !== -1) {
                        y = l.y + (l.h - h);
                        h = l.y !== y && y < 0 ? l.h : h;
                        y = y < 0 ? 0 : y;
                    }

                    shouldMoveItem = true;
                }

                // Something like quad tree should be used
                // to find collisions faster
                if (preventCollision && !allowOverlap) {
                    const collisions = getAllCollisions(layout, {
                        ...l,
                        w,
                        h,
                        x,
                        y
                    }).filter(layoutItem => layoutItem.i !== l.i);
                    hasCollisions = collisions.length > 0;

                    // If we're colliding, we need adjust the placeholder.
                    if (hasCollisions) {
                        // Reset layoutItem dimensions if there were collisions
                        y = l.y;
                        h = l.h;
                        x = l.x;
                        w = l.w;
                        shouldMoveItem = false;
                    }
                }

                l.w = w;
                l.h = h;

                return l;
            });

            if (!l) return;

            finalLayout = newLayout;
            if (shouldMoveItem) {
                // Move the element to the new position.
                const isUserAction = true;
                finalLayout = moveElement(
                    newLayout,
                    l,
                    x,
                    y,
                    isUserAction,
                    this.preventCollision,
                    compactType(this),
                    cols,
                    allowOverlap
                );
            }

            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                static: true,
                i: i
            };

            this.handleResize?.(finalLayout, oldResizeItem, l, placeholder);
            this.layoutData = allowOverlap
                ? finalLayout
                : compact(finalLayout, compactType(this), cols);
            this.activeDrag = placeholder;
            this.showActiveDrag = true;

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_RESIZING', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        },

        onResizeStop(i, w, h, options) {
            const { layoutData: layout, oldResizeItem } = this;
            const { colNum: cols, allowOverlap } = this;
            const l = getLayoutItem(layout, i);

            const newLayout = allowOverlap
                ? layout
                : compact(layout, compactType(this), cols);

            this.handleResizeStop?.(newLayout, oldResizeItem, l);

            const { oldLayoutData: oldLayout } = this;

            this.activeDrag = null;
            this.showActiveDrag = false;
            if (this.useInternalEditor) {
                const { id, actions } = this.useInternalNode();
                const { actions: editorActions } = this.useInternalEditor();
                actions.setCustom(data => {
                    data.layout = newLayout;
                });
                newLayout.forEach(item => {
                    editorActions.history.merge().setProp(item.i, ($$data) => {
                        $$data.layer = Object.assign($$data.layer || {}, {
                            w: item.w,
                            h: item.h,
                            x: item.x,
                            y: item.y,
                        });
                    })
                });
            } else {
                this.layoutData = newLayout;
            }
            this.oldResizeItem = null;
            this.oldLayoutData = null;
            this.resizing = false;
            this.onLayoutMaybeChanged(newLayout, oldLayout);

            if (this.useInternalEditor) {
                const { actions } = this.useInternalEditor();
                actions.setNodeEvent('selected', [i]);
            }

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_RESIZE_STOP', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        }
    },

    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
    }
}
</script>
<style lang="less" scoped>
.grid-layout {
    position: relative;
    // width: 100% !important;
    // height: 100% !important;
    // border: 1px solid blue;
    min-height: 100%;
}

.grid-placeholder {
    background: red;
    opacity: 0.2;
    transition-duration: 100ms;
    z-index: 2 !important;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
</style>