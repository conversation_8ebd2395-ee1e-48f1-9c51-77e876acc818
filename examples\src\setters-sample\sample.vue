<template>
    <div class="w-100 h-100"></div>
</template>
<script lang="js">
import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
import Demo from './Demo.vue';
import Page from '../components/Page.vue';

export default {
    mounted() {
        this.$el.innerHTML = ''; // 粗暴隐藏，内存泄漏
        const $container = document.createElement('div');
        this.$el.appendChild($container);
        init(
            $container,
            {
                resolver: {
                    Page,
                    Demo,
                },
                compLoader: {
                    paas: Cmpt.get_paas,
                    // 'paasbiz': (name) => Fx.getBizComponent({name: 'paasbiz', css: true}, name).then(res => res()).then(res => res.default ?? res),
                    'dhtbiz': (name) => () => Fx.getBizComponent({name: 'dhtbiz', css: true}, name)
                },
                import: [{
                    id: 'root',
                    name: 'Page',
                    $$data: {
                        style: {
                            'height': '100%',
                        },
                    },
                    // children: [{
                    //     name: 'Demo',
                    // }]
                    children: []
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "示例",
                            "name": "Demo",
                            data: {
                                text: '示例'
                            }
                        },{
                            "title": "示例(paas)",
                            "name": "Demo",
                            data: {
                                text: '示例'
                            },
                            source: 'paas'
                        },{
                            "title": "示例(paasbiz)",
                            "name": "APINameInput",
                            data: {
                                text: '示例'
                            },
                            source: 'paasbiz'
                        }, {
                            "title": "示例（dhtbiz）",
                            "name": "dht_web_product_detail_all",
                            source: 'dhtbiz'
                        }]
                    }],
                    setbar: {
                        style: {
                            width: '350px'
                        }
                    }
                },
                plugins: [
                    builtinPlugins.PaasWorkbenchInit,
                    builtinPlugins.PaasSetterLibs
                ],
                useDefaultPluginPackages: false,
                disableDefaultWrapper: true,
                preprocessData: (data) => {
                    console.log('preprocessData', data);
                    return data;
                }
            }
        )
    }
}
</script>