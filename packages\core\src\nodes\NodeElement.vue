<template>
    <NodeContext :key="id" :id="id" v-if="isShow">
        <RenderNode></RenderNode>
    </NodeContext>
</template>
<script>
import RenderNode from '../render/RenderNode.vue';
import NodeContext from './NodeContext.vue';
import { deepMerge } from '@beecraft/shared';
import { loadResolver } from '@beecraft/shared';

export default {
    name: 'NodeElement',
    inheritAttrs: false,
    inject: {
        useInternalEditor: 'useInternalEditor',
    },
    props: {
        id: String,
    },
    components: {
        RenderNode,
        NodeContext
    },
    data() {
        return {
            isShow: false
        }
    },
    created() {
        const { id } = this;
        const { query, actions } = this.useInternalEditor();
        // console.log('NodeElement.created', this.id);
        // 渲染时，开始加载对应的组件
        this.useInternalEditor(state => {
            const node = state.nodes[id];

            if (!node._done) {
                loadResolver(node.name, node.source || 'paas', state.options).then(result => {
                    // 更新resolver
                    actions.history.ignore().setOptions(options => {
                        Object.assign(options.resolver, result);
                    });
                    actions.history.ignore().setState(state => {
                        const { parent, children } = node;
                        // 更新node
                        const nNode = query.parseSerializedNode(node, result).toNode();
                        nNode.parent = parent;
                        nNode.children = children;
                        state.nodes[id] = nNode;
                    });
                    this.isShow = true;
                });
            } else {
                this.isShow = true;
            }
        });
    }
}
</script>
<style lang="less" scoped>
// .skeleton {
//     width: 100%;
//     height: 120px;
//     background: linear-gradient(90deg, #fff 25%, #e6e6e6 37%, #fff 63%);
//     background-size: 400% 100%;
//     animation: skeleton-loading 1.4s ease infinite;
// }

// @keyframes skeleton-loading {
//     0% {
//         background-position: 100% 50%;
//     }

//     100% {
//         background-position: 0 50%;
//     }
// }</style>