export default [
    {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        data: {
            label: '基础类型',
            display: 'block',
        },
        children: [
            {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                data: {
                    label: '文本',
                    display: 'block',
                    setter: {
                        component: 'StringSetter',
                        ranges: ['data', 'text'],
                    },
                },
            },
            {
                name: 'Setter<PERSON>ield',
                data: {
                    label: '文本(多语言)',
                    display: 'block',
                    setter: {
                        component: 'StringSetter',
                        ranges: ['data', 'text'],
                        data: {
                            supportI18n: true
                        }
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '',
                    display: 'divider'
                },
            },
            {
                name: 'Setter<PERSON>ield',
                data: {
                    label: '文本',
                    display: 'block',
                    allowLock: true,
                    onLockChange(isLock) {
                        console.log(isLock);
                    },
                    setter: {
                        component: 'StringSetter',
                        ranges: ['data', 'text'],
                    },
                },
            },
            {
                name: 'Setter<PERSON>ield',
                data: {
                    label: '文本(inline)',
                    display: 'inline',
                    setter: {
                        component: 'StringSetter',
                        ranges: ['data', 'text'],
                    },
                },
            },
            {
                name: 'Set<PERSON><PERSON><PERSON>',
                data: {
                    label: '单选',
                    display: 'block',
                    setter: {
                        component: 'RadioSetter',
                        ranges: ['data', 'radio'],
                        data: {
                            options: [
                                {
                                    value: 'normal',
                                    label: $t('普通'),
                                },
                                {
                                    value: 'card',
                                    label: $t('卡片'),
                                },
                            ],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '选择器',
                    display: 'block',
                    setter: {
                        component: 'SelectSetter',
                        ranges: ['data', 'radio'],
                        data: {
                            options: [
                                {
                                    value: 'normal',
                                    label: $t('普通'),
                                },
                                {
                                    value: 'card',
                                    label: $t('卡片'),
                                },
                            ],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '尺寸大小',
                    display: 'block',
                    setter: {
                        component: 'LengthUnitSetter',
                        ranges: ['$$data', 'style', 'height'],
                        data: {
                            maxlength: Infinity,
                            minlength: 1,
                            units: ['rem', 'px', 'vw', '%'],
                            unitOption: 'px'
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '尺寸大小(input属性)',
                    display: 'block',
                    setter: {
                        component: 'LengthUnitSetter',
                        ranges: ['$$data', 'style', 'height'],
                        data: {
                            maxlength: Infinity,
                            minlength: 1,
                            units: ['px'],
                            size: 'small'
                        },
                    },
                },
            },
        ],
    },
    {
        name: 'SetterField',
        data: {
            label: '特殊类型',
            display: 'block',
        },
        children: [
            {
                name: 'SetterField',
                data: {
                    label: '盒模型(外边距)',
                    display: 'block',
                    allowLock: function({ id }, { query }){
                        return true;
                    },
                    setter: {
                        component: 'LayoutSetter',
                        ranges: ['$$data', 'style'],
                        data: {
                            type: 'margin',
                            units: ['px'],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '盒模型(内边距)',
                    display: 'block',
                    setter: {
                        component: 'LayoutSetter',
                        ranges: ['$$data', 'style'],
                        data: {
                            type: 'padding',
                            units: ['px'],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '盒模型(边框大小)',
                    display: 'block',
                    setter: {
                        component: 'LayoutSetter',
                        ranges: ['$$data', 'style'],
                        data: {
                            type: 'borderWidth',
                            units: ['px'],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '盒模型(边框圆角)',
                    display: 'block',
                    setter: {
                        component: 'LayoutSetter',
                        ranges: ['$$data', 'style'],
                        data: {
                            type: 'borderRadius',
                            units: ['px'],
                        },
                    },
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '背景图片',
                    display: 'block',
                    setter: {
                        component: 'ImageSetter',
                        ranges: ['data'],
                        data: {
                            heightUnits: ['px', 'rem', 'vw'],
                            showImage: true,
                            showHeight: true,
                            showAdaptaion: true,
                            showAlignType: true,
                            showOpacity: true,
                            // sourceType: ['cms'],
                            sourceType: ['local'],
                            cmsArgs: {
                                workSpaceApiName: 'cms_workspace_iD10h__c',
                                apiName: 'FSAID_127a3981'
                            },
                            // prefix: 'background',
                        },
                    },
                    nodeToValue({ data }) {
                        return {
                            image: data.src,
                            height: data.height,
                            position: data.position,
                            size: data.size,
                            opacity: data.opacity,
                        }
                    },
                    valueToNode(data) {
                        return {
                            src: data.image,
                            height: data.height,
                            position: data.position,
                            size: data.size,
                            opacity: data.opacity,
                        }
                    }
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '颜色',
                    display: 'block',
                    setter: {
                        component: 'ColorPickerSetter',
                        ranges: ['$$data', 'style', 'color'],
                    },
                    // canShow: function (node, { query }) {
                    //     return query.getOptions().bizContext?.businessScene === 'site';
                    // }
                },
            },
            {
                name: 'SetterField',
                data: {
                    label: '动作',
                    display: 'block',
                    setter: {
                        component: 'ActionSetter',
                        ranges: ['data', 'actions'],
                    },
                    // canShow: function (node, { query }) {
                    //     return query.getOptions().bizContext?.businessScene === 'site';
                    // }
                },
            },
        ],
    },
];

//     name: 'SetterField',
//     data: {
//         label: '背景高度',
//         display: 'block',
//         setter: {
//             component: 'LengthUnitSetter',
//             ranges: ['$$data', 'style'],
//             data: {
//                 unitOptions: [{
//                     value: "px",
//                     label: "px",
//                 }, {
//                     value: "rem",
//                     label: "rem",
//                 }, {
//                     value: "vw",
//                     label: "vw",
//                 }],
//                 maxlength: 100,
//                 minlength: 1
//             }
//         }
//     }
// },{
//     name: 'SetterField',
//     data: {
//         label: '背景颜色',
//         display: 'block',
//         setter: {
//             component: 'ColorPickerSetter',
//             ranges: ['$$data', 'style']
//         }
//     }
// }]
