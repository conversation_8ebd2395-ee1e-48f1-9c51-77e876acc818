<template>
    <div>
        <a class="cusor-pointer" @click="openSettingDialog">{{$t('beecraft.setters.setting', '设置')}}</a>
    </div>
</template>
<script>
    export default {
        components: { 
            NavigatorConfigDialog: () => Fx.getBizComponent('paasbiz', 'Navigator').then((res) => res()) 
        },
        data() {
            return {
                showDialog: false
            }
        },
        created() {
            this.navigatorProps = {
                // UI显示控制选项
                uiOptions: {
                    showGroup: true,
                    showCreateItem: true
                },
        
                // 字段配置选项
                fieldOptions: {
                    url: true,
                },
        
                // 默认值配置
                defaultValues: {
                    url: '',
                }
            }
        },
        methods: {
            openSettingDialog() {
                this.showDialog = true;
            },
            handleSave(form) {
                // 处理保存逻辑
                console.log('保存的表单数据:', form);
                this.showDialog = false;
            }
        }
    }
</script>