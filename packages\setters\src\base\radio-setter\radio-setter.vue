<template>
    <fx-radio-group size="mini" :value="value" @input="handleInput">
        <fx-radio v-for="item in options" :label="item.value">{{item.label}}</fx-radio>
    </fx-radio-group>
</template>
<script lang="ts">
    export default {
        props: {
            value: {
                type: String
            },
            options: {
                type: Array
            }
        },
        methods: {
            handleInput(val) {
                this.$emit('change', val);
            }
        }
    }
</script>
<style lang="less" scoped></style>