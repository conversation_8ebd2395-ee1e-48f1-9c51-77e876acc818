<template>
    <component
        v-bind="node.data"
        :id="node.id"
        :data-id="node.id"
        :is="node.type"
        ref="instance"
    >
        <NodeElement
            v-for="id in node.children"
            :id="id"
            :key="id"
            :nodes="nodes"
        />
    </component>
</template>
<script lang="ts">
    export default {

        name: 'NodeElement',

        props: {
            id: {
                type: String
            },
            nodes: {
                type: Object
            }
        },

        computed: {
            node() {
                return this.nodes?.[this.id];
            }
        }

    }
</script>
<style lang="less" scoped>
    .bc-settings-pane {
        padding: 0 12px;
    }
</style>