export default class TestWorkbenchInit {
    apply() {
        return [
            {
                event: 'beecraft.init.before',
                functional: this.initBefore.bind(this),
            },
        ];
    }

    initBefore(service, { config, options }) {

        // 样式设置
        config.add('workbench.setbar.items', {
            label: '可见性',
            name: 'style',
            type: 'node',
            component: [{
                name: 'SetterField',
                data: {
                    label: $t('paasbiz.portal-page.setters.margin', {}, '外边距'),
                    display: 'block',
                }
            }]
        });

        config.add('workbench.setbar.items', {
            label: '可见性',
            name: 'visibility',
            type: 'node',
            component: [{
                name: 'SetterField',
                data: {
                    label: '',
                    display: 'block',
                }
            }]
        });
    }
}
