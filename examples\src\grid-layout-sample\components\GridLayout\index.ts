import GridLayout from './GridLayout.vue';
// import GridItem from './GridItem.vue';

GridLayout.beecraft = function () {
    return {
        name: 'GridLayout1',
        displayName: '栅格布局容器',
        data: {
            isDroppable: true,
            layout: []
        },
        $$data: {
            isCanvas: true,
            noWrapper: true,
        },
        rules: {
            canSelect: () => false,
            canHover: () => false
        },
        hooks: {
            beforeDragOver(){
                return false;
            }
        }
    }
}

export {
    GridLayout,
    // GridItem
}