<script>
    import { deepMerge } from '@beecraft/shared';
    export default {
        name: 'NodeContext',

        inheritAttrs: false,

        props: {
            id: String,
        },

        inject: {
            useInternalEditor: 'useInternalEditor'
        },

        provide() {
            return {
                useInternalNode: this._useInternalNode.bind(this),
            }
        },

        render(createElement) {
            return this.$slots.default?.[0];
        },

        methods: {
            // TODO deepMerge支持undefined
            _useInternalNode(collect) {
                const { id } = this;
                const {
                    connectors: editorConnectors,
                    actions,
                    query,
                    ...collected
                } = this.useInternalEditor((state, query) => {
                    if(id && state.nodes[id]) {
                        if(collect) {
                            return collect(query.node(id).get(true), query);
                        }
                    }
                    return null;
                });

                return {
                    id,
                    instance: query.instance(id),
                    ...collected,
                    connectors: {
                        connect: (dom) => editorConnectors.connect(dom, id),
                        drag: (dom) => editorConnectors.drag(dom, id),
                    },
                    actions: {
                        setProp: (cb) => actions.setProp([id], cb),
                        setCustom: (cb) => actions.setCustom([id], cb)
                    }
                }
            }
        }
    }
</script>