<template>
    <div class="bc-workbench">
        <!-- 侧边栏 -->
        <div class="bc-left-area" v-if="options.enabled">
            <ActivityBar v-if="options.activityBar?.show !== false"></ActivityBar>
        </div>

        <!-- 此处需要加key，是因为vue的diff算法，导致right-area隐藏时会导致所有节点重新渲染 -->
        <div class="bc-main-area" key="main">
            <ToolBar :area="options.tools" v-if="options.enabled"></ToolBar>
            <div class="bc-main-content">
                <slot></slot>
                <FloatingSetBar v-if="options.enabled && options.type === 'floatingLayer'"></FloatingSetBar>
            </div>
        </div>

        <!-- 设置器 -->
        <div v-if="options.enabled && options.type !== 'floatingLayer'" class="bc-right-area">
            <SetBar :style="options.setbar?.style"></SetBar>
            <fx-button v-if="options.enableTypeSwitch" class="btn-fix" size="small" plain icon="fx-icon-f-obj-app29"
                square @click="_handleTypeSwitch"></fx-button>
        </div>
    </div>
</template>
<script>
import invariant from 'tiny-invariant';
import ActivityBar from './layouts/ActivityBar.vue';
import ToolBar from './layouts/ToolBar.vue';
import SetBar from './layouts/setbar/SetBar.vue';
import FloatingSetBar from './layouts/setbar/FloatingSetBar.vue';

export default {
    name: 'Workbench',

    inheritAttrs: false,
    
    components: {
        ActivityBar,
        ToolBar,
        SetBar,
        FloatingSetBar
    },

    inject: ['useInternalEditor'],

    computed: {
        options() {
            return this.useInternalEditor((state) => {
                return {
                    enabled: state.options.enabled,
                    ...state.options.workbench
                }
            })
        }
    },

    created() {
        invariant(
            this.useInternalEditor,
            '<Workbench/> must be wrapped with <Editor/>.'
        );
    },

    methods: {
        _handleTypeSwitch() {
            const { actions } = this.useInternalEditor();
            actions.history.ignore().setOptions(options => {
                if (options.workbench.type === 'normal') {
                    options.workbench.type = 'floatingLayer';
                } else {
                    options.workbench.type = 'normal';
                }
            });
        }
    }
}
</script>
<style lang="less" scoped>
.bc-workbench {
    width: 100%;
    height: 100%;
    display: flex;
    --bc-g-wb-size: 40px;
}

.bc-main-area {
    width: 0;
    flex-direction: column;
    display: flex;
    flex: 1;
}

.bc-main-content {
    position: relative;
    flex: 1;
    height: 0;
    overflow-x: auto;

    .vdr {
        top: 0;
        touch-action: none;
        position: absolute;
        box-sizing: border-box;
        // border: 1px dashed black;
    }
}

.bc-right-area {
    position: relative;
}

.btn-fix {
    position: absolute;
    top: 5px;
    right: 5px;
    border: none;
    width: calc(var(--bc-g-wb-size, 40px) - 10px);
    height: calc(var(--bc-g-wb-size, 40px) - 10px);
}
</style>