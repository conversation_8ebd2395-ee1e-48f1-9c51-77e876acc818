<template>
    <div></div>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import { init } from '@beecraft/engine';
export default {
    mounted() {
        init(
            this.$el,
            {
                resolver: {
                    Demo
                },
                import: [{
                    name: '<PERSON>'
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "示例",
                            "name": "Demo"
                        }, {
                            "title": "轮播图",
                            "name": "SlideImage"
                        }]
                    }]
                },
            }
        )
    }
}
</script>