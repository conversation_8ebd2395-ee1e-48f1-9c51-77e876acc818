import {
    ERROR_CANNOT_DRAG,
    ERROR_DUPLICATE_NODEID,
    ERROR_INVALID_NODE_ID,
    ERROR_MOVE_INCOMING_PARENT,
    ERROR_MOVE_NONCANVAS_CHILD,
    ERROR_MOVE_OUTGOING_PARENT,
    ERROR_MOVE_TO_DESCENDANT,
    ERROR_MOVE_TO_NONCANVAS_PARENT,
    ERROR_MOVE_TOP_LEVEL_NODE,
    ERROR_MOVE_CANNOT_DROP,
    // deepMerge
} from '@beecraft/shared';
import {
    EditorState,
    NodeId,
    NodeSelector
} from '../interfaces';
import invariant from 'tiny-invariant';
import { getNodesFromSelector } from '../shared/getNodesFromSelector';
import { deepMerge } from '@beecraft/shared';
import { createProxyProxy } from '../shared/proxy';

// function createNodeProxy(target = {}) {
//     return new Proxy(target, {
//         get(target, prop) {
//             if (target[prop] === undefined) {
                
//             }
//             if (typeof target[prop] === 'object' && target[prop] !== null) {
//                 return createNodeProxy(target[prop]);
//             }
//             return target[prop];
//         }
//     });
// }

export function NodeHelpers(
    state: EditorState,
    id: NodeId,
    query?: any
) {
    const node = state.nodes[id];

    const nodeHelpers = (id: NodeId) => NodeHelpers(state, id, query);
    return {
        get index() {
            return this.isRoot() ? 0 : state.nodes[node.parent].children.indexOf(id)
        },

        isCanvas() {
            return !!node.$$data.isCanvas;
        },

        get(includeContextNode = false) {
            if(includeContextNode) {
                const ancestors = this.ancestors(true);
                const contextNodeDatas = ancestors.reduce((acc, id) => {
                    const ancestorNode = state.nodes[id];
                    if (ancestorNode.$$data) {
                        if (ancestorNode.$$data.isContext) {
                            if (ancestorNode.$$data.contextNode) {
                                acc.push(ancestorNode.$$data.contextNode);
                            }
                        }
                    }
                    return acc;
                }, [])
                return createProxyProxy(undefined, node, ...contextNodeDatas);
            }
            return node;
        },

        isRoot() {
            return !node.parent;
        },

        isTopLevelNode() {
            return this.isRoot();
        },

        /************ 选中处理 ************/

        /**
         * 是否选择状态 
         */
        isSelected() {
            return state.events.selected.has(id);
        },

        /**
         * 是否悬停状态 
         */
        isHovered() {
            return state.events.hovered.has(id);
        },

        /**
         * 是否拖拽状态 
         */
        isDragged() {
            return state.events.dragged.has(id);
        },

        /**
         * 查询后代
         */
        descendants(
            deep = false
        ): NodeId[] {
            function appendChildNode(
                id: NodeId,
                descendants: NodeId[] = [],
                depth: number = 0
            ) {
                if (deep || (!deep && depth === 0)) {
                    const node = state.nodes[id];

                    if (!node) {
                        return descendants;
                    }

                    const childNodes = nodeHelpers(id).childNodes();

                    childNodes.forEach((id) => {
                        descendants.push(id);
                        descendants = appendChildNode(id, descendants, depth + 1);
                    });

                    return descendants;
                }
                return descendants;
            }

            return appendChildNode(id);
        },

        /**
         * 查询祖先
         */
        ancestors(deep = false) {
            function appendParentNode(
                id: NodeId,
                ancestors: NodeId[] = [],
                depth: number = 0
            ) {
                const node = state.nodes[id];
                if (!node) {
                    return ancestors;
                }

                ancestors.push(id);

                if (!node.parent) {
                    return ancestors;
                }

                if (deep || (!deep && depth === 0)) {
                    ancestors = appendParentNode(node.parent, ancestors, depth + 1);
                }
                return ancestors;
            }

            if (!node) {
                return [];
            }

            return appendParentNode(node.parent as NodeId);
        },

        /**
         * @desc 查询上下文祖先
         * @returns 
         */
        contextAncestor() {
            const ancestors = this.ancestors();

            return ancestors.find(id => state.nodes[id].$$data.isContext);
        },

        childNodes() {
            return node.children || [];
        },

        isDraggable(onError?: (err: string) => void) {
            try {
                const targetNode = node;
                invariant(!this.isTopLevelNode(), ERROR_MOVE_TOP_LEVEL_NODE);
                invariant(
                    NodeHelpers(state, targetNode.parent, query).isCanvas(),
                    ERROR_MOVE_NONCANVAS_CHILD
                );
                invariant(
                    targetNode.rules.canDrag?.(targetNode, nodeHelpers) ?? true,
                    ERROR_CANNOT_DRAG
                );
                return true;
            } catch (err: any) {
                if (onError) {
                    onError(err);
                }
                return false;
            }
        },

        /**
         * 是否允许其他节点放置在当前节点
         */
        isDroppable(selector: NodeSelector, onError?: (err: string) => void) {
            const targets = getNodesFromSelector(state.nodes, selector);

            const newParentNode = node;
            try {
                invariant(this.isCanvas(), ERROR_MOVE_TO_NONCANVAS_PARENT);
                invariant(
                    newParentNode.rules.canMoveIn?.(
                        targets.map((selector) => selector.node),
                        newParentNode,
                        nodeHelpers
                    ) ?? true,
                    ERROR_MOVE_INCOMING_PARENT
                );

                const parentNodes: any = {};

                targets.forEach(({ node: targetNode, exists }) => {
                    invariant(
                        targetNode.rules.canDrop?.(newParentNode, targetNode, nodeHelpers) ?? true,
                        ERROR_MOVE_CANNOT_DROP
                    );

                    if (!exists) {
                        return;
                    }

                    invariant(
                        !nodeHelpers(targetNode.id).isTopLevelNode(),
                        ERROR_MOVE_TOP_LEVEL_NODE
                    );

                    const targetDeepNodes = nodeHelpers(targetNode.id).descendants(true);

                    invariant(
                        !targetDeepNodes.includes(newParentNode.id) &&
                        newParentNode.id !== targetNode.id,
                        ERROR_MOVE_TO_DESCENDANT
                    );

                    if (!targetNode.parent) {
                        return;
                    }

                    const currentParentNode = state.nodes[targetNode.parent];

                    invariant(
                        (currentParentNode as any).$$data.isCanvas,
                        ERROR_MOVE_NONCANVAS_CHILD
                    );

                    invariant(
                        currentParentNode ||
                        (!currentParentNode && !state.nodes[targetNode.id]),
                        ERROR_DUPLICATE_NODEID
                    );

                    if (currentParentNode.id !== newParentNode.id) {
                        if (!parentNodes[currentParentNode.id]) {
                            parentNodes[currentParentNode.id] = [];
                        }

                        parentNodes[currentParentNode.id].push(targetNode);
                    }
                });

                Object.keys(parentNodes).forEach((parentNodeId) => {
                    const childNodes = parentNodes[parentNodeId];
                    const parentNode = state.nodes[parentNodeId];

                    invariant(
                        parentNode.rules.canMoveOut?.(childNodes, parentNode, nodeHelpers) ?? true,
                        ERROR_MOVE_OUTGOING_PARENT
                    );
                });

                return true;
            } catch (err: any) {
                if (onError) {
                    onError(err);
                }
                return false;
            }
        },

        /**
         * 是否允许被选择 
         */
        isSelectable(onError?: (err: string) => void) {
            try {
                invariant(
                    node.rules.canSelect?.(node, nodeHelpers) ?? true,
                    'Node cannot be selected'
                );

                return true;
            } catch (err: any) {
                if (onError) {
                    onError(err);
                }
                return false;
            }
        },

        /**
         * 是否允许悬停
         */
        isHoverable(onError?: (err: string) => void) {
            try {
                invariant(
                    node.rules.canHover?.(node, nodeHelpers) ?? true,
                    'Node cannot be hovered'
                );

                return true;
            } catch (err: any) {
                if (onError) {
                    onError(err);
                }
                return false;
            }
        },

        /**
         * 是否允许被删除
         */
        isDeletable(onError?: (err: string) => void) {
            try {
                invariant(
                    node.rules.canDelete?.(node, nodeHelpers) ?? true,
                    'Node cannot be deleted'
                );

                return true;
            } catch (err: any) {
                if (onError) {
                    onError(err);
                }
                return false;
            }
        },

        // todo 为什么有两个？？？
        callHook(name, ...args) {
            if (!args.length) {
                args = [this.get(), { query }]
            }
            const hooks = node.hooks || {};
            return hooks[name]?.(...args);
        },

        runHook(name) {
            return node.hooks[name]?.(this.get(), { query });
        },
    }
}