import RichtextSetter from "./setters/richtext-setter.vue"
export default function () {
    return {
        type: 'RichTextWidget',
        displayName: $t('beecraft.materials.richtextCom',{},'富文本组件'),
        data: {
            content: {
                "type": "doc",
                "content": [
                    {
                        "type": "paragraph",
                        "content": {},
                        "attrs": {
                            "textAlign": "left"
                        }
                    }
                ]
            }
        },
        $$data: {
            noWrapper: false, // 需要包装器
        },
        related: {
            attributeSettings: [{
                name: 'SetterField',
                data: {
                    // label: $t('轮播项'),
                    display: 'block',
                    setter: {
                        component: RichtextSetter
                    }
                }
            }]
        }
    }
}