import { isPlainObject, Vue } from "@beecraft/shared";
import { Editor, Frame } from '@beecraft/core';
import { Workbench } from '@beecraft/workbench';

import builtinPlugins from './plugins';

// todo version需要从pkg中读取
// export const version = VERSION_PLACEHOLDER;
export const version = '1.0.0';

// 初始化
export async function init(
    container?: HTMLElement,
    options?: any,
) {
    options = Object.assign({
        useDefaultPluginPackages: true
    }, options)
    let engineOptions: any = null;
    let engineContainer: any = null;

    if(isPlainObject(container)) {
        engineOptions = container;
        engineContainer = document.createElement('div');
        engineContainer.id = 'beecraft';
        document.body.appendChild(engineContainer);
    }else {
        engineOptions = options;
        engineContainer = container;
        if(!container) {
            engineContainer = document.createElement('div');
            engineContainer.id = 'beecraft';
            document.body.appendChild(engineContainer);
        }
    }

    return new Promise<Vue>((resolve) => {
        const instance = new Vue({
            render(createElement) {
                const frame = createElement(Frame);
    
                const workbench = engineOptions.mode === 'preview'
                    ? frame
                    : createElement(Workbench, [frame]);
    
                return createElement(Editor, {
                    attrs: {
                        ...engineOptions,
                        enabled: engineOptions.mode !== 'preview' && engineOptions.enabled,
                        plugins: engineOptions.useDefaultPluginPackages ? [
                            ...builtinPlugins,
                            ...(engineOptions.plugins || [])
                        ] : [
                            ...(engineOptions.plugins || [])
                        ]
                    },
                    on: {
                        onCreated(editor) {
                            resolve(editor);
                        }
                    }
                }, [workbench]);
            }
        }).$mount(engineContainer);
    });
}