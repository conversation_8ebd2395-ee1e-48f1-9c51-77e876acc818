<template>
    <div class="w-100 h-100"></div>
</template>
<script lang="js">
import Page from '../components/Page.vue';
import { init } from '@beecraft/engine';
import { builtinPlugins } from '@beecraft/engine';
import Demo from '../components/Demo.vue';
import TestWorkbenchInit from './TestWorkbenchInit';

// const Demo = () => import('../components/Demo.vue');
// Demo.beecraft = () => import('./demoBeecraft');

export default {
    mounted() {
        this.$el.innerHTML = ''; // 粗暴隐藏，内存泄漏
        const $container = document.createElement('div');
        this.$el.appendChild($container);
        init(
            $container,
            {
                resolver: {
                    Demo,
                    Page,
                },
                import: [{
                    name: 'Page',
                    children: []
                }],
                workbench: {
                    materials: [{
                        "title": "示例组件",
                        "children": [{
                            "title": "示例",
                            "name": "Demo"
                        }, {
                            "title": "菜单",
                            "name": "menu",
                            "source": "paas"
                        }, {
                            "title": "栅格容器",
                            "name": "grid_row",
                            "source": "paas"
                        }, {
                            "title": "页签容器",
                            "name": "tabs",
                            "source": "paas"
                        }, {
                            "title": "折叠容器",
                            "name": "collapse",
                            "source": "paas"
                        }]
                    }],
                    type: 'floatingLayer',
                    enableTypeSwitch: true,
                    setbar: {
                        style: {
                            width: '320px'
                        }
                    }
                },
                plugins: [
                    builtinPlugins.PaasWorkbenchInit,
                    builtinPlugins.PaasComponentLibs,
                    builtinPlugins.PaasSetterLibs,
                    TestWorkbenchInit
                ],
                useDefaultPluginPackages: false
            }
        )
    }
}
</script>