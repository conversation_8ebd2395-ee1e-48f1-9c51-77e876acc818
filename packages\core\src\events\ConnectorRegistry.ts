import {
    ConnectorPayload,
    RegisteredConnector
} from '../interfaces';
import { generateUuid } from '@beecraft/shared';

/**
 * 连接器管理器
 */
export default class ConnectorRegistry {

    private registry: Map<String, RegisteredConnector> = new Map();

    private elementIdMap: WeakMap<HTMLElement, string> = new WeakMap();

    private isEnabled: boolean = false;

    /**
     * 获取与给定的元素关联的ID
     */
    private getElementId(element: HTMLElement) {
        const existingId = this.elementIdMap.get(element);

        if (existingId) {
            return existingId;
        }

        const newId = generateUuid();

        this.elementIdMap.set(element, newId);

        return newId;
    }


    /**
     * 注册连接器
     */
    public register(element: HTMLElement, connectorPayload: ConnectorPayload): RegisteredConnector {
        const  existingConnector = this.getByElement(element, connectorPayload.name);

        if(existingConnector) {
            if (connectorPayload.required === existingConnector.required) {
                return existingConnector;
            }
            existingConnector.disable();
        }

        let cleanup: any = null;

        const id = this.getConnectorId(element, connectorPayload.name);

        this.registry.set(id, {
            id,
            required: connectorPayload.required,
            enable: () => {
                if (cleanup) {
                    cleanup();
                }
                cleanup = connectorPayload.connector(
                    element,
                    connectorPayload.required,
                    connectorPayload.options
                );
            },
            disable: () => {
                if (!cleanup) {
                    return;
                }
                cleanup();
            },
            remove: () => {
                return this.remove(id);
            }
        });

        if(this.isEnabled) {
            this.registry.get(id)?.enable();
        }

        return this.registry.get(id) as RegisteredConnector;

    }

    /**
     * 获取关联器ID
     */
    public getConnectorId(element: HTMLElement, connectorName: string): string {
        const elementId = this.getElementId(element);

        return `${connectorName}--${elementId}`;
    }

    /**
     * 通过ID获取连接器
     */
    public get(id: string): RegisteredConnector | void {
        return this.registry.get(id);
    }

    /**
     * 通过ID删除连接器
     */
    public remove(id: string): void {
        const connector = this.get(id);

        if (!connector) {
            return;
        }

        connector.disable();

        this.registry.delete(connector.id);
    }

    /**
     * 启用
     */
    public enable(): void {
        if (this.isEnabled) {
            return;
        }
        this.isEnabled = true;
        
        this.registry.forEach((connectors) => {
            connectors.enable();
        });
    }

    /**
     * 禁用
     */
    public disable(): void {
        if (!this.isEnabled) {
            return;
        }
        this.isEnabled = false;
        
        this.registry.forEach((connectors) => {
            connectors.disable();
        });
    }

    /**
     * 通过元素获取连接器
     */
    public getByElement(element: HTMLElement, connectorName: string): RegisteredConnector | void {
        return this.get(this.getConnectorId(element, connectorName));
    }

    /**
     * 通过元素删除连接器
     */
    public removeByElement(element: HTMLElement, connectorName: string): void {
        return this.remove(this.getConnectorId(element, connectorName));
    }

    /**
     * 清空
     */
    public clear(): void {
        this.disable();
        this.elementIdMap = new WeakMap();
        this.registry = new Map();
    }

}