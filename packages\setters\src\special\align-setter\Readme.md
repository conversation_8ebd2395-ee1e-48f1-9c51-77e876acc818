## 使用

```
    {
        name: 'SetterField',
        data: {
            label: $t('对齐方式', {}, '对齐方式'),
            display: 'block',
            helpText: $t('beecraft.setters.component.alignment', {}, '容器内组件的对齐方式'),
            setter: {
                component: 'AlignSetter',
                ranges: ['$$data', 'style'],
                data: {
                    type: 'vertical'                  ==> 可以传入 'vertical' || 'horizontal' || 'text'   竖直，水平，文本
                }
            }
        }
    },

```

## 传出的值

### vertical：

```
{
  alignItems: 'xxx'
}

```

### horizontal：

```
{
  justifyContent: 'xxx'
}
```

### text

```
{
  textAlign: 'xxx'
}
```

## 其他
```js
align-items: 
    stretch：默认值。项目将被拉伸以填充容器的交叉轴。
    flex-start：项目在容器的交叉轴开始位置对齐。
    flex-end：项目在容器的交叉轴结束位置对齐。
    center：项目在容器的交叉轴中间对齐。
    baseline：项目的基线对齐。项目将根据其内容的第一行文本的基线进行对齐。

```

```js
justify-content:
    flex-start：项目向主轴的起始位置对齐。
    flex-end：项目向主轴的结束位置对齐。
    center：项目在主轴的中间对齐。
    space-between：项目在主轴上平均分布，第一个项目放置在起始位置，最后一个项目放置在结束位置，其他项目之间的间隔相等。
    space-around：项目在主轴上平均分布，每个项目两侧的间隔相等。第一个和最后一个项目与容器边缘之间的间隔为其他项目之间间隔的一半。
    space-evenly：项目在主轴上平均分布，每个项目之间的间隔相等，包括第一个和最后一个项目与容器边缘之间的间隔。
```

```js
text-align:
    left：文本或内容向左对齐（这是大多数语言的默认设置）。
    right：文本或内容向右对齐。
    center：文本或内容在容器内居中对齐。
    justify：文本或内容两端对齐，通过调整单词间的空隙，使每一行的左右边缘都对齐（常用于段落文本，以实现整齐的文本块）。

```
