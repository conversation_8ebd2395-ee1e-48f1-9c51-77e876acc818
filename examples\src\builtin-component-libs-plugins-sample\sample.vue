<template>
    <Editor v-bind="options">
        <Workbench v-bind="options.workbench">
            <Frame></Frame>
        </Workbench>
    </Editor>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import { Editor, Frame } from '@beecraft/core'; // 导入编辑器和页面框架组件
import { Workbench } from '@beecraft/workbench'; // 导入工作台组件
import { builtinPlugins } from '@beecraft/engine';

export default {
    components: {
        Editor,
        Frame,
        Workbench
    },
    created() {
        this.options = {
            resolver: {
                Demo
            },
            import: [{
                name: 'Page'
            }],
            workbench: {
                type: 'floatlayer',
                materials: [{
                    "title": "示例组件",
                    "children": [{
                        "title": "示例",
                        "name": "Demo"
                    }]
                }],
            },
            plugins: [
                
            ]
        }
    }
}
</script>