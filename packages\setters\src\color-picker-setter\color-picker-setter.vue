<template>
    <div class="color-picker">
        <fx-color-picker v-model="value" size="small" v-bind="$attrs" show-alpha show-hue show-input v-on="$listeners"
            @change="onColorChange"></fx-color-picker>
    </div>
</template>
<script lang="ts">
export default {
    name: 'ColorPickerSetter',
    props: {
        value: {
            type: String
        }
    },
    methods: {
        onColorChange(value) {
            this.$emit('change', value)
        }
    }
}
</script>
<style lang="less">
.color-picker {
    display: flex;
    width: 100%;
    height: 28px;
    line-height: 28px;
    align-items: center;
    border: 1px solid #c1c5ce;
    border: 1px solid var(--color-neutrals07);
    border-radius: 4px;

    .el-color-picker__color {
        border: none;
    }

    .el-color-picker {
        width: 100%;
        height: 28px;
    }

    .el-color-picker__trigger {
        width: 100%;
        height: 28px;
        border: none;
    }

    .el-icon-close {
        display: none;
    }

    .el-icon-arrow-down {
        display: none;
    }

    .color-picker-percent-input {
        width: 100px;

        .el-input__inner {
            border-radius: 0px;
            border: none;
            border-left: 1px solid #c1c5ce;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }
}
</style>
