<script>
    export default {
        render(createElement) {
            const { renderItem, list } = this;
            return createElement('div', list.map(item => renderItem({text: item})));
        },
        data() {
            return {
                name: 'hello world',
                list: [1, 2, 3, 4, 5]
            }
        },
        created() {
            this.renderItem = (data) => {
                const scopedSlots = this.$scopedSlots;
                return (scopedSlots.renderItems || scopedSlots.default)(data)[0];
            }
        },
        beecraft() {
            return {
                $$data: {
                    isCanvas: true, //是否是容器
                    isRepeater: true, //是否是中继器
                    template: {
                        name: 'TestRepeater',
                        children: [{
                            name: 'TestRepeaterItem',
                            children: []
                        }]
                    },
                },
                rules: {
                    canDrag: () => false,
                    canHover: () => false,
                    canSelect: () => false,
                }
            }
        }
    }
</script>