<template>
    <div class="bc-outline">
        <div class="bc-outline-top">
            <fx-input :placeholder="$t('请输入')" v-model="keyword" size="mini" clearable is-search @change="handleChange"
                @on-search="handleSearch"></fx-input>
        </div>
        <div class="bc-outline-content">
            <fx-tree class="filter-tree" :data="treeData" :props="{ children: 'children', label: 'label' }"
                default-expand-all highlight-current :filter-node-method="filterNode" :expand-on-click-node="false"
                ref="tree" @node-click="handleClick">
            </fx-tree>
        </div>
    </div>
</template>
<script>
import { Blueprint } from '@beecraft/core';

export default {
    name: 'Material',

    components: {
        Blueprint
    },

    props: {
        materials: Array
    },

    inject: ['useInternalEditor'],

    data() {
        return {
            keyword: ''
        }
    },

    computed: {
        treeData() {
            const { query } = this.useInternalEditor();
            const rootNodes = query.getRootNodes();

            return _buildTree(rootNodes)

            function _buildTree(nodes) {
                return nodes.map(node => {
                    return {
                        id: node.id,
                        label: node.displayName || node.name || $t('未知'),
                        children: _buildTree(node.children.map(id => query.node(id).get()))
                    }
                })
            }
        }
    },

    watch: {
        keyword(val) {
            this.keywordChange(val);
        }
    },

    methods: {
        keywordChange(val) {
            this.$refs.tree.filter(val);
        },

        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },

        handleDragEnd(draggingNode, dropNode, dropType, ev) {
            const { query, actions } = this.useInternalEditor();

            const nodeId = draggingNode.data.id;
            const dropNodeId = dropNode.data.id;

            if (dropType === 'before' || dropType === 'after') {
                const dropNodeHelper = query.node(dropNodeId);

                actions.move([nodeId], dropNodeHelper.get().parent, dropNodeHelper.index + (dropType === 'before' ? 0 : 1));
            } else {
                actions.move([nodeId], dropNodeId, 0);
            }
        },

        handleClick(data) {
            const store = this.useInternalEditor();
            const { actions, query } = store;

            const nodeAccessor = query.node(data.id);
            if (
                nodeAccessor.callHook('beforeSelect', undefined, nodeAccessor.get(), store) === false ||
                !nodeAccessor.isSelectable()
            ) {
                return;
            }

            actions.setNodeEvent('selected', [data.id]);
            query.node(data.id).get()?.dom.scrollIntoView();
        }
    }
}
</script>
<style lang="less" scoped>
.bc-outline-top {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
}

.bc-outline-content {
    ::v-deep .el-tree-node__children {
        position: relative;

        &:before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 6px;
            border-right: 1px solid dashed;
            border-right: 1px rgb(217, 221, 228) solid;
        }
    }
}
</style>