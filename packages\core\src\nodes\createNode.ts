import { NodeData, Node, Options } from '../interfaces';
import { generateUuid as getRandomNodeId, deepMerge } from '@beecraft/shared';

/**
 * @desc 创建节点
 * 1. 合并数据：全局节点默认值、类型节点默认值、实例节点默认值
 * @param { Object } newNode - 页面节点的数据
 * @param { Object } defaultNode - 全局默认节点
 */
export function createNode(
    newNode: NodeData,
    options: Options,
    normalize?: (node: Node) => void
): Node {
    let actualType = newNode.type as any;
    let id = newNode.id || getRandomNodeId(`widget_${newNode.name}_`);

    let node = {
        id,
        type: newNode.type,
        name: newNode.name,
        source: newNode.source,
        _hydrationTimestamp: Date.now(),
        _done: !!actualType,
        data: {
            ...(newNode.data || {})
        },
        $$data: {
            ...(newNode.$$data || {})
        },
        related: {},
        events: {
            selected: false,
            dragged: false,
            hovered: false,
        },
        // false-不允许，undefined、true-允许
        rules: {
            // canDrag: () => true,
            // canDrop: () => true,
            // canMoveIn: () => true,
            // canMoveOut: () => true,
            // canSelect: () => true,
            // canDelete: () => true,
            // canCopy: () => true,
            // canHover: () => true,
            ...(newNode.rules || {})
        },
        hooks: {
            // beforeSelect: undefined,
            // beforeCreate: undefined, // todo 未支持
            // created: undefined, // todo 未支持
            // beforeRender: undefined,
            // rendered: undefined,
            // deleted: undefined
            ...(newNode.hooks || {})
        },
        dom: null,
    } as Node;

    if (normalize) {
        normalize(node);
    }

    const userComponentConfig = actualType?.beecraft?.(options);

    if(actualType) {
        const userComponentConfig = actualType.beecraft?.(options);
        if (userComponentConfig) {
            node.displayName =
                userComponentConfig.displayName ||
                userComponentConfig.name ||
                node.displayName;
    
            // todo 需要考虑全局node存在的必要性
            const defaultNode = (options as any).node || {};
    
            // todo deepMerge去做空值处理
            node.data = deepMerge({}, defaultNode.data || {}, userComponentConfig.data || {}, node.data || {});
            node.$$data = deepMerge({}, defaultNode.$$data || {}, userComponentConfig.$$data || {}, node.$$data || {});
            node.rules = deepMerge({}, node.rules, userComponentConfig.rules || {}, newNode.rules || {});
            node.hooks = deepMerge({}, node.hooks, userComponentConfig.hooks || {}, newNode.hooks || {});
            // node.data = Object.assign({}, userComponentConfig.data || {}, node.data || {});
            // node.$$data = Object.assign({}, userComponentConfig.$$data || {}, node.$$data || {});
            // node.rules = Object.assign({}, node.rules, userComponentConfig.rules || {}, newNode.rules || {});
    
            if (userComponentConfig.related) {
                Object.keys(userComponentConfig.related).forEach((key) => {
                    node.related[key] = userComponentConfig.related[key];
                });
            }
            node.extra = deepMerge({}, userComponentConfig.extra || {});
            if(userComponentConfig.toJSON) {
                node.toJSON = userComponentConfig.toJSON;
            }
    
            // TODO node钩子，特殊实现
            node = node.hooks.created?.(node) || node;
        }
    }

    return node;
}