<script>
    export default {
        render(createElement) {
            // if(this.$scopedSlots.renderItems) {
            //     debugger;
            // }
            return createElement(
                'div',
                this.$scopedSlots.renderItems
                    ? this.list.map((item, index) => {
                        return this.$scopedSlots.renderItems(
                            { 
                                data: item,
                                $$data: { disabledConnect: index > 0 }
                            }
                        )
                    })
                    : this.$slots.default
            );
        },
        data() {
            return {
                list: [{
                    name: 'RepeaterItem1',
                    address: 'address1'
                }, {
                    id: 'RepeaterItem2',
                    name: 'RepeaterItem2',
                    address: 'address2'
                },{
                    id: 'RepeaterItem3',
                    name: 'RepeaterItem3',
                    address: 'address3'
                }, {
                    id: 'RepeaterItem4',
                    name: 'RepeaterItem4',
                    address: 'address4'
                }, {
                    id: 'RepeaterItem5',
                    name: '<PERSON>eaterItem5',
                    address: 'address5'
                }]
            }
        },
        beecraft() {
            return {
                $$data: {
                    isRepeater: true, //是否是中继器
                    template: {
                        name: '<PERSON><PERSON><PERSON>',
                        children: [{
                            name: 'RepeaterItem',
                            children: []
                        }]
                    },
                },
                rules: {
                    canDrag: () => false,
                    canHover: () => false,
                    canSelect: () => false,
                }
            }
        }
    }
</script>