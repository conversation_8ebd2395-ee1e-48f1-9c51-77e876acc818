<template>
    <fx-checkbox size="mini" :value="!!value" @change="handleChange">{{label}}
        <fx-tooltip class="description" effect="dark" :content="description" :disabled="!description" placement="top">
            <SvgIcon v-if="icon?.content" :size="20" :viewBox="'0 0 1024 1024'" :content="icon.content"> 
            </SvgIcon>
            <span v-else :class="`item-icon ${icon}`"></span>
        </fx-tooltip>
    </fx-checkbox>
</template>
<script lang="ts">
    export default {
        props: {
            value: [Boolean,Number],
            label: String,
            description: String,
            icon: [String,Object]
        },
        methods: {
            handleChange(val) {
                this.$emit('change', val);
            }
        }
    }
</script>
<style lang="less" scoped>
    .description.item-icon {
        font-size: 14px;
    }
</style>
