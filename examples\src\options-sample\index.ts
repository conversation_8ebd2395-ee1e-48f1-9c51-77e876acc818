import Vue from 'vue';
import Demo from './Demo';
import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'Demo',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }]
            }]
        },
        node: {
            data: {
                from: 'dashboard',
                fromId: ''
            },
            $$data: {
                style: {
                    'margin': '8px 0',
                    'padding': '16px',
                    'background-color': '#fff'
                }
            }
        },
        _isReactive: true
    }
);

