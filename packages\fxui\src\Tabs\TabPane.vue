
<script>
    import { Vue } from '@beecraft/shared';
    import { generateUuid } from '@beecraft/shared';

    const FxTabPane = {
        extends: Vue.options.components.FxTabPane,
        computed: {
            isClosable() {
                return this.closable || this.$parent.$parent.closable;
            },
            active() {
                let parent = this.$parent;
                
                // todo 未来有性能隐患
                while (parent) {
                    if (parent.$options.componentName !== 'FxTabs') {
                        parent = parent.$parent;
                    } else {
                        break;
                    }
                }
        
                const active = parent.currentName === (this.name || this.index);
                
                if (active) {
                    this.loaded = true;
                }
                
                return active;
            },
            paneName() {
                return this.name || this.index;
            }
        }
    }

    FxTabPane.beecraft = function(options) {
        return {
            name: 'FxTabPane',
            displayName: $t('beecraft.fxui.tabPane',{},'页签面板'),
            data: {
                label: $t('页签'),
                name: `tabItem${generateUuid()}`
            },
            $$data: {
                isCanvas: true,
                isDeletable: false,
                templateAncestors: ['FxTabs'],
                noWrapper: true, // 不需要包装器
            },
            rules: {
                canDrag: () => false,
                canSelect: () => false,
            },
            hooks: {
                beforeSelect(e, node, { query, actions }) {
                    const parentNode = query.node(node.parent).get();

                    if(parentNode.name === 'FxTabs') {
                        actions.setNodeEvent('selected', [parentNode.id]);
                    }
                }
            }
        }
    }

    export default FxTabPane;
</script>