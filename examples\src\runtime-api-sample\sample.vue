<template>
    <div></div>
</template>
<script lang="js">
import Demo from '../components/Demo.vue';
import { init } from '@beecraft/engine';
export default {
    mounted() {
        init(
            this.$el,
            {
                resolver: {
                    Demo
                },
                import: [{
                    name: 'FxTabs',
                    data: {
                        "value": "item1"
                    },
                    children: [{
                        name: 'FxTabPane',
                        data: {
                            "label": "标签1",
                            "name": "item1"
                        },
                        children: [{
                            name: 'Demo',
                        }]
                    }, {
                        name: 'FxTabPane',
                        data: {
                            "label": "标签2",
                            "name": "item2"
                        },
                    }]
                }],
                mode: 'preview'
            }
        )
    }
}
</script>