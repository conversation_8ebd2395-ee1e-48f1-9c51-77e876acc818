import Demo from './Demo';
import { init } from '@beecraft/engine';
import SetbarCustomSchema from './SetbarCustomSchema';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'Demo',
            children: [{
                name: 'Demo',
            }]
        }],
        workbench: {
            materials: [{
                "title": "示例组件",
                "children": [{
                    "title": "示例",
                    "name": "Demo"
                }]
            }],
            setbar: {
                related: {
                    customSchemaOptionsSettings: [{
                        name: 'Set<PERSON><PERSON><PERSON>',
                        data: {
                            label: '测试1',
                            display: 'block',
                        },
                        children: [
                            {
                                name: 'Set<PERSON><PERSON><PERSON>',
                                data: {
                                    label: '单行文本',
                                    display: 'block',
                                    setter: {
                                        component: 'StringSetter',
                                        ranges: ['options', 'text']
                                    }
                                }
                            }
                        ]
                    }],
                    customComponentOptionsSettings: {
                        render: h => h('div', 'customComponentOptions')
                    }
                },
                style: {
                    width: '700px'
                }
            },
            activityBar: {
                // show: false
            }
        },
        plugins: [
            SetbarCustomSchema
        ]
    }
)