import Demo from './Demo.vue';
import { init } from '@beecraft/engine';

init(
    document.getElementById('app-portal') as HTMLElement,
    {
        resolver: {
            Demo
        },
        import: [{
            name: 'Demo',
            children: [{
                name: 'Demo',
            },{
                name: 'SlideImage',
                data: {
                    autoplay: false,
                    items: [{
                        src: 'https://a9.fspage.com/FSR/frontend/html/paas/appcustomization/images/lunbo.291a831e.png',
                        actionType: ''
                    },{
                        src: 'https://a9.fspage.com/FSR/frontend/html/paas/appcustomization/images/lunbo.291a831e.png',
                        actionType: 3,
                        actionData: {
                            url: 'https://www.baidu.com'
                        }
                    },{}],
                    direction: 'horizontal',
                    type: 'normal'
                }
            }]
        }],
        mode: 'preview'
    }
)