<script>
    import { Vue } from '@beecraft/shared';

    const FxRow = {
        extends: Vue.options.components.FxRow
    }

    const template = {
        name: 'FxRow',
        data: {
            gutter: 6
        },
        children: [{
            name: 'FxCol',
            data: {
                "span": "12"
            }
        },{
            name: 'FxCol',
            data: {
                "span": "12"
            }
        }]
    };

    FxRow.beecraft = function() {
        return {
            name: 'FxRow',
            displayName: '行',
            $$data: {
                template,
                isCanvas: true
            },
            rules: {
                canMoveIn: (nodes) => (!nodes.find(node => node.name !== 'FxCol'))
            }
        }
    }

    export default FxRow;
</script>
<style lang="less">
    .bc-node.el-row {
        overflow: hidden;
        padding: 3px 0;
    }
</style>