// 找到id对应的项
export function getLayoutItem(layout, id) {
    for (let i = 0, len = layout.length; i < len; i++) {
        if (layout[i].i === id) return layout[i];
    }
}


export function cloneLayoutItem(layoutItem) {
    return {
        w: layoutItem.w,
        h: layoutItem.h,
        x: layoutItem.x,
        y: layoutItem.y,
        i: layoutItem.i,
        minW: layoutItem.minW,
        maxW: layoutItem.maxW,
        minH: layoutItem.minH,
        maxH: layoutItem.maxH,
        moved: <PERSON><PERSON><PERSON>(layoutItem.moved),
        static: Bo<PERSON>an(layoutItem.static),
        // These can be null/undefined
        isDraggable: layoutItem.isDraggable,
        isResizable: layoutItem.isResizable,
        resizeHandles: layoutItem.resizeHandles,
        isBounded: layoutItem.isBounded
    };
}


export function setTopLeft({ top, left, width, height }) {
    return {
        top: `${top}px`,
        left: `${left}px`,
        width: `${width}px`,
        height: `${height}px`,
        position: "absolute"
    };
}


export function setTransform({ top, left, width, height }) {
    const translate = `translate(${left}px,${top}px)`;
    return {
        transform: translate,
        WebkitTransform: translate,
        MozTransform: translate,
        msTransform: translate,
        OTransform: translate,
        width: `${width}px`,
        height: `${height}px`,
        position: "absolute"
    };
}


export function moveElement(
    layout,
    l,
    x,
    y,
    isUserAction,
    preventCollision,
    compactType,
    cols,
    allowOverlap?
) {
    // 纠正x,y避免超出

    if (l.static && l.isDraggable !== true) return layout;

    if (l.y === y && l.x === x) return layout;

    // console.log(
    //     `Moving element ${l.i} to [${String(x)},${String(y)}] from [${l.x},${l.y}]`
    // );

    const oldX = l.x;
    const oldY = l.y;

    if (typeof x === "number") l.x = x;
    if (typeof y === "number") l.y = y;
    l.moved = true;

    let sorted = sortLayoutItems(layout, compactType);
    const movingUp =
        compactType === "vertical" && typeof y === "number"
            ? oldY >= y
            : compactType === "horizontal" && typeof x === "number"
                ? oldX >= x
                : false;

    if (movingUp) sorted = sorted.reverse();
    const collisions = getAllCollisions(sorted, l);
    const hasCollisions = collisions.length > 0;

    if (hasCollisions && allowOverlap) {
        return cloneLayout(layout);
    } else if (hasCollisions && preventCollision) {
        // console.log(`Collision prevented on ${l.i}, reverting.`);
        l.x = oldX;
        l.y = oldY;
        l.moved = false;
        return layout;
    }

    for (let i = 0, len = collisions.length; i < len; i++) {
        const collision = collisions[i];
        // console.log(
        //     `Resolving collision between ${l.i} at [${l.x},${l.y}] and ${collision.i} at [${collision.x},${collision.y}]`
        // );

        if (collision.moved) continue;

        if (collision.static) {
            layout = moveElementAwayFromCollision(
                layout,
                collision,
                l,
                isUserAction,
                compactType,
                cols
            );
        } else {
            layout = moveElementAwayFromCollision(
                layout,
                l,
                collision,
                isUserAction,
                compactType,
                cols
            );
        }
    }

    return layout;
}


export function moveElementAwayFromCollision(
    layout,
    collidesWith,
    itemToMove,
    isUserAction,
    compactType,
    cols
) {
    const compactH = compactType === "horizontal";
    // Compact vertically if not set to horizontal
    const compactV = compactType === "vertical";
    const preventCollision = collidesWith.static;

    if (isUserAction) {
        isUserAction = false;
        const fakeItem = {
            x: compactH ? Math.max(collidesWith.x - itemToMove.w, 0) : itemToMove.x,
            y: compactV ? Math.max(collidesWith.y - itemToMove.h, 0) : itemToMove.y,
            w: itemToMove.w,
            h: itemToMove.h,
            i: "-1"
        };
        const firstCollision = getFirstCollision(layout, fakeItem);
        const collisionNorth =
            firstCollision && firstCollision.y + firstCollision.h > collidesWith.y;
        const collisionWest =
            firstCollision && collidesWith.x + collidesWith.w > firstCollision.x;

        if (!firstCollision) {
            // console.log(
            //     `Doing reverse collision on ${itemToMove.i} up to [${fakeItem.x},${fakeItem.y}].`
            // );
            return moveElement(
                layout,
                itemToMove,
                compactH ? fakeItem.x : undefined,
                compactV ? fakeItem.y : undefined,
                isUserAction,
                preventCollision,
                compactType,
                cols
            );
        } else if (collisionNorth && compactV) {
            return moveElement(
                layout,
                itemToMove,
                undefined,
                collidesWith.y + 1,
                isUserAction,
                preventCollision,
                compactType,
                cols
            );
        } else if (collisionNorth && compactType == null) {
            collidesWith.y = itemToMove.y;
            itemToMove.y = itemToMove.y + itemToMove.h;

            return layout;
        } else if (collisionWest && compactH) {
            return moveElement(
                layout,
                collidesWith,
                itemToMove.x,
                undefined,
                isUserAction,
                preventCollision,
                compactType,
                cols
            );
        }
    }

    const newX = compactH ? itemToMove.x + 1 : undefined;
    const newY = compactV ? itemToMove.y + 1 : undefined;

    if (newX == null && newY == null) {
        return layout;
    }
    return moveElement(
        layout,
        itemToMove,
        compactH ? itemToMove.x + 1 : undefined,
        compactV ? itemToMove.y + 1 : undefined,
        isUserAction,
        preventCollision,
        compactType,
        cols
    );
}


export function cloneLayout(layout) {
    const newLayout = Array(layout.length);
    for (let i = 0, len = layout.length; i < len; i++) {
        newLayout[i] = cloneLayoutItem(layout[i]);
    }
    return newLayout;
}


/**
 * Return the bottom coordinate of the layout.
 */
export function bottom(layout) {
    let max = 0,
        bottomY;
    for (let i = 0, len = layout.length; i < len; i++) {
        bottomY = layout[i].y + layout[i].h;
        if (bottomY > max) max = bottomY;
    }
    return max;
}


export function compactType(
    props
) {
    const { verticalCompact, compactType } = props || {};
    return verticalCompact === false ? null : compactType;
}


// 获取百分比
export function perc(num) {
    return num * 100 + "%";
}

// 获取固定的项
export function getStatics(layout) {
    return layout.filter(l => l.static);
}

// 
export function sortLayoutItems(
    layout,
    compactType
) {
    if (compactType === "horizontal") return sortLayoutItemsByColRow(layout);
    if (compactType === "vertical") return sortLayoutItemsByRowCol(layout);
    else return layout;
}

// 先数列再数行
export function sortLayoutItemsByColRow(layout) {
    return layout.slice(0).sort(function (a, b) {
        if (a.x > b.x || (a.x === b.x && a.y > b.y)) {
            return 1;
        }
        return -1;
    });
}

// 先数行再数列
export function sortLayoutItemsByRowCol(layout) {
    // Slice to clone array as sort modifies
    return layout.slice(0).sort(function (a, b) {
        if (a.y > b.y || (a.y === b.y && a.x > b.x)) {
            return 1;
        } else if (a.y === b.y && a.x === b.x) {
            // Without this, we can get different sort results in IE vs. Chrome/FF
            return 0;
        }
        return -1;
    });
}

// 查找最佳位置
export function findBestPos(colsNum = 12, layout: any[], w: number, h: number): { x: number, y: number } {
    const hPerX: any = new Array(colsNum);

    // 将布局转换为柱状图
    layout.forEach(item => {
        for (let i = item.x; i < item.x + item.w; i++) {
            hPerX[i] = Math.max(hPerX[i] || -Infinity, item.y + item.h);
        }
    });

    // 初始化最佳位置
    let bestX = 0;
    let bestY = Infinity;

    // 遍历每个 x 坐标，找到最佳的位置
    for (let i = 0; i <= hPerX.length - w; i++) {
        // 找到当前 x 坐标到 x + w 坐标范围内的最大高度
        let maxH = hPerX[i] || 0;
        for (let j = i; j < i + w; j++) {
            maxH = Math.max(maxH, hPerX[j] || 0);
        }

        // 如果当前位置可以容纳新元素，则更新最佳位置
        if (maxH < bestY) {
            bestX = i;
            bestY = maxH;
        }
    }

    // 返回最佳位置的横坐标和纵坐标
    return { x: bestX, y: bestY };
}

/**
 * Given a layout, compact it. This involves going down each y coordinate and removing gaps
 * between items.
 *
 * Does not modify layout items (clones). Creates a new layout array.
 * 
 * Test：
 *  ......
 */
export function compact(
    layout,
    compactType,
    cols,
    allowOverlap,
    maxRowsPerPage = Infinity,
    isAdaptive?
) {
    const compareWith = getStatics(layout);
    const sorted = sortLayoutItems(layout, compactType);
    const out = Array(layout.length);

    for (let i = 0, len = sorted.length; i < len; i++) {
        let l = cloneLayoutItem(sorted[i]);

        // Don't move static elements
        if (!l.static) {
            l = compactItem(compareWith, l, compactType, cols, sorted, allowOverlap, maxRowsPerPage, isAdaptive);

            // Add to comparison array. We only collide with items before this one.
            // Statics are already in this array.
            compareWith.push(l);
        }

        // Add to output array to make sure they still come out in the right order.
        out[layout.indexOf(sorted[i])] = l;

        // Clear moved flag, if it exists.
        l.moved = false;
    }

    return out;
}


/**
 * Before moving item down,
 * it will check if the movement will cause collisions and move those items down before.
 */
var heightWidth = { x: "w", y: "h" };
function resolveCompactionCollision(
    layout,
    item,
    moveToCoord,
    axis
) {
    const sizeProp = heightWidth[axis];
    item[axis] += 1;
    const itemIndex = layout
        .map(layoutItem => {
            return layoutItem.i;
        })
        .indexOf(item.i);

    // Go through each item we collide with.
    for (let i = itemIndex + 1; i < layout.length; i++) {
        const otherItem = layout[i];
        // Ignore static items
        if (otherItem.static) continue;

        // Optimization: we can break early if we know we're past this el
        // We can do this b/c it's a sorted layout
        if (otherItem.y > item.y + item.h) break;

        if (collides(item, otherItem)) {
            resolveCompactionCollision(
                layout,
                otherItem,
                moveToCoord + item[sizeProp],
                axis
            );
        }
    }

    item[axis] = moveToCoord;
}

/**
 * Compact an item in the layout.
 *
 * Modifies item.
 *
 */
export function compactItem(
    compareWith,
    l,
    compactType,
    cols,
    fullLayout,
    allowOverlap,
    maxRowsPerPage?,
    isAdaptive?
) {
    const compactV = compactType === "vertical";
    const compactH = compactType === "horizontal";

    // 挤空白
    if (compactV) {
        // Math.floor(l.y / 5)
        // Bottom 'y' possible is the bottom of the layout.
        // This allows you to do nice stuff like specify {y: Infinity}
        // This is here because the layout must be sorted in order to get the correct bottom `y`.
        l.y = Math.min(bottom(compareWith), l.y);
        // Move the element up as far as it can go without colliding.
        while (l.y > 0 && !getFirstCollision(compareWith, l)) {
            l.y--;
        }
    } else if (compactH) {
        // Move the element left as far as it can go without colliding.
        while (l.x > 0 && !getFirstCollision(compareWith, l)) {
            l.x--;
        }
    }

    // Move it down, and keep moving it down if it's colliding.
    let collides;
    // Checking the compactType null value to avoid breaking the layout when overlapping is allowed.
    while (
        (collides = getFirstCollision(compareWith, l)) &&
        !(compactType === null && allowOverlap)
    ) {
        if (compactH) {
            resolveCompactionCollision(fullLayout, l, collides.x + collides.w, "x");
        } else {
            resolveCompactionCollision(fullLayout, l, collides.y + collides.h, "y");
        }
        // Since we can't grow without bounds horizontally, if we've overflown, let's move it down and try again.
        if (compactH && l.x + l.w > cols) {
            l.x = cols - l.w;
            l.y++;
            // ALso move element as left as we can
            while (l.x > 0 && !getFirstCollision(compareWith, l)) {
                l.x--;
            }
        }
    }

    // Ensure that there are no negative positions
    l.y = Math.max(l.y, 0);
    l.x = Math.max(l.x, 0);

    resolveItemOnPage(l, maxRowsPerPage, isAdaptive);

    return l;
}

/**
 * @desc 按照页面进行坐标分配
 * @param l 
 * @param maxRowsPerPage
 */
function resolveItemOnPage(l, maxRowsPerPage = Infinity, isAdaptive?) {
    const currentPageIndex = Math.floor(l.y / maxRowsPerPage);
    const bottomPageIndex = Math.floor((l.y + l.h - 1) / maxRowsPerPage);

    if (bottomPageIndex > currentPageIndex) {
        const nextPageStartY = maxRowsPerPage * bottomPageIndex;
        const remainSlots = nextPageStartY - l.y;
        if (isAdaptive && remainSlots / l.h > 0.7) {
            l.oh = l.h;
            l.h = remainSlots;
        } else {
            l.y = nextPageStartY;
            if (l.h > maxRowsPerPage) {
                if(isAdaptive) {
                    l.oh = l.h;
                }
                l.h = maxRowsPerPage;
            }
        }
    }
}


// 获取布局中第一个碰撞的元素
export function getFirstCollision(
    layout,
    layoutItem
) {
    for (let i = 0, len = layout.length; i < len; i++) {
        if (collides(layout[i], layoutItem)) return layout[i];
    }
}


// 获取布局中所有碰撞的元素
export function getAllCollisions(
    layout,
    layoutItem
) {
    return layout.filter(l => collides(l, layoutItem));
}


/**
 * Given two layoutitems, check if they collide.
 */
export function collides(l1, l2) {
    if (l1.i === l2.i) return false; // same element
    if (l1.x + l1.w <= l2.x) return false; // l1 is left of l2
    if (l1.x >= l2.x + l2.w) return false; // l1 is right of l2
    if (l1.y + l1.h <= l2.y) return false; // l1 is above l2
    if (l1.y >= l2.y + l2.h) return false; // l1 is below l2
    return true; // boxes overlap
}

const constrainLeft = (left: number) => Math.max(0, left);

const constrainTop = (top: number) => Math.max(0, top);

const constrainWidth = (
    left: number,
    currentWidth: number,
    newWidth: number,
    containerWidth: number
) => {
    return left + newWidth > containerWidth ? currentWidth : newWidth;
};

const constrainHeight = (
    top: number,
    currentHeight: number,
    newHeight: number
) => {
    return top < 0 ? currentHeight : newHeight;
};

const resizeNorth = (currentSize, { left, height, width }, _containerWidth) => {
    const top = currentSize.top - (height - currentSize.height);

    return {
        left,
        width,
        height: constrainHeight(top, currentSize.height, height),
        top: constrainTop(top)
    };
};

const resizeEast = (
    currentSize,
    { top, left, height, width },
    containerWidth
) => ({
    top,
    height,
    width: constrainWidth(
        currentSize.left,
        currentSize.width,
        width,
        containerWidth
    ),
    left: constrainLeft(left)
});

const resizeWest = (currentSize, { top, height, width }, containerWidth) => {
    const left = currentSize.left - (width - currentSize.width);

    return {
        height,
        width:
            left < 0
                ? currentSize.width
                : constrainWidth(
                    currentSize.left,
                    currentSize.width,
                    width,
                    containerWidth
                ),
        top: constrainTop(top),
        left: constrainLeft(left)
    };
};

const resizeSouth = (
    currentSize,
    { top, left, height, width },
    containerWidth
) => ({
    width,
    left,
    height: constrainHeight(top, currentSize.height, height),
    top: constrainTop(top)
});

const resizeNorthEast = (...args) =>
    resizeNorth(args[0], resizeEast(args[0], args[1], args[2]), args[2]);
const resizeNorthWest = (...args) =>
    resizeNorth(args[0], resizeWest(args[0], args[1], args[2]), args[2]);
const resizeSouthEast = (...args) =>
    resizeSouth(args[0], resizeEast(args[0], args[1], args[2]), args[2]);
const resizeSouthWest = (...args) =>
    resizeSouth(args[0], resizeWest(args[0], args[1], args[2]), args[2]);


const ordinalResizeHandlerMap = {
    // n: resizeNorth,
    // ne: resizeNorthEast,
    // e: resizeEast,
    // se: resizeSouthEast,
    // s: resizeSouth,
    // sw: resizeSouthWest,
    // w: resizeWest,
    // nw: resizeNorthWest
    mr: resizeEast,
    ml: resizeWest,
    tm: resizeNorth,
    bm: resizeSouth,
    tr: resizeNorthEast,
    tl: resizeNorthWest,
    br: resizeSouthEast,
    bl: resizeSouthWest
};



export function resizeItemInDirection(
    direction,
    currentSize,
    newSize,
    containerWidth
) {
    const ordinalHandler = ordinalResizeHandlerMap[direction];
    // Shouldn't be possible given types; that said, don't fail hard
    if (!ordinalHandler) return newSize;
    return ordinalHandler(
        currentSize,
        { ...currentSize, ...newSize },
        containerWidth
    );
}

export function modifyLayout(layout, layoutItem) {
    const newLayout = Array(layout.length);
    for (let i = 0, len = layout.length; i < len; i++) {
        if (layoutItem.i === layout[i].i) {
            newLayout[i] = layoutItem;
        } else {
            newLayout[i] = layout[i];
        }
    }
    return newLayout;
}


export function withLayoutItem(
    layout,
    itemKey,
    cb
) {
    let item = getLayoutItem(layout, itemKey);
    if (!item) return [layout, null];
    item = cb(cloneLayoutItem(item)); // defensive clone then modify
    // FIXME could do this faster if we already knew the index
    layout = modifyLayout(layout, item);
    return [layout, item];
}


