<template>
    <fx-select width="100%" :placeholder="$t('请选择')" size="mini" v-bind="{...$attrs, ...$props}" v-on="$listeners">
        <fx-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
        </fx-option>
    </fx-select>
</template>
<script>
    export default {
        name: 'SelectSetter',

        props: {
            options: {
                type: Array
            }
        }
    }
</script>
<style lang="less" scoped>
    
</style>