<script>
    import { Vue } from '@beecraft/shared';

    const template = {
        name: 'FxCollapse',
        data: {
            value: 'item1'
        },
        children: [{
            name: 'FxCollapseItem',
            data: {
                "title": $t('beecraft.widget.CollapseItem1', {}, '折叠面板1'),
                "name": "item1"
            }
        },{
            name: 'FxCollapseItem',
            data: {
                "title": $t('beecraft.widget.CollapseItem2', {}, '折叠面板2'),
                "name": "item2"
            }
        }]
    };

    const FxCollapse = {
        extends: Vue.options.components.FxCollapse,
        beecraft: function() {
            return {
                name: 'FxCollapse',
                displayName: $t('beecraft.widget.Collapse', {}, '折叠容器'),
                $$data: {
                    template,
                    noWrapper: true,
                },
                rules: {
                    canMoveIn: (nodes) => (!nodes.find(node => node.name !== 'FxCollapseItem'))
                },
                related: {
                    attributeSettings: [{
                        name: '<PERSON><PERSON><PERSON><PERSON>',
                        data: {
                            label: $t('基本信息'),
                            display: 'block',
                        }
                    },{
                        name: 'Setter<PERSON>ield',
                        data: {
                            label: $t('beecraft.widget.foldingdevice', {}, '折叠项'),
                            display: 'block',
                            setter: {
                                component: 'ArraySetter',
                                data: {
                                    ranges: ['data', 'title'],
                                    enabledI18N: true
                                }
                            }
                        }
                    },{
                        name: 'SetterField',
                        data: {
                            label: $t('beecraft.widget.collapsemode', {}, '手风琴模式'),
                            display: 'inline',
                            setter: {
                                component: 'SwitchSetter',
                                ranges: ['data', 'accordion'],
                            }
                        }
                    }]
                }
            }
        },
        mounted() {
            const childNames = this.$children?.reduce((items, instance) => {
                while (instance) {
                    const vnode = instance.$vnode;

                    if (vnode?.componentOptions?.Ctor?.options?.name === 'FxCollapseItem') {
                        items.push(vnode.componentOptions.propsData?.name || instance.name);
                        break;
                    }

                    instance = instance.$children[0]; // 此处完全利用了Node渲染的结构，如果结构变更，此处也需要修改
                }
                return items;
            }, []);
            if (childNames && childNames[0] && this.value !== childNames[0]) {
                this.value = childNames[0];
            }
        },
    }

    export default FxCollapse;
</script>
<style lang="less">
    // .bc-node.el-collapse-item {
    //     .el-collapse-item__content {
    //         padding: 15px!important;
    //     }
    // }
</style>