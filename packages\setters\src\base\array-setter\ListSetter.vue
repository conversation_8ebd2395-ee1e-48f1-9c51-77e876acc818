<template>
    <div class="bc-setter-list">
        <div class="bc-setter-list-columns">
            <fx-draggable :list="cList" animation="200" handle=".bc-listitem-action-drag" @change="handleDragChange">
                <template v-if="cList?.length">
                    <ArrayItem v-for="item in cList" :key="item.id" v-bind="item" @delete="handleDelete(item.id)"
                        @change="handleValueChange(item.id, $event, arguments[1])" :enabledI18N="enabledI18N"></ArrayItem>
                </template>
                <div v-else class="no-data d-flex align-items-center">
                    <span class="icon fx-icon-warning"></span>
                    <span>暂时还没有添加内容</span>
                </div>
            </fx-draggable>
        </div>
        <div class="bc-setter-list-add">
            <a class="cursor-pointer" @click="handleClick">{{ $t('beecraft.setters.addAnItem', {}, '添加一项')}}+</a>
        </div>
    </div>
</template>
<script>
import ArrayItem from './ArrayItem.vue';
import { getRangesFromTarget, setRangesToTarget } from '../../shared/formatRanges';

export default {

    inject: ['useInternalEditor', 'useInternalNode'],

    components: {
        ArrayItem
    },

    props: {
        value: {
            type: Object
        },
        ranges: {
            type: Array
        },
        // todo
        enabledI18N: {
            type: Boolean
        }
    },

    computed: {
        cList() {
            // todo value 未处理
            const { ranges, value } = this;

            const { query } = this.useInternalEditor();
            const { list } = this.useInternalNode(node => {
                return {
                    list: node.children.map(child => {
                        const childNode = query.node(child).get();

                        return {
                            id: childNode.id,
                            value: getRangesFromTarget(childNode, ranges),
                            i18nMap: getRangesFromTarget(childNode, [ ranges[0], 'i18nMap', ...ranges.slice(1)]), // todo
                        }
                    })
                }
            });

            return list;
        }
    },

    methods: {
        handleClick() {
            const { actions } = this.useInternalEditor();
            const { template, id } = this.useInternalNode(node => {
                return {
                    id: node.id,
                    template: node.$$data.template
                }
            });

            if (template) {
                const childTemplate = template.children[0];

                const nodeTree = actions.parseSerializedNodes([{
                    name: childTemplate.name
                }])[0];

                actions.addNodeTree(nodeTree, id);
            }
        },

        handleDelete(id) {
            if (this.cList.length === 1) {
                this.$message({
                    message: $t('至少保留一项'),
                    type: 'error'
                })
                return;
            }
            const { actions, query } = this.useInternalEditor();

            const children = query.node(id).get().children;

            if (children.length > 0) {
                this.$confirm($t('beecraft.setters.canvas-item-delete', '删除后，将同步删除已配置的组件')).then(() => {
                    actions.delete(id);
                });
                return;
            }

            actions.delete(id);
        },

        // todo
        handleValueChange(id, value, i18nValue) {
            const dataRanges = this.ranges.slice(1);
            const { actions } = this.useInternalEditor();

            actions.setCustom(id, (data) => {
                setRangesToTarget(data, dataRanges, value);
                setRangesToTarget(data, [ 'i18nMap', ...dataRanges], i18nValue);
            });
        },

        handleDragChange({ moved }) {
            const { actions } = this.useInternalEditor();
            const { id } = this.useInternalNode();
            const { element, newIndex, oldIndex } = moved;

            actions.move(element.id, id, newIndex > oldIndex ? newIndex + 1 : newIndex, true);
        }

    }

}
</script>
<style lang="less" scoped>
.no-data {
    height: 42px;
    background-color: var(--color-primary01);
    padding: 12px;
    box-sizing: border-box;

    .icon {
        margin-right: 6px;
    }

    .icon:before {
        color: var(--color-primary05);
    }
}

.bc-setter-list-add * {
    font-size: 12px !important;
}

.btn-add {
    padding: 0;
    color: var(--color-info06, #0c6cff);
}
</style>