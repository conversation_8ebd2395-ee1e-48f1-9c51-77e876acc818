<script>
import GridItem from './GridItem.vue';
import <PERSON><PERSON><PERSON> from './Lattice.vue';
import {
    bottom,
    getLayoutItem,
    moveElement,
    compact,
    compactType,
    cloneLayoutItem,
    withLayoutItem,
    getAllCollisions,
    findBestPos
} from './utils';
import { calcXY, calcGridItemWHPx, calcGridColWidth } from './calculateUtils';
import Vue from 'vue';

const deepEqual = _.isEqual;
const layoutClassName = "beecraft-grid-layout";
const defaultDragPos = { "w": 6, "h": 6, "i": null };
let isFirefox = false;
try {
    isFirefox = /firefox/i.test(navigator.userAgent);
} catch (e) {
    /* Ignore */
}

// 判断数组是否相同
function arraysAreEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
        return false;
    }

    const sortedArr1 = arr1.slice().sort();
    const sortedArr2 = arr2.slice().sort();

    for (let i = 0; i < sortedArr1.length; i++) {
        if (sortedArr1[i] !== sortedArr2[i]) {
            return false;
        }
    }

    return true;
}

export default {
    props: {
        className: {
            type: String,
            default: ''
        },
        colNum: {
            type: Number,
            default: 12
        },
        rowNum: {
            type: Number
        },
        rowHeight: {
            type: [Number, Function],
            default: 20
        },
        margin: {
            type: Array,
            default: [10, 10]
        },
        maxRows: {
            type: Number,
            default: Infinity
        },
        containerPadding: {
            type: Array
        },
        isDraggable: {
            type: Boolean,
            default: true
        },
        isDroppable: {
            type: Boolean,
            default: false
        },
        layout: {
            type: Array,
            default: null
        },
        compactType: {
            type: String,
            default: 'vertical'
        },
        verticalCompact: {
            type: Boolean,
            default: true
        },
        autoSize: {
            type: Boolean,
            default: true
        },

        droppingItem: {
            type: Object,
            default: {
                i: "__dropping-elem__",
                h: 1,
                w: 1
            }
        },

        // ??? 没东啥意思
        allowOverlap: {
            type: Boolean,
            default: false
        },

        transformScale: {
            type: Number,
            default: 1
        },

        handleResizeStart: {
            type: Function
        },

        handleDragStart: {
            type: Function
        },

        handleDrag: {
            type: Function
        },

        handleDragEnter: {
            type: Function
        },

        handleDragStop: {
            type: Function
        },

        handleLayoutChange: {
            type: Function
        },

        handleDragOver: {
            type: Function
        },

        handleDrop: {
            type: Function
        },

        handleResizeStart: {
            type: Function
        },

        handleResize: {
            type: Function
        },

        handleResizeStop: {
            type: Function
        },

        useCSSTransforms: {
            type: Boolean,
            default: true
        },

        isBounded: {
            type: Boolean,
            default: false
        },

        isResizable: {
            type: Boolean,
            default: true
        },

        // 是否开启网格
        isEnaleLattice: {
            type: Boolean,
            default: true
        },

        preventCollision: {
            type: Boolean,
            default: false
        },

        width: {
            type: Number
        },

        gridRectStroke: {
            type: String
        },

        children: {
            type: Array
        },

        enabled: {
            type: Boolean,
            default: true
        },

        mode: {
            type: String,
            default: 'carousel'
        },

        maxRowsPerPage: {
            type: Number,
            default: Infinity
        },

        autoplay: {
            type: Boolean,
            default: true
        },

        interval: {
            type: Number,
            default: 3000
        },

        direction: {
            type: String
        },

        // 页脚提示文本
        pageFooterText: {
            type: String,
            default: '页脚提示文本'
        },

        // 页脚背景色
        pageFooterBackgroundColor: {
            type: String,
            default: '#fff'
        },

        // 页脚文字颜色
        pageFooterFontColor: {
            type: String,
            default: '#ff8000'
        },

        pageFooterHeight: {
            type: Number,
            default: 32
        },

        controlBackgroundColor: {
            type: String,
            default: 'rgba(0, 0, 0, 0.48)'
        },

        controlFontColor: {
            type: String,
            default: '#fff'
        },

        enableDoubleTapControl: {
            type: Boolean,
            default: false
        }
    },
    components: {
        GridItem,
        Lattice
    },
    provide() {
        return {
            layout: this
        }
    },
    computed: {
        cRowHeight() {
            const {
                rowHeight,
                margin,
                containerWidth
            } = this;

            if (typeof rowHeight === 'function') {
                return rowHeight({
                    margin,
                    containerWidth: containerWidth,
                    viewportHeight: this.viewportHeight - (this.mode === 'carousel' && this.cEnabled ? this.pageFooterHeight : 0),
                    colWidth: calcGridColWidth({
                        margin,
                        containerPadding: this.containerPadding || margin,
                        containerWidth,
                        cols: this.colNum
                    })
                });
            }

            return rowHeight;
        },

        cEnabled() {
            return this.enabled;
        },

        cMaxRowsPerPage() {
            return this.mode === 'carousel' ? this.maxRowsPerPage : Infinity;
        },

    },
    render(createElement) {
        const {
            isDroppable,
            noop,
            activeDrag,
            layoutData,
            processGridItem,
            droppingDOMNode,
            droppingItem,
            mode,
            cEnabled: enabled
        } = this;
        const containerHeight = this.containerHeight();
        let layout = layoutData;

        if (droppingDOMNode) {
            layout = layout.filter(item => item.i !== droppingItem.i);
        }

        // 需要优化
        if (!enabled && mode === 'carousel') {
            const { cMaxRowsPerPage: maxRowsPerPage } = this;
            const group = layout.reduce((memo, item) => {
                const pageIndex = Math.floor(item.y / maxRowsPerPage);
                if (!memo[pageIndex]) {
                    memo[pageIndex] = [];
                }
                memo[pageIndex].push(processGridItem(createElement, { ...item, y: item.y % maxRowsPerPage }, this.$slots.default?.find(slot => slot.key === item.i || slot.asyncMeta?.data.key === item.i)))
                return memo;
            }, []);

            return createElement('div', {
                class: 'grid-layout',
                on: {
                    'mousemove': this.startDelayDisplay
                }
            }, [
                this.dAutoplay
                    ? createElement('div', {
                        class: 'grid-layout-mask'
                    })
                    : '',
                createElement('fx-carousel', {
                    ref: 'carousel',
                    props: {
                        height: `${this.viewportHeight}px`,
                        trigger: 'click',
                        autoplay: this.dAutoplay,
                        interval: this.interval,
                        direction: this.direction
                    }
                },
                    group.map((items, index) => {
                        return createElement('fx-carousel-item', {
                            key: index
                        }, items)
                    })
                ),
                createElement('div', {
                    class: `button-group ${this.controlDisplay ? 'button-group--visible' : ''}`,
                    style: {
                        'background-color': this.controlBackgroundColor
                    }
                }, [
                    createElement('div', {
                        class: 'button fx-icon-backoff',
                        ref: 'prevBtn',
                        on: {
                            click: () => {
                                this.$refs.carousel?.prev();
                            }
                        }
                    }),
                    createElement('div', {
                        class: `button ${this.dAutoplay ? "fx-icon-suspend" : 'fx-icon-play'}`,
                        ref: 'playBtn',
                        on: {
                            click: () => {
                                this.dAutoplay = !this.dAutoplay;
                            }
                        }
                    }),
                    createElement('div', {
                        class: 'button fx-icon-forward',
                        ref: 'nextBtn',
                        on: {
                            click: () => {
                                this.$refs.carousel?.next();
                            }
                        }
                    })
                ])
            ]);
        }

        const items = layout.map(item => {
            return processGridItem(createElement, item, this.$slots.default?.find(slot => slot.key === item.i || slot.asyncMeta?.data.key === item.i))
        });

        const placeholder = activeDrag && this.showActiveDrag ? createElement('GridItem', {
            class: {
                'grid-placeholder': true,
                'placeholder-resizing': this.resizing
            },
            props: {
                i: 'placeholder',
                w: activeDrag.w,
                h: activeDrag.h,
                x: activeDrag.x,
                y: activeDrag.y,
                draggable: false,
                resizable: false,
                isBounded: false,
            }
        }, [createElement('div')]) : '';

        const lattice = enabled && this.isEnaleLattice ? createElement('Lattice', {
            props: {
                containerWidth: +this.containerWidth,
                containerHeight,
                viewportHeight: this.viewportHeight,
                colNum: this.colNum,
                rowHeight: this.cRowHeight,
                margin: this.margin,
                gridRectStroke: this.gridRectStroke
            }
        }) : '';

        return this.containerWidth ? createElement('div', {
            class: 'grid-layout',
            style: {
                height: `${containerHeight}px`,
                ...this.style,
                draggable: enabled,
                resizable: enabled,
            },
            on: {
                dragenter: isDroppable ? this.onDragEnter : noop,
                dragleave: isDroppable ? this.onDragLeave : noop,
                dragover: isDroppable ? this.onDragOver : noop,
                drop: isDroppable ? this.onDrop : noop
            }
        }, [
            ...items,
            placeholder,
            this.isDroppable && droppingDOMNode ? processGridItem(createElement, layoutData.find(item => item.i === droppingItem.i), '', true) : '',
            lattice
        ]) : ''
    },
    data() {
        let layout = this.layout;

        return {
            resizing: false,
            activeDrag: null,
            showActiveDrag: false,
            layoutData: compact(layout || [], compactType(this), this.colNum, false, this.cMaxRowsPerPage),
            droppingDOMNode: null,
            containerWidth: this.width, // Grid自身的宽度
            viewportHeight: undefined, // Grid父元素的高度
            dAutoplay: this.autoplay,
            controlDisplay: false
        }
    },
    created() {
        this.oldDragItem = null;
        this.oldLayoutData = null;
        this.dragEnterCounter = 0;
        this.isFirstUpdateContainerSize = false;
        this.noop = function () { };

        this.oldResizeItem = null;
        this.children = [];

        this.resizeObserver = null;
        this.copyDropItem = null;
        // this.observeChildren();
    },
    watch: {
        layout: {
            handler(val) {
                this.layoutData = compact(val, compactType(this), this.colNum, false, this.cMaxRowsPerPage);
            },
            immediate: true
        },
        enabled(val) {
            // this.isFirstUpdateContainerSize = false;
            this.bindControlEvents();
            this.startDelayDisplay();
            this.dAutoplay = this.autoplay;
        },
        mode() {
            // this.isFirstUpdateContainerSize = false;
            this.bindControlEvents();
            this.dAutoplay = this.autoplay;
        }
    },
    mounted() {
        // todo 没有计算父容器的margin、padding
        // this.onLayoutMaybeChanged(this.layoutData, this.layout);
        this.observe(this.$el.parentElement);
        this.isMounted = true;
        this.bindControlEvents();
        this.startDelayDisplay();
        this.$nextTick(() => {
            this.$refs.prevBtn?.style.setProperty('--before-color', this.controlFontColor);
            this.$refs.nextBtn?.style.setProperty('--before-color', this.controlFontColor);
            this.$refs.playBtn?.style.setProperty('--before-color', this.controlFontColor);
        });
    },
    methods: {
        bindControlEvents() {
            document.removeEventListener('keydown', this.onKeyDown);
            if (!this.enabled && this.mode === 'carousel') {
                document.addEventListener('keydown', this.onKeyDown);
            }

            if (this.enableDoubleTapControl) {
                this.$el.addEventListener('dblclick', this.onDBLClick);
            }
        },

        onKeyDown(e) {
            const { direction } = this;
            if (direction === 'vertical' ? e.keyCode === 38 : e.keyCode === 37) {
                this.$refs.carousel?.prev();
                this.startDelayDisplay();
            } else if (direction === 'vertical' ? e.keyCode === 40 : e.keyCode === 39) {
                this.$refs.carousel?.next();
                this.startDelayDisplay();
            } else if (e.keyCode === 32) {
                this.dAutoplay = !this.dAutoplay;
                this.startDelayDisplay();
            }
            e.preventDefault();
        },

        onDBLClick() {
            this.dAutoplay = !this.dAutoplay;
            this.startDelayDisplay();
        },

        startDelayDisplay() {
            if (this.enabled) {
                return;
            }
            this.controlDisplay = true;
            if (this.delayDisplay) {
                clearTimeout(this.delayDisplay);
            }
            this.delayDisplay = setTimeout(() => {
                this.controlDisplay = false;
            }, 3000);
        },

        shouldLayoutUpdate() {
            return (
                this.layoutData !== this.oldLayoutData ||
                this.activeDrag !== this.oldActiveDrag
            )
        },

        processGridItem(createElement, item, child, isDroppingItem) {
            if (!item.i) {
                return;
            }
            const draggable =
                typeof item.isDraggable === "boolean"
                    ? item.isDraggable
                    : !item.static && this.cEnabled;
            const resizable =
                typeof item.isResizable === "boolean"
                    ? item.isResizable
                    : !item.static && this.cEnabled;
            return createElement('GridItem', {
                props: {
                    ...item,
                    isDroppingItem,
                    draggable,
                    resizable
                    // // 设置此项避免GridItem中的其他孙节点拖拽被GridLayout拦截
                    // dragIgnoreFrom: '.vue-grid-layout .bc-node .bc-node'
                },
                ref: isDroppingItem ? 'droppingItem' : 'griditem',
                key: item.i
            }, [
                child
            ])
        },

        observe(elem) {
            this.$nextTick(() => {
                const resizeObserver = this.resizeObserver = new ResizeObserver(entries => {
                    for (const entry of entries) {
                        const width = entry.contentRect.width;
                        const height = entry.contentRect.height;
                        if (width !== this.containerWidth || height !== this.viewportHeight) {
                            // 初始的宽度可能会有变动，所以需要等待稳定后再进行赋值
                            if (!this.isFirstUpdateContainerSize) {
                                if (this.delay) {
                                    clearTimeout(this.delay);
                                    this.delay = null;
                                }
                                this.delay = setTimeout(() => {
                                    this.containerWidth = width;
                                    this.viewportHeight = height;
                                    this.isFirstUpdateContainerSize = true;
                                }, 200);
                            } else {
                                this.containerWidth = width;
                                this.viewportHeight = height;
                            }
                            setTimeout(() => {
                                document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_WIDTH_UPDATE', {
                                    bubbles: true,
                                    cancelable: true,
                                    detail: {
                                        id: this._uid,
                                        width,
                                        height
                                    }
                                }));
                            }, 250)
                        }
                    }
                });
                resizeObserver.observe(elem);
            })
        },

        onDragStart(i, x, y, options) {
            const { layoutData: layout, handleDragStart } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            // Create placeholder (display only)
            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };

            this.oldDragItem = cloneLayoutItem(l);
            this.oldLayoutData = layout;
            this.activeDrag = placeholder;

            return handleDragStart?.(layout, l, l);
        },

        /**
         * 处理拖拽动作
         * @param {*} i - 拖拽目标nodeId
         * @param {*} x - 栅格布局横坐标
         * @param {*} y - 栅格布局纵坐标
         */
        onDrag(i, x, y) {
            const { oldDragItem, allowOverlap, preventCollision, colNum, handleDrag, cMaxRowsPerPage: maxRowsPerPage } = this;
            let { layoutData: layout } = this;

            const l = getLayoutItem(layout, i);
            if (!l) return;

            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };

            // Move the element to the dragged location.
            const isUserAction = true;
            layout = moveElement(
                layout,
                l,
                x,
                y,
                isUserAction,
                preventCollision,
                compactType(this),
                colNum,
                allowOverlap
            );

            const onDragResult = handleDrag?.(layout, oldDragItem, l, placeholder); // TODO 和原来不一样

            if (onDragResult === false) {
                return;
            }

            this.layoutData = allowOverlap ? layout : compact(layout, compactType(this), colNum, false, maxRowsPerPage);
            this.activeDrag = placeholder;
            this.showActiveDrag = true;
        },

        onDragStop(i, x, y, options) {
            if (!this.activeDrag) return;
            let { layoutData: layout } = this;
            const { oldDragItem, colNum, preventCollision, allowOverlap, handleDragStop } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            // Move the element here
            const isUserAction = true;
            layout = moveElement(
                layout,
                l,
                x,
                y,
                isUserAction,
                preventCollision,
                compactType(this),
                colNum,
                allowOverlap
            );

            const newLayout = allowOverlap ? layout : compact(layout, compactType(this), colNum, false, this.cMaxRowsPerPage);

            handleDragStop?.(newLayout, oldDragItem, l, null);

            const { oldLayoutData: oldLayout } = this;
            this.activeDrag = null;
            this.oldDragItem = null;
            this.oldLayoutData = null;
            this.showActiveDrag = false;

            this.onLayoutMaybeChanged(newLayout, this.layout);
        },

        onLayoutMaybeChanged(newLayout, oldLayout) {
            if (!oldLayout) oldLayout = this.layoutData;

            this.handleLayoutChange?.(newLayout, oldLayout);
        },

        onDragEnter(e) {
            e.preventDefault();
            e.stopPropagation();
            this.dragEnterCounter++;
            this.handleDragEnter?.(e);
            if (e.toElement) { }
            console.log('onDragEnter', e);
        },

        onDrop(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            const { droppingItem, droppingDOMNode } = this;
            const { layoutData: layout } = this;
            const item = layout.find(l => l.i === droppingItem.i);

            // reset dragEnter counter on drop
            this.dragEnterCounter = 0;
            this.removeDroppingPlaceholder();
            this.handleDrop?.(layout, item, e);
        },

        onDragLeave(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            this.dragEnterCounter--;
            console.log('onDragLeave', this.dragEnterCounter);

            if (this.dragEnterCounter === 0) {
                this.removeDroppingPlaceholder();
            }
        },

        onDragOver(e) {
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();

            if (
                isFirefox &&
                // $FlowIgnore can't figure this out
                !e.nativeEvent.target?.classList.contains(layoutClassName)
            ) {
                return false;
            }

            const {
                droppingItem,
                handleDragOver,
                margin,
                colNum,
                cRowHeight: rowHeight,
                maxRows,
                containerWidth: width,
                containerPadding,
                transformScale
            } = this;

            const onDragOverResult = handleDragOver?.(e);
            if (onDragOverResult === false) {
                if (this.droppingDOMNode) {
                    this.removeDroppingPlaceholder();
                }
                return false;
            }

            const finalDroppingItem = { ...droppingItem, ...onDragOverResult };

            let { layoutData: layout } = this;
            const gridRect = e.currentTarget.getBoundingClientRect();
            const layerX = e.clientX - gridRect.left;
            const layerY = e.clientY - gridRect.top;
            const droppingPosition = {
                left: layerX / transformScale,
                top: layerY / transformScale,
                e
            };

            if (!this.droppingDOMNode) {
                // let finalDroppingItem = {
                //     ...droppingItem
                // }

                // const onDragOverResult = handleDragOver?.(e);
                // if (onDragOverResult === false) {
                //     if (this.droppingDOMNode) {
                //         this.removeDroppingPlaceholder();
                //     }
                //     return false;
                // }

                // finalDroppingItem = Object.assign(finalDroppingItem, onDragOverResult)

                const positionParams = {
                    cols: colNum,
                    margin,
                    maxRows,
                    rowHeight,
                    containerWidth: width,
                    containerPadding: containerPadding || margin
                };
                const calculatedPosition = calcXY(
                    positionParams,
                    layerY,
                    layerX,
                    finalDroppingItem.w,
                    finalDroppingItem.h
                );

                this.droppingDOMNode = finalDroppingItem.i;
                this.droppingPosition = droppingPosition;

                layout = [
                    ...layout,
                    {
                        ...finalDroppingItem,
                        x: -1,
                        y: -1,
                        static: false,
                        isDraggable: true
                    }
                ];

                this.layoutData = moveElement(
                    layout,
                    layout[layout.length - 1],
                    calculatedPosition.x,
                    calculatedPosition.y,
                    this.isUserAction,
                    this.preventCollision,
                    compactType(this),
                    this.colNum,
                    this.allowOverlap
                );

                this.$forceUpdate();
                this.$nextTick(() => {
                    this.$refs.droppingItem?.moveDroppingItem(droppingPosition);
                });
            } else if (this.droppingPosition) {
                const { left, top } = this.droppingPosition;
                const shouldUpdatePosition = left != layerX || top != layerY;

                if (shouldUpdatePosition) {
                    this.droppingPosition = droppingPosition;
                    this.$forceUpdate();
                    this.$nextTick(() => {
                        this.$refs.droppingItem?.moveDroppingItem(droppingPosition);
                    })
                }
            }
        },

        removeDroppingPlaceholder() {
            const { droppingItem, colNum: cols, allowOverlap } = this;
            const { layoutData: layout } = this;

            const newLayout = compact(
                layout.filter(l => l.i !== droppingItem.i),
                compactType(this),
                cols,
                this.allowOverlap,
                this.cMaxRowsPerPage
            );

            this.layoutData = newLayout;
            this.droppingDOMNode = null;
            this.activeDrag = null;
            this.showActiveDrag = false;
            this.droppingPosition = null;
            this.$refs.droppingItem?.moveDroppingItem();
        },

        /**
         * Calculates a pixel value for the container.
         * @return {String} Container height in pixels.
         */
        containerHeight() {
            if (!this.autoSize) return;
            const { margin, cRowHeight: rowHeight, containerPadding, layoutData } = this;
            const nbRow = bottom(layoutData);
            const containerPaddingY = containerPadding
                ? containerPadding[1]
                : margin[1];

            return (
                nbRow * rowHeight +
                (nbRow - 1) * margin[1] +
                containerPaddingY * 2
            )
        },

        onResizeStart(i, w, h, options) {
            const { layoutData: layout } = this;
            const l = getLayoutItem(layout, i);
            if (!l) return;

            this.oldResizeItem = cloneLayoutItem(l);
            this.oldLayoutData = this.layoutData;
            this.resizing = true;

            this.handleResizeStart?.(layout, l, l);
        },

        onResize(i, w, h, { e, node, size, handle }) {
            const { oldResizeItem } = this;
            const { layoutData: layout } = this;
            const { colNum: cols, preventCollision, allowOverlap } = this;

            let shouldMoveItem = false;
            let finalLayout;
            let x;
            let y;

            const [newLayout, l] = withLayoutItem(layout, i, l => {
                let hasCollisions;
                x = l.x;
                y = l.y;

                if (["bl", "ml", "tl", "tm", "tr"].indexOf(handle) !== -1) {
                    if (["bl", "tl", "ml"].indexOf(handle) !== -1) {
                        x = l.x + (l.w - w);
                        w = l.x !== x && x < 0 ? l.w : w;
                        x = x < 0 ? 0 : x;
                    }

                    if (["tr", "tm", "tl"].indexOf(handle) !== -1) {
                        y = l.y + (l.h - h);
                        h = l.y !== y && y < 0 ? l.h : h;
                        y = y < 0 ? 0 : y;
                    }

                    shouldMoveItem = true;
                }

                // Something like quad tree should be used
                // to find collisions faster
                if (preventCollision && !allowOverlap) {
                    const collisions = getAllCollisions(layout, {
                        ...l,
                        w,
                        h,
                        x,
                        y
                    }).filter(layoutItem => layoutItem.i !== l.i);
                    hasCollisions = collisions.length > 0;

                    // If we're colliding, we need adjust the placeholder.
                    if (hasCollisions) {
                        // Reset layoutItem dimensions if there were collisions
                        y = l.y;
                        h = l.h;
                        x = l.x;
                        w = l.w;
                        shouldMoveItem = false;
                    }
                }

                l.w = w;
                l.h = h;

                return l;
            });

            if (!l) return;

            finalLayout = newLayout;
            if (shouldMoveItem) {
                // Move the element to the new position.
                const isUserAction = true;
                finalLayout = moveElement(
                    newLayout,
                    l,
                    x,
                    y,
                    isUserAction,
                    this.preventCollision,
                    compactType(this),
                    cols,
                    allowOverlap
                );
            }

            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                static: true,
                i: i
            };

            this.handleResize?.(finalLayout, oldResizeItem, l, placeholder);
            this.layoutData = allowOverlap
                ? finalLayout
                : compact(finalLayout, compactType(this), cols, false, this.cMaxRowsPerPage);
            this.activeDrag = placeholder;
            this.showActiveDrag = true;

            document.dispatchEvent(new CustomEvent('BEECRAFT_GRIDLAYOUT_RESIZING', {
                bubbles: true,
                cancelable: true,
                detail: {
                    id: this._uid
                }
            }));
        },

        onResizeStop(i, w, h, options) {
            const { layoutData: layout, oldResizeItem } = this;
            const { colNum: cols, allowOverlap } = this;
            const l = getLayoutItem(layout, i);

            const newLayout = allowOverlap
                ? layout
                : compact(layout, compactType(this), cols, false, this.cMaxRowsPerPage);

            this.handleResizeStop?.(newLayout, oldResizeItem, l);

            const { oldLayoutData: oldLayout } = this;

            oldLayout.forEach((item, index) => {
                if (item.h !== layout[index].h) {
                    delete layout[index].oh;
                }
            });

            this.activeDrag = null;
            this.showActiveDrag = false;
            this.oldResizeItem = null;
            this.oldLayoutData = null;
            this.resizing = false;
            this.onLayoutMaybeChanged(newLayout, oldLayout);
        }
    },

    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        if (this.enableDoubleTapControl) {
            this.$el.removeEventListener('dblclick', this.onDBLClick);
        }
    }
}
</script>
<style lang="less" scoped>
.grid-layout {
    position: relative;
    // width: 100% !important;
    // height: 100% !important;
    // border: 1px solid blue;
    min-height: 100%;

    // overflow-x: hidden;
    // overflow-y: hidden;
    .button-group {
        position: absolute;
        bottom: 48px;
        left: 0;
        right: 0;
        width: 144px;
        margin: 0 auto;
        border-radius: 35px;
        box-sizing: border-box;
        z-index: 999;
        height: 44px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        transition: all 1s ease-in-out;
        opacity: 0;
        margin-top: 4px;

        .button:before {
            color: var(--before-color, #fff);
        }

        // &--dark {
        //     background-color: rgba(0, 0, 0, 0.48);

        //     .button:before {
        //         color: #fff;
        //     }
        // }

        // &--light {
        //     background-color: rgba(255, 255, 255, 0.48);

        //     .button:before {
        //         color: #fff;
        //     }
        // }

        &--visible {
            opacity: 1;
            transition: unset;
        }
    }

    .button {
        margin: 0;
        cursor: pointer;
        font-size: 16px;
    }

    .button:nth-child(2) {
        font-size: 20px;
    }

    /deep/.el-carousel__arrow {
        display: none;
    }

    /deep/.el-carousel__indicators {
        display: none;
    }

    .grid-layout-mask {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        z-index: 999;
    }
}

.grid-placeholder {
    background: red;
    opacity: 0.2;
    transition-duration: 100ms;
    z-index: 2 !important;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
</style>