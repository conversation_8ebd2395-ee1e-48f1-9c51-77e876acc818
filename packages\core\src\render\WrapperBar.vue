<script>
import { getDOMInfo, hasVerticalScrollbar, getScrollbarWidth } from '../shared/getDOMInfo';

function findScrollParent(element) {
    if (!element) return null;
    // element = element;
    const style = window.getComputedStyle(element);
    const overflowValues = (style.overflow || '').split(' ');
    if (['scroll', 'auto'].indexOf(overflowValues[overflowValues.length - 1]) > -1) {
        if (style.position === 'relative') {
            return element;
        }
    }
    return findScrollParent(element.parentNode);
}

export default {
    props: ['target', 'useInternalNode', 'useInternalEditor'],

    data() {
        return {
            hydrationTimestamp: +new Date()
        }
    },

    render(creaeElement) {
        const { isHover, isActive, isDragged, id } = this.useInternalNode((node) => {
            return {
                isHover: node.events.hovered,
                isActive: node.events.selected,
                isDragged: node.events.dragged,
            }
        });

        if (isDragged || (!isHover && !isActive)) {
            return '';
        }

        const { hydrationTimestamp } = this;

        const { dom, displayName, nodeBars = [], isDeletable } = this.useInternalNode((node) => ({
            dom: node.dom,
            displayName: node.displayName || node.name,
            nodeBars: node.related?.wrapperBars ?? [],
            isDeletable: node.$$data?.isDeletable
        }));

        let wrapperBars = [
            ...nodeBars
        ];

        if (isDeletable !== false) {
            wrapperBars.unshift({
                name: 'delete',
                data: {
                    icon: 'fx-icon-process-delete',
                    description: $t("删除"),
                }
            },)
        }

        const btns = wrapperBars.map(item => {
            return creaeElement('fx-tooltip', {
                props: {
                    effect: 'dark',
                    content: item.data?.description,
                    placement: 'top'
                }
            }, [
                creaeElement('span', {
                    class: `icon cursor-pointer ${item.data?.icon}`,
                    on: {
                        'click': () => {
                            this.handleAction(item);
                        }
                    }
                })
            ])
        })


        return creaeElement('div', {
            class: 'bc-node-wrapper-tools position-absolute d-flex align-items-center',
            style: this.getPos(dom),
            attrs: {
                'data-relate-id': id
            }
        }, [
            displayName ? creaeElement('h2', displayName) : '',
            ...btns
        ]);
    },

    created() {
        this.targetDom = this.target;
        this.updatePosition = this.updatePosition.bind(this);
    },

    beforeUpdate() {
        this.init();
    },

    methods: {
        init() {
            if (!this.$frame && this.$el) {
                this.$frame = findScrollParent(this.targetDom);
                if (this.$frame) {
                    this.$frame.appendChild(this.$el);
                    this.$frame.addEventListener('scroll', this.updatePosition);
                    this.updatePosition();
                }
            }
        },
        getPos(dom) {
            if (!dom || !this.$frame) {
                return {
                    display: 'none'
                }
            }
            const domInfo = getDOMInfo(dom);
            const rootDomInfo = getDOMInfo(this.$frame);
            return {
                top: `${domInfo.top - rootDomInfo.top + this.$frame.scrollTop}px`,
                right: `${rootDomInfo.right - domInfo.right - (hasVerticalScrollbar(this.$frame) ? getScrollbarWidth() : 0)}px`,
                'margin-top': domInfo.top > rootDomInfo.top + 20 ? '-19px' : '0'
            }
        },

        updatePosition() {
            this.hydrationTimestamp = +new Date();
        },

        handleDelete() {
            const { actions, query } = this.useInternalEditor();
            const { id } = this.useInternalNode();

            const nodeHelper = query.node(id);

            const result = nodeHelper.callHook('beforeDelete', nodeHelper.get(), { actions, query });

            if (result instanceof Promise) {
                result.then(() => {
                    actions.delete([id]);
                })
            } else if (result !== false) {
                actions.delete([id]);
            }
        },

        handleCopy() {
            const { actions } = this.useInternalEditor();
            const { id } = this.useInternalNode();

            console.log('执行复制：', id);

            // const nodeTree = actions.parseSerializedNodes([template])[0];
        },

        handleAction({ name, onClick }) {
            if (name === 'delete') {
                this.handleDelete();
            } else if (name === 'copy') {
                this.handleCopy();
            } else if (onClick) {
                const { query, actions } = this.useInternalEditor();
                const { id } = this.useInternalNode();

                onClick({ id, query, actions });
            }
        },

        // _interceptAction(name, callback) {
        //     const { actions, query } = this.useInternalEditor();
        //     const { id } = this.useInternalNode();

        //     const nodeHelper = query.node(id);

        //     const result = nodeHelper.callHook('beforeDelete', nodeHelper.get(), { actions, query });

        //     if (result instanceof Promise) {
        //         result.then(() => {
        //             callback && callback();
        //         })
        //     } else if (result !== false) {
        //         callback && callback();
        //     }
        // }
    },

    beforeDestroy() {
        if (this.$frame) {
            this.$frame.removeEventListener('scroll', this.updatePosition);
        }
        this.$el.remove();
        this.rootDom = this.targetDom = this.$frame = null;
    }
}
</script>
<style lang="less" scoped>
h2,
span {
    // line-height: 12px;
    margin: 0 4px;
    font-size: 12px;
}

.bc-node-wrapper-tools {
    height: 20px;
    background-color: var(--color-primary06);
    // margin-top: -19px;
    box-sizing: border-box;
    z-index: 200;
    color: var(--color-primary01);
    padding: 0 5px;
    width: fit-content;
}

.icon:before {
    color: var(--color-primary01);
}
</style>