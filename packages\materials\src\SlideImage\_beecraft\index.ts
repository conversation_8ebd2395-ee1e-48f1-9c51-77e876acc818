import ImageSetters from './setters/image-setter.vue';

export default function() {
    return {
        type: 'SlideImage',
        displayName: $t('轮播图'),
        data: {
            autoplay: true,
            items: [],
            direction: 'horizontal',
            type: 'normal',
            maxCount: 10,
            height: 450
        },
        $$data: {
            noWrapper: true, // 不需要包装器
            layer: {
                w: 6,
                h: 6
            },
            style: {
                padding: 0
            }
        },
        related: {
            attributeSettings: [{
                name: 'SetterField',
                data: {
                    label: $t('beecraft.materials.carouselItems', {}, '轮播项'),
                    display: 'block',
                    helpText: $t('beecraft.materials.carouselMaxCount', {maxCount: '10'}, '最多设置{{maxCount}}张'),
                    setter: {
                        component: ImageSetters,
                        ranges: ['data', 'items']
                    }
                }
            },
            // {
            //     name: 'SetterField',
            //     data: {
            //         label: $t('方向'),
            //         display: 'block',
            //         setter: {
            //             component: 'RadioSetter',
            //             ranges: ['data', 'direction'],
            //             data: {
            //                 options: [{
            //                     value: 'horizontal',
            //                     label: '水平'
            //                 },{
            //                     value: 'vertical',
            //                     label: '垂直'
            //                 }]
            //             }
            //         }
            //     }
            // },
            {
                name: 'SetterField',
                data: {
                    label: $t('beecraft.common.form',{},'形态'),
                    display: 'block',
                    setter: {
                        component: 'RadioSetter',
                        ranges: ['data', 'type'],
                        data: {
                            options: [{
                                value: 'normal',
                                label: $t('普通'),
                            },{
                                value: 'card',
                                label: $t('卡片')
                            }]
                        }
                    }
                }
            }]
        }
    }
}