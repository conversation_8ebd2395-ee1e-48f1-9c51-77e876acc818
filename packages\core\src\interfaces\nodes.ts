export type NodeId = string;

export type NodeEventTypes = 'selected' | 'dragged' | 'hovered';

export type Nodedata = {
    [key: string]: any;
}

// todo any
export type NodeRules = any;

// todo any
export type NodeHooks = any;

export type SerializedNode = {
    id?: NodeId,
    name: string;
    data?: Nodedata;
    $$data?: Nodedata;
    children?: SerializedNode[];
    rules?: NodeRules;
    hooks?: NodeHooks;
    source?: string;
    extra?: Record<string, any>;
};

export type NodeData = Omit<
    Node,
    'displayName' | 'related' | 'events' | 'dom' | '_hydrationTimestamp'
> ;

export type Node = {
    id: NodeId;
    _hydrationTimestamp: number;
    _done: boolean;
    events: Record<NodeEventTypes, boolean>;
    name: string;
    displayName: string;
    type?: any;
    source?: string;
    extra?: Record<string, any>;
    data: Nodedata;
    $$data: Nodedata;
    children: NodeId[];
    rules: NodeRules;
    related: Record<string, any>;
    dom: Nullable<HTMLElement>;
    parent: NodeId;
    hooks: NodeHooks;
    toJSON?: (data: any, node: Node) => Promise<SerializedNode>;
};


export type Nodes = Node[];

export type NodeMap = Record<string, Node>;

export type NodeSelectorWrapper = {
    node: Node;
    exists: boolean;
};

export interface NodeTree {
    rootNodeId: NodeId;
    nodes: NodeMap;
}

export type NodeObjSelector = Nodes | Node;

export type NodeIdSelector = NodeId[];

export enum NodeSelectorType {
    Any,
    Id,
    Obj
}


type SerializeNodeSelector = Node | Node[];

export type NodeSelector<
    T extends NodeSelectorType = NodeSelectorType.Any
> = T extends NodeSelectorType.Id
    ? NodeIdSelector
    : T extends NodeSelectorType.Obj
    ? SerializeNodeSelector
    : NodeIdSelector | SerializeNodeSelector;


export interface INode {
    
}