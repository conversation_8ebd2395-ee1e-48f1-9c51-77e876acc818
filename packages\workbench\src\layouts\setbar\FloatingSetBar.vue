<template>
    <vue-draggable-resizable v-if="selectedNode && isShow" ref="vdr" class="vdr" :parent="true" v-bind="vdrOptions"
        :z="300" @dragstop="onDragStop" drag-cancel=".el-tabs__content" @hook:mounted="noticeReady">
        <div class="drag-content" ref="dragContent">
            <template v-if="isUnFold">
                <SetBar :style="cSetBarStyle"></SetBar>
                <div class="btn-group">
                    <fx-tooltip effect="dark" :content="$t('beecraft.btn.type_switch', '固定')" placement="top">
                        <fx-button class="btn-item btn-fix" size="small" plain icon="fx-icon-obj-app29" square
                        @click="_handleTypeSwitch"></fx-button>
                    </fx-tooltip>
                    <fx-tooltip effect="dark" :content="$t('关闭')" placement="top">
                        <fx-button class="btn-item btn-close" size="small" plain icon="fx-icon-close" square
                            @click="_handleToggle"></fx-button>
                    </fx-tooltip>
                </div>
            </template>
            <fx-tooltip effect="dark" :content="$t('展开')" placement="top" v-else>
                <fx-button class="btn-item btn-config" size="small" plain icon="fx-icon-configuration" square
                @click="_handleToggle"></fx-button>
            </fx-tooltip>
        </div>
    </vue-draggable-resizable>
</template>
<script>
import SetBar from './SetBar.vue';
import VueDraggableResizable from 'vue-draggable-resizable';
import { getCssVariable, observeDomRect } from '@beecraft/shared';
export default {
    components: {
        SetBar,
        VueDraggableResizable
    },

    inject: ['useInternalEditor'],
    data() {
        return {
            vdrOptions: {
                w: 40,
                h: 40,
                x: 0,
                y: 0
            },
            isUnFold: false,
            allowToggle: true,
            isShow: false
        }
    },
    computed: {
        cSetBarStyle() {
            const { style } = this.useInternalEditor((state) => {
                return {
                    style: state.options.workbench.setbar?.style
                }
            });
            return {
                height: 'unset',
                maxHeight: `${this.containerRect.height - 50 - this.uiBaseSize}px`,
                ...style
            };
        },

        // todo：目的是为了把selectedNodeId和updateBounds去做绑定
        selectedNode() {
            const { query } = this.useInternalEditor();
            const selectedNodeId = query.getEvent('selected').first();

            if (selectedNodeId) {
                // this.onNextTickRender(() => {
                //     if (this.isUnFold) {
                //         this.updateBounds();
                //     } else {
                //         this._handleToggle();
                //     }
                // })
                // TODO: Settimeout不优雅
                setTimeout(() => {
                    if (this.isUnFold) {
                        this.updateBounds();
                    } else {
                        this._handleToggle();
                    }
                }, 200)
            }

            return true;
        },
        // selectedNode: {
        //     get() {
        //         const { query } = this.useInternalEditor();
        //         return query.getEvent('selected').first();
        //     },
        //     set(value) {
        //         debugger;
        //     }
        // },

        dragContent() {
            return this.$refs.dragContent;
        }
    },
    mounted() {
        this.$container = this.$parent.$el.closest('.bc-workbench');
        const rect = this.containerRect = this.$container.getBoundingClientRect();
        this.uiBaseSize = getCssVariable(this.$container, 'bc-g-wb-size').slice(0, -2) * 1;
        this.vdrOptions.w = this.uiBaseSize;
        this.vdrOptions.h = this.uiBaseSize;
        this.vdrOptions.x = rect.width - 30 - this.uiBaseSize;
        this.vdrOptions.y = 8;

        setTimeout(() => {
            this.isShow = true;
            this.$nextTick(() => {
                this.syncWH();
                this.observe();
            })
        }, 100) //固定转浮层模式时，侧边栏总是晚于当前组件渲染，导致渲染时父容器的宽高非最终状态。延时100ms只是为了让宽度真实
    },
    methods: {
        onDragStop(left, top) {
            this.allowToggle = false;
            setTimeout(() => {
                this.allowToggle = true;
            }, 100);

            this.vdrOptions.x = left;
            this.vdrOptions.y = top;
        },

        _handleToggle() {
            if (this.allowToggle) {
                this.isUnFold = !this.isUnFold;
                this.updateBounds();
            }
        },

        _handleTypeSwitch() {
            const { actions } = this.useInternalEditor();
            actions.history.ignore().setOptions(options => {
                if (options.workbench.type === 'normal') {
                    options.workbench.type = 'floatingLayer';
                } else {
                    options.workbench.type = 'normal';
                }
            });
        },

        updateBounds() {
            this.prevVdrOptions = {
                ...this.prevVdrOptions,
                ...this.vdrOptions
            };
            this.$nextTick(() => {
                const containerRect = this.containerRect;
                const rect = this.dragContent.getBoundingClientRect();

                if (this.isUnFold) {
                    if (rect.x - rect.width < 0) {
                        this.vdrOptions.x = 0;
                    } else {
                        // this.vdrOptions.x -= (rect.width - this.prevVdrOptions.w + 10);
                        let offset = rect.width - this.prevVdrOptions.w;
                        if (offset) {
                            offset += 10;
                        }
                        this.vdrOptions.x -= offset;
                    }

                    if (rect.height + rect.y > containerRect.height) {
                        this.vdrOptions.y = containerRect.height - rect.height - 10 - this.uiBaseSize;
                    }
                } else {
                    setTimeout(() => {
                        this.vdrOptions.x += (this.prevVdrOptions.w - rect.width - 10);
                    });
                }
                this.syncWH();
            });
        },

        // 同步宽高给拖拽组件
        syncWH(callback) {
            this.$nextTick(() => {
                let rect = this.dragContent.getBoundingClientRect();
                this.vdrOptions.w = rect.width + 10;
                this.vdrOptions.h = rect.height + 10;
                this.$nextTick(callback);
            });
        },

        observe() {
            this.wObserver = observeDomRect(this.$container, 'width', (newValue, preValue, entry) => {
                this.containerRect.width = newValue;
                const offset = newValue - (preValue || newValue);
                this.vdrOptions.x += offset;
            });
            this.hObserver = observeDomRect(this.$container, 'height', (newValue, preValue, entry) => {
                this.containerRect.height = newValue;
                const offset = newValue - (preValue || newValue);
                this.vdrOptions.y += offset;
            });
        },

        onNextTickRender(callback) {
            if(this.$el) {
                callback();
            } else {
                if(!this._tasks) {
                    this._tasks = [];
                }
                this._tasks.push(callback);
            }
        },

        noticeReady() {
            if(this._tasks) {
                this._tasks.forEach(callback => callback());
                this._tasks = null;
            }
        }
    },

    beforeDestroy() {
        if (this.wObserver) {
            this.wObserver.disconnect();
        }
        if (this.hObserver) {
            this.hObserver.disconnect();
        }
    }
}
</script>
<style lang="less" scoped>
.btn-item {
    border: none;
    width: calc(var(--bc-g-wb-size, 40px) - 1px);
    height: calc(var(--bc-g-wb-size, 40px) - 1px);

    &+.btn-item {
        margin-left: 0;
    }
}

.drag-content {
    width: fit-content;
    position: relative;
    filter: drop-shadow(0 4px 12px #00000026);
    
    .bc-set-bar {
        border-left: none;
    }
    // box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
}

.btn-group {
    position: absolute;
    top: 5px;
    right: 5px;

    .btn-item {
        width: calc(var(--bc-g-wb-size, 40px) - 10px);
        height: calc(var(--bc-g-wb-size, 40px) - 10px);
    }
}

.btn-config {
    border-radius: 2px;
}
</style>