<template>
    <div class="bc-setter-layout">
        <div class="bc-setter-layout-all">
            <div class="bc-setter-layout-header">
                <div class="bc-setter-layout-header-item">
                    {{ cLabel }}
                    <fx-switch v-model="openEqually" size="mini" @change="handleCheckedChange"></fx-switch>
                </div>
                <div class="bc-setter-layout-header-item">
                    <fx-select v-model="dUnit" :options="cUnitsOptions" size="mini"></fx-select>
                </div>
            </div>
        </div>
        <div class="bc-setter-layout-short" v-if="openEqually">
            <fx-input size="mini" type="number" isPositiveNum :placeholder="$t('请输入')" :value="getValueWithoutUnit(normalizedValue[type])"
                :disabled="!openEqually" @change="onChange">
                <div class="pre-wrap" slot="prefix">
                    <span class="fx-icon-tuliweizhi5"></span>
                </div>
                <span class="el-input__icon" slot="suffix"> {{ dUnit }} </span>
            </fx-input>
        </div>
        <div class="bc-setter-layout-detail" v-else>
            <template v-for="item in cSides">
                <div class="bc-setter-layout-detail-item" :key="item.property">
                    <fx-input size="mini" type="number" isPositiveNum :value="item.value"
                        @change="onChange($event, item.property)">
                        <div class="pre-wrap" slot="prefix">
                            <span :class="item.icon"></span>
                        </div>
                        <span class="el-input__icon" slot="suffix"> {{ dUnit }} </span>
                    </fx-input>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
import { normalizeStyleProperty, propertyConfig, getCssPropertyWithDirection, fillAndUnitValue } from './util';

export default {
    props: {
        // margin, padding, borderWidth, borderRadius
        type: {
            type: String,
            default: 'margin',
        },

        value: {
            type: Object,
            default: () => ({}),
        },

        units: {
            type: Array,
            default: () => ['px']
        }
    },
    data() {
        return {
            dUnit: '',
            openEqually: true,
        }
    },
    computed: {
        cUnitsOptions() {
            return this.units.map(unit => ({
                value: unit,
                label: unit,
            }));
        },
        cLabel() {
            return propertyConfig[this.type].label || '';
        },
        cSides() {
            const { directionConfig, type, openEqually } = this;
            if(openEqually) {
                return [];
            }
            return directionConfig.positions.map((position, index) => {
                const property = getCssPropertyWithDirection(type, position);
                return {
                    property,
                    icon: directionConfig.icons[index],
                    value: this.getValueWithoutUnit(this.normalizedValue[property])
                }
            });
        }        
    },
    created() {
        const { type, units, value } = this;

        this.directionConfig = propertyConfig[type];
        this.normalizedValue = normalizeStyleProperty(value, type);
        this.openEqually = this.isOpenEqually(this.normalizedValue);
        this.dUnit = this.getUnitFromStyle(this.normalizedValue) || units[0];
        this.getCssProperty = getCssPropertyWithDirection;


        // 为什么需要记录原始属性？
        // 因为原有属性可能会被处理成undefiend，而当 openEqually 为 true 时，normalizedValue 会导致原油属性丢失，从而导致style中原有的属性不会被删除
        // 所以需要记录原始属性，以便第一次执行 triggerChange 时删除这些属性
        this.originalProperty = Object.keys(this.normalizedValue).reduce((memo, key) => {
            if(!this.normalizedValue[key]) {
                memo.push(key);
            }
            return memo;
        }, []);
    },
    methods: {
        // 没有支持单位不统一的情况
        getUnitFromStyle(style) {
            const value = Object.values(style).find(value => value !== undefined);
            return value ? value.match(/[a-zA-Z]+$/)[0] : '';
        },

        onChange(value, property = this.type) {
            if(!value) {
                this.normalizedValue[property] = '';
            }else {
                value = value * 1;
                if (value < 0) {
                    value = Math.abs(value);
                }

                this.normalizedValue[property] = `${value}${this.dUnit}`;
            }
            this.triggerChange();
        },

        getValueWithoutUnit(value) {
            if (value) {
                return value.replace(this.dUnit, '') * 1;
            }
            return value;
        },

        handleCheckedChange(value) {
            let { normalizedValue, type } = this;
            if(value) {
                normalizedValue = fillAndUnitValue(normalizedValue, type);
                this.normalizedValue = normalizeStyleProperty(normalizedValue, type);
            }else {
                this.normalizedValue = normalizeStyleProperty(normalizedValue, type, false);
            }

            this.openEqually = value;
            this.triggerChange();
        },

        triggerChange() {
            const { originalProperty, normalizedValue } = this;
            if(!this.isChanged) {
                originalProperty.forEach(key => {
                    if(!normalizedValue[key]) {
                        normalizedValue[key] = undefined;
                    }
                });
            }
            this.isChanged = true;
            this.$emit('change', normalizedValue);
        },

        isOpenEqually(value) {
            const values = Object.values(value);
            
            return !!value[this.type] || (values.length < 2  && values[0] === undefined);
        },

        isEmpty() {
            const { normalizedValue } = this;
            return !Object.values(normalizedValue).find(item => item || item === 0);
        }
    }
}
</script>

<style lang="less" scoped>
.bc-setter-layout {
    display: flex;
    width: 100%;
    align-items: center;
    flex-direction: column;
    gap: 8px;

    .bc-setter-layout-all {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .bc-setter-layout-header {
            display: flex;
            width: 100%;
            justify-content: space-between;
            align-items: anchor-center;

            /deep/.el-select {
                width: 60px;
            }
        }
    }

    .bc-setter-layout-short {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 100%;

    }

    .bc-setter-layout-detail {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, auto);
        gap: 8px;
    }

    .bc-setter-layout-detail-item {
        display: flex;
        flex-direction: column;
    }

    .pre-wrap {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;

        span {
            font-size: 14px;

            :before {
                color: #737C8C;
            }
        }
    }
}
</style>
