# BeeCraftJs

纷享销客低代码框架

## ✨ Features

## 🌰 Usage example

## 目录结构

```
beecraft
│
├─ examples
│  └─ src
│     ├─ activitybar-sample 活动栏扩展示例
│     ├─ grid-layout-sample 栅格布局示例
│     └─ ......
│
└─ packages
   ├─ core
   │  ├─ editor
   │  │  ├─ actions.ts 操作API
   │  │  ├─ query.ts 查询API
   │  │  ├─ store.ts 设计器上下文
   │  │  ├─ NodeHelper.ts 节点辅助函数
   │  │  ├─ EventHelper.ts 事件辅助函数
   │  │  └─ Editor.vue 设计器组件
   │  ├─ events
   │  │  ├─ ConnectorRegistry.ts 连接器注册表
   │  │  ├─ createShadow.ts (暂未使用)
   │  │  ├─ EventHandlers.ts 事件注册器
   │  │  ├─ findPosition.ts 位置查询函数
   │  │  ├─ isEventBlockedByDescendant.ts 事件阻断函数
   │  │  ├─ Positioner.ts 定位器
   │  │  └─ RenderEditorIndicator.vue 指针组件
   │  ├─ nodes
   │  │  ├─ createNode.ts 创建节点函数
   │  │  ├─ createNodeFromVNode.ts (暂未使用)
   │  │  ├─ deserializeNode.ts 序列化节点函数
   │  │  ├─ Blueprint.vue 蓝图组件
   │  │  ├─ NodeContext.vue 节点上下文
   │  │  └─ NodeElement.vue 节点渲染元素组件
   │  ├─ render
   │  │  ├─ DefaultRender.vue 节点渲染真实组件
   │  │  ├─ Frame.vue 渲染器组件
   │  │  ├─ RenderNode.vue 渲染节点组件
   │  │  ├─ RenderWrapper.vue 组件包装器组件
   │  │  └─ WrapperBar.vue 包装器工具组件
   │  ├─ shared
   │  │  ├─ getDOMInfo.ts 获取DOM位置/大小数据函数
   │  │  ├─ History.ts 历史记录模块
   │  │  ├─ Hotkey.ts 快捷键模块
   │  │  └─ PluginService.ts 插件引擎模块
   │  ├─ interfaces
   │  │  ├─ editor.ts 设计器类型
   │  │  ├─ event.ts 事件类型
   │  │  └─ nodes.ts 节点类型
   ├─ engine
   │  ├─ engine.ts 初始化API
   │  ├─ plugins 内置插件
   │  │  ├─ plugin-builtin-editor-init 设计器初始化插件
   │  │  ├─ plugin-builtin-hotkey 快捷键插件
   │  │  ├─ plugin-builtin-setbar-init 设置器初始化插件
   │  │  └─ plugin-builtin-workbench-init 工作台初始化插件
   ├─ workbench
   │  ├─ Workbench.vue 工作台组件
   │  ├─ layouts
   │  │  ├─ ActivityBar.vue 活动栏组件
   │  │  ├─ MainArea.vue 主区域组件
   │  │  ├─ SetBar.vue 设置栏组件
   │  │  └─ ToolBar.vue 工具栏组件
   ├─ fxui fxui物料
   ├─ materials 内置物料
   ├─ setters 设置器
   └─ shared 公用函数

```

## 💻 本地调试

```
git clone https://git.firstshare.cn/fe-paas/beecraft.git
cd beecraft
npm run setup
npm run start:examples
```

启动项目后，进入纷享销客任意账号，控制台输入：

```
const script = document.createElement('script');

script.src = 'http://localhost:3000/src/index';
script.setAttribute('type', 'module');
document.body.appendChild(script);
```

### 新建子项目

1. packages 中新建子项目文件夹
2. 进入文件夹，执行 `pnpm init`
3. 如果子项目依赖其他项目，执行 `pnpm add 被依赖的子包 -F 子包`

## 📚 代码部署

### 第一步 代码构建

代码构建会同时生成es规范和umd规范的代码

```
pnpm run build
```

### 第二步 发布仓库

提交变更
```
npx changeset
```

更新版本
```
npx changeset version
```

发布版本
```
npx changeset publish
```

## examples

### 如何测试示例


## Q&A

### Q: 如何解决自动刷新页面的问题

A: 打开 **\node_modules\vite\src\client\client.ts** 文件，搜索 **server connection lost. polling for restart** 关键字，屏蔽当前回调中的 **location.reload()**。


### Q: local version has not been published on npm ？

A: 这是因为有其人发布后未提交代码，导致版本不同步的问题。在确定代码同步的情况下，可以再次执行 `npx changeset version` 命令，然后执行 `npx changeset publish` 命令进行发布，直至没有错误为止。
