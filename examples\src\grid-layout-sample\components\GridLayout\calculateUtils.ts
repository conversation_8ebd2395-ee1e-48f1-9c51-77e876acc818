export function calcGridColWidth(positionParams) {
    const { margin, containerPadding, containerWidth, cols } = positionParams;

    return (
        (containerWidth - margin[0] * (cols - 1) - containerPadding[0] * 2) / cols
    );
}

// 根据真实坐标计算x/y
export function calcXY(
    positionParams,
    top,
    left,
    w,
    h
) {
    const { margin, cols, rowHeight, maxRows } = positionParams;
    const colWidth = calcGridColWidth(positionParams);

    let x = Math.round((left - margin[0]) / (colWidth + margin[0]));
    let y = Math.round((top - margin[1]) / (rowHeight + margin[1]));

    // Capping
    x = clamp(x, 0, cols - w);
    y = clamp(y, 0, maxRows - h);

    return { x, y };
}

export function clamp(
    num,
    lowerBound,
    upperBound
) {
    return Math.max(Math.min(num, upperBound), lowerBound);
}

export function calcGridItemWHPx(
    gridUnits,
    colOrRowSize,
    marginPx
) {
    // 0 * Infinity === NaN, which causes problems with resize contraints
    if (!Number.isFinite(gridUnits)) return gridUnits;
    return Math.round(
        colOrRowSize * gridUnits + Math.max(0, gridUnits - 1) * marginPx
    );
}

// 根据x/y计算真实坐标
export function calcGridItemPosition(
    positionParams,
    x,
    y,
    w,
    h,
    state
) {
    const { margin, containerPadding, rowHeight } = positionParams;
    const colWidth = calcGridColWidth(positionParams);
    const out: any = {};

    // If resizing, use the exact width and height as returned from resizing callbacks.
    if (state && state.resizing) {
        out.width = Math.round(state.resizing.width);
        out.height = Math.round(state.resizing.height);
    }
    // Otherwise, calculate from grid units.
    else {
        out.width = calcGridItemWHPx(w, colWidth, margin[0]);
        out.height = calcGridItemWHPx(h, rowHeight, margin[1]);
    }

    // If dragging, use the exact width and height as returned from dragging callbacks.
    if (state && state.dragging) {
        out.top = Math.round(state.dragging.top);
        out.left = Math.round(state.dragging.left);
    } else if (
        state &&
        state.resizing &&
        typeof state.resizing.top === "number" &&
        typeof state.resizing.left === "number"
    ) {
        out.top = Math.round(state.resizing.top);
        out.left = Math.round(state.resizing.left);
    }
    // Otherwise, calculate from grid units.
    else {
        out.top = Math.round((rowHeight + margin[1]) * y + containerPadding[1]);
        out.left = Math.round((colWidth + margin[0]) * x + containerPadding[0]);
    }

    return out;
}


export function calcWH(positionParams, width, height, x, y, handle) {
    const { margin, maxRows, cols, rowHeight } = positionParams;
    const colWidth = calcGridColWidth(positionParams);

    let w = Math.round((width + margin[0]) / (colWidth + margin[0]));
    let h = Math.round((height + margin[1]) / (rowHeight + margin[1]));

    let _w = clamp(w, 0, cols - x);
    let _h = clamp(h, 0, maxRows - y);
    if (["bl", "ml", "tl"].indexOf(handle) !== -1) {
        _w = clamp(w, 0, cols);
    }
    if (["tl", "tm", "tr"].indexOf(handle) !== -1) {
        _h = clamp(h, 0, maxRows);
    }
    return { w: _w, h: _h };
}