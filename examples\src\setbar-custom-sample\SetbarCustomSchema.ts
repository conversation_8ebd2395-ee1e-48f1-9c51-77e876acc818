export default class SetbarCustomSchema {
    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore(service, { config }) {
        // 通过插件配置schema的方式来组件设置器
        config.add('workbench.setbar.items', {
            label: '组件设置器[schema]',
            name: 'customSchemaNode',
            type: 'node'
        });

        // 通过插件配置组件的方式来组件设置器
        config.add('workbench.setbar.items', {
            label: '组件设置器[component]',
            name: 'customComponentNode',
            type: 'node'
        });

        // 通过插件配置schema的方式来自定义设置器
        config.add('workbench.setbar.items', {
            label: '非组件设置器[schema][plugin]',
            name: 'customSchema',
            related: [{
                name: 'SetterField',
                data: {
                    label: '测试1',
                    display: 'block',
                },
                children: [
                    {
                        name: 'SetterField',
                        data: {
                            label: '单行文本',
                            display: 'block',
                            setter: {
                                component: 'StringSetter',
                                ranges: ['options', 'text']
                            }
                        }
                    }
                ]
            }]
        });

        // 通过插件配置组件的方式来自定义设置器
        config.add('workbench.setbar.items', {
            label: '非组件设置器[component][plugin]',
            name: 'customComponent',
            related: {
                render: h => h('div', 'customComponent')
            }
        });

        // 通过options配置schema的方式来自定义设置器
        config.add('workbench.setbar.items', {
            label: '非组件设置器[schema][options]',
            name: 'customSchemaOptions'
        });

        // 通过options配置组件的方式来自定义设置器
        config.add('workbench.setbar.items', {
            label: '非组件设置器[component][options]',
            name: 'customComponentOptions'
        });

        // 设置设置器面板默认值
        config.set('workbench.setbar.activeTab', 'attribute');
    }
}