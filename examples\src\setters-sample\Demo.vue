<template>
    <div class="picture-component">
        <h1>低代码PaaS平台能力全景图</h1>
        <div>
            <img src="https://www.fxiaoke.com/ap/wp-content/uploads/2021/09/<EMAIL>" alt="picture-component" />
        </div>
        <div>{{ radio }}</div>
        <div>{{ text }}</div>
        <div>{{ src }}</div>
        <div>{{ height }}</div>
        <div>{{ size }}</div>
        <div>{{ position }}</div>
        <div @click="handleClick">点我</div>
    </div>
</template>
<script>
import setterConfig from './setter-config';
export default {
    inject: ['useInternalNode'],
    props: {
        text: String,
        src: String,
        height: String,
        size: String,
        position: String,
    },
    beecraft: function () {
        return {
            data: {
                text: 'Demo',
                src: 'https://www.fxiaoke.com/ap/wp-content/uploads/2021/09/<EMAIL>',
                size: 'contain',
                position: 'right top',
                radio: 'normal'
            },
            $$data: {
                style: {
                    'height': '600px',
                    'margin-top': 0,
                    'padding': '10px 20px 10px 30px',
                    'border-width': '10px 10px 10px 10px',
                    'border-radius': '5px 2px',
                    'color': '#409EFF'
                },
                noWrapper: true,
            },
            related: {
                attributeSettings: setterConfig
            },
            extra: {
                sourceTypeAndPropMap: {
                    'cms': [
                        'data.src',
                    ],
                    'i18n': [
                        'data.title'
                    ]
                }
            }
        }
    },
    watch: {
        text: {
            handler(val) {
                console.log(val);
            }
        }
    },
    methods: {
        handleClick() {
            const { actions } = this.useInternalNode();
            actions.setCustom((data) => {
                data.text = '点击了';
            });
        }
    }
}
</script>
<style lang="less" scoped>
h1 {
    text-align: center;
}

.picture-component {
    width: 100%;
    box-sizing: border-box;

    img {
        width: 100%;
    }
}
</style>
