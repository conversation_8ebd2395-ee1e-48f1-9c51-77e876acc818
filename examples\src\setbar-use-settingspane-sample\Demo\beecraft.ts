// 创建节点时用的模板
const template = {
    name: 'Demo'
}

const settings = [{
    name: 'SetterField',
    data: {
        label: '测试1',
        display: 'block'
    },
    children: [{
        name: 'SetterField',
        data: {
            label: '测试1',
            display: 'block'
        },
        children: [{
            id: 'text',
            name: 'Setter<PERSON>ield',
            data: {
                label: '单行文本',
                display: 'block',
                setter: {
                    component: 'StringSetter',
                    ranges: ['data', 'text']
                }
            }
        }]
    },{
        id: 'text1',
        name: 'SetterField',
        data: {
            label: '单行文本',
            display: 'block',
            setter: {
                component: 'StringSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        id: 'longtext',
        name: 'Setter<PERSON>ield',
        data: {
            label: '多行文本',
            setter: {
                component: 'TextareaSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        id: 'select',
        name: 'SetterField',
        data: {
            label: '选择器',
            setter: {
                component: 'SelectSetter',
                ranges: ['data', 'text'],
                data: {
                    options: [{
                        value: '选项1',
                        label: '黄金糕'
                    }, {
                        value: '选项2',
                        label: '双皮奶'
                    }]
                }
            }
        }
    },{
        id: 'switch',
        name: 'SetterField',
        data: {
            label: '开关',
            setter: {
                component: 'SwitchSetter',
                ranges: ['data', 'text']
            }
        }
    },{
        id: 'custom',
        name: 'SetterField',
        data: {
            label: '自定义',
            setter: {
                component: 'SwitchSetter',
                ranges: ['data', 'text'],
                resource: {
                    render: (h: any) => h('div', 2333)
                }
            }
        }
    }]
}];

export default function(){
    return {
        type: 'Demo',
        name: $t('测试'),
        data: {
            text: '',
            address: {
                name: 23333
            }
        },
        $$data: {
            isCanvas: true,
            template,
            title: $t('示例组件'),
            setbar: {
                default: 'style'
            }
        },
        related: {
            styleSettings: settings,
            attributeSettings: settings,
            settings: settings
        }
    }
}