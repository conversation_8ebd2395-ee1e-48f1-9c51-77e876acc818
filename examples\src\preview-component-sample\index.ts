import Vue from 'vue';
import App from './App.vue';
import Demo from './Demo.vue';

new Vue({
    render: h => h(App, {
        props: {
            resolver: {
                Demo
            },
            import: [{
                name: '<PERSON><PERSON>',
                children: [{
                    name: 'De<PERSON>',
                }]
            }],
            mode: 'preview'
        }
    })
}).$mount(document.getElementById('app-portal') as HTMLElement);