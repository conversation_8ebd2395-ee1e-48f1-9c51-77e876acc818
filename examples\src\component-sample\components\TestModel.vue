<script>
    export default {
        render(createElement) {
            return createElement('div', this.$slots.default);
        },
        beecraft() {
            return {
                $$data: {
                    isContext: true, //是否是上下文
                    isCanvas: true, //是否是容器
                    contextNode: {
                        data: {
                            text: 'TestModel'
                        }
                    }
                },
                rules: {
                    canDrag: () => false,
                    canHover: () => false,
                    canSelect: () => false,
                }
            }
        }
    }
</script>