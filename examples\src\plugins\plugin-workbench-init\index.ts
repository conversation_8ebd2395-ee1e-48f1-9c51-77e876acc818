/**
 * @desc 工作台初始化
 * 
 */

class WorkbenchInitPlugin{

    apply() {
        return [{
            event: 'beecraft.init.before',
            functional: this.initBefore.bind(this)
        }]
    }

    initBefore({ preData }, { actions, config }) {
        config.add('workbench.activityBar.activityArea', {
            name: 'test',
            data: {
                align: "top",
                icon: "fx-icon-obj-app11",
                description: "测试",
            },
            type: 'link',
            linkdata: {
                url: 'https://www.baidu.com'
            }
        });
        config.add('workbench.tools', {
            name: 'test',
            data: {
                align: "center",
                icon: "fx-icon-obj-app11",
                description: "测试"
            },
            onClick: (item, editor) => {
                alert('测试');
            }
        });
    }
}

export default WorkbenchInitPlugin;