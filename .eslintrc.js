const DOMGlobals = ['window', 'document'];
const NodeGlobals = ['module', 'require'];

module.exports = {
	parser: 'vue-eslint-parser',
	parserOptions: {
		sourceType: 'module',
		parser: '@typescript-eslint/parser',
	},
	rules: {
		'no-debugger': 'error',
		'no-unused-vars': [
			'error',
			// we are only using this rule to check for unused arguments since TS
			// catches unused variables but not args.
			{ varsIgnorePattern: '.*', args: 'none' },
		],
		// most of the codebase are expected to be env agnostic
		'no-restricted-globals': ['error', ...DOMGlobals, ...NodeGlobals],
		// since we target ES2015 for baseline support, we need to forbid object
		// rest spread usage (both assign and destructure)
		'no-restricted-syntax': [
			'error',
			'ObjectExpression > SpreadElement',
			'ObjectPattern > RestElement',
			'AwaitExpression'
		],
		'max-lines': [
			'error',
			{
				max: 500,
				skipBlankLines: true,
				skipComments: true,
			},
		],
	},
	overrides: [
		{
			files: ['packages/**'],
			rules: {
				'no-restricted-globals': 'off',
				'no-restricted-syntax': 'off',
				'no-non-null-asserted-optional-chain': 'off',
			},
		},
	],
};
